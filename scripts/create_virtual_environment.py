import logging
import subprocess
import platform

def _print(message):
	logging.info(message)

CURRENT_PLATFORM = platform.platform()
print("Current platform: %s" % CURRENT_PLATFORM)


def _get_architecture_prefix():

	if 'arm' in platform.machine():
		return 'arch -arm64 '
	else:
		return ''


def execute_command(cmd, print_it=True):
	if print_it:
		print(cmd)

	subprocess.check_call(cmd, shell=True)


def print_line(message):
	print()
	print("-------------------------------------------------------------")
	print(message.upper())
	print("-------------------------------------------------------------")


def _create_new_virtual_env():

	execute_command("cd %s/" % VIRTUAL_ENV_FOLDER)

	command = '%spython3.10 -m venv %s/%s' % (_get_architecture_prefix(), VIRTUAL_ENV_FOLDER, VIRTUAL_LOCATION_NAME)
	execute_command(command)


def _add_required_libraries():

	upgrade_command = '%s%s/%s/bin/pip3 install --upgrade pip' % (_get_architecture_prefix(), VIRTUAL_ENV_FOLDER, VIRTUAL_LOCATION_NAME)
	execute_command(upgrade_command)

	command = '%s%s/%s/bin/pip3 install -r %s/src/requirements.txt' % (_get_architecture_prefix(), VIRTUAL_ENV_FOLDER, VIRTUAL_LOCATION_NAME, VIRTUAL_ENV_FOLDER)
	execute_command(command)


#Change this to a different path if you want to create the virtual environment in a different location
VIRTUAL_ENV_FOLDER = '..'
VIRTUAL_LOCATION_NAME = 'venv'


if __name__ == '__main__':

	_create_new_virtual_env()
	_add_required_libraries()