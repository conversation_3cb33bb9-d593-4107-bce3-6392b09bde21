import logging
import subprocess

def _print(message):
	logging.info(message)


def execute_command(cmd, print_it=True):
	if print_it:
		print(cmd)

	subprocess.check_call(cmd, shell=True)


def print_line(message):
	print()
	print("-------------------------------------------------------------")
	print(message.upper())
	print("-------------------------------------------------------------")


def deploy_flex():
	# execute_command("cd ../src")

	# fmatheis, For the moment B2 is working fine, if we need more memory or performance we can consider working with Flexible
	command = 'cd ../src;gcloud config set app/cloud_build_timeout 20000;gcloud app deploy app.yaml --project call-seeker --promote --quiet'
	execute_command(command)


if __name__ == '__main__':
	deploy_flex()
