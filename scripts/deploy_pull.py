import datetime
import json
import os
import subprocess

import requests


def execute_command(cmd):
    print(cmd)
    subprocess.check_call(cmd, shell=True)


if __name__ == "__main__":
    project_name = "call-center-software"
    service = "v2"
    gcloud_project = "call-seeker"
    branch_bitbucked= "python310"
    submodule_branch_bitbucked = "master"
    print("Uploading project")
    user = os.getcwd().split("/")[2]
    path_tmp = "/Users/<USER>/tmp" % user

    if not os.path.exists(path_tmp):
        os.mkdir(path_tmp)
        print("tmp folder created")

    path_projects = "%s/projects" % path_tmp
    if not os.path.exists(path_projects):
        os.mkdir(path_projects)
        print("tmp projects folder created")

    current_date = datetime.datetime.now()
    gcloud_version = current_date.strftime("%Y%m%d%H%M%S")
    project_build = "%s_%s" % (project_name, current_date.strftime("%Y%m%d_%H%M%S"))
    project_build_path = "%s/%s" % (path_projects, project_build)
    execute_command("cd %s && git clone -b %s *****************:paraty/%s.git %s" % (path_projects, branch_bitbucked, project_name, project_build))
    execute_command(f"cd {project_build_path} && git submodule init && git submodule update && cd src/paraty_commons_3 && git checkout {submodule_branch_bitbucked}")

    # SLACK
    user = subprocess.check_output(["git", "config", "user.name"])
    user = user.decode().replace("\n", "")
    short_description = input("Short description: ")
    message = f"usuario:    {user}\ntarget project:     [{gcloud_project}]\ntarget service:     [{service}]\ntarget version:     [{gcloud_version}]\n{short_description}"
    data_to_send = {'message': message,
                    'chat_id': 'C06ALTFNJL8'}
    header = {"content-type": "application/json"}
    requests.post('https://europe-west1-web-seeker.cloudfunctions.net/paraty-slack-notify', headers=header,
                  data=json.dumps(data_to_send))

    update_command = f"gcloud config set app/cloud_build_timeout 30000s;gcloud app deploy app.yaml --project {gcloud_project} --no-promote --version={gcloud_version} -q"
    execute_command("cd %s/src && %s" % (project_build_path, update_command))

    os.system(f'open https://{gcloud_version}-dot-{service}-dot-{gcloud_project}.appspot.com/healthcheck')
    os.system('open https://docs.google.com/forms/d/e/1FAIpQLSf3nLeuAmSVSgzNem4gdJ5b4EgSkC38HP4x4pagLLvLWYSgkQ/viewform')
