.search_form_block {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  align-content: space-between;
  margin-top: 20px;
  gap: 20px;
  flex-wrap: wrap;

  .switches_wrapper{
    width: 100%;
    display: flex;
    align-items: center;
    gap: 40px;
    .switch_element_wrapper {
      position: relative;
      box-sizing: border-box;
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 16px;

      .label{
        font-size: 16px;
        letter-spacing: 0.45px;
      }

      .switch {
        position: relative;
        width: 68px;
        height: 33px;
        overflow: visible;
        border-radius: 100px;

        .checkbox {
          position: relative;
          width: 100%;
          height: 100%;
          padding: 0;
          margin: 0;
          opacity: 0;
          cursor: pointer;
          z-index: 3;

          &:checked:active + .knobs:before {
            margin-left: -26px;
          }

          &:checked + .knobs:before {
            content: "\f00c";
            right: 3px;
            background-color: #F28E2A;
          }

          &:checked ~ .layer {
            background-color: transparent;
          }
        }

        .knobs, .layer {
          position: absolute;
          top: 0;
          right: 0;
          bottom: 0;
          left: 0;
        }

        .knobs {
          z-index: 2;

          &:before {
            content: "\f00d";
            font-family: "Font Awesome 5 Pro";
            position: absolute;
            top: 3px;
            left: auto;
            right: 37px;
            width: 27px;
            height: 27px;
            box-sizing: border-box;
            color: white;
            background-color: #92714f;
            font-size: 14px;
            font-weight: bold;
            text-align: center;
            line-height: 0.7;
            padding: 11px 4px;
            border-radius: 50%;
            transition: 0.3s ease all, left 0.3s cubic-bezier(0.18, 0.89, 0.35, 1.15);
          }
        }

        .layer {
          width: 100%;
          background-color: rgba(#92714f, 0.2);
          border: 1px solid #F28E2A;
          transition: 0.3s ease all;
          z-index: 1;
          border-radius: 100px;
        }
      }
    }
  }
  .top_inputs_wrapper{
    width: 100%;
    display: flex;
    align-items: center;
    gap: 40px;
    position: relative;
    &:before {
      content: "";
      width: calc(100% - 325px);
      height: 1px;
      background: #E9E9F0;
      position: absolute;
      bottom: -80px;
      left: 15px;
    }
    .flight_hotel_selector_wrapper{
      font-size: 16px;
      margin-bottom: 24px;
      label{
        z-index: 1;
      }
      .select2.select2-container{
        .select2-selection{
          padding: 5px 8px 4px;
          border: none;
        }
        .select2-selection__arrow{
          display: none;
        }
      }
    }
  }
}

.comments_wrapper {
  position: relative;
  width: 100%;

  #comments {
    width: 100%;
    height: 75px;
    padding: 12px;
    margin-bottom: 10px;
  }

  .comments_label {
    top: -8px;
    left: 10px;
    position: absolute;
    z-index: 1;
    background-color: white;
    padding: 0px 5px;
  }
}

.rooms_data_block, .search_filters_block {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: stretch;
  flex: 1;
}

.rooms_data_block {
  .group_search {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: 15px;
    width: 100%;
    height: 40px;
    margin-bottom: 40px;

    .group_search_label {
      margin-bottom: 3px;
      font-weight: 600;
      font-size: 20px;
      letter-spacing: 0;
    }

    .group_search_checkbox_wrapper {
      .hidden_checkbox {
        display: none !important;
      }

      .yes_no_checkbox {
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        cursor: pointer;


        .yes_no_checkbox_option {
          display: flex;
          justify-content: center;
          align-items: center;
          min-width: 50px;
          height: 24px;
          border: 1px solid $grey_2;
          font-weight: 300;
          font-size: 14px;

          &:first-child {
            border-top-left-radius: 6px;
            border-bottom-left-radius: 6px;
          }

          &:last-child {
            border-top-right-radius: 6px;
            border-bottom-right-radius: 6px;
          }
        }
      }

      .hidden_checkbox:not(:checked) + .yes_no_checkbox {
        .yes_no_checkbox_option {
          &.yes {
            border-right: none;
          }

          &.no {
            border-color: $color_1;
            background-color: rgba($color_1, 0.1);
          }
        }
      }

      .hidden_checkbox:checked + .yes_no_checkbox {
        .yes_no_checkbox_option {
          &.yes {
            border-color: $color_1;
            background-color: rgba($color_1, 0.1);
          }

          &.no {
            border-left: none;
          }
        }
      }
    }
  }
}

.search_filters_block {
  .group_selector {
    width: 100%;

    .group_select {

    }
  }

  .user_params_wrapper {
    display: flex;
    width: 100%;
    height: 100%;
    gap: 15px;

    .input_wrapper {
      width: 100%;
      margin-bottom: 30px;
    }
  }
}

.buttons_block {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: stretch;
  width: 275px;
  gap: 25px;

  > * {
    width: 100%;
  }

  .submit_button {
    &.comments:not(.active) {
      display: none;
    }

    &.disabled {
      opacity: 0.3;
      cursor: auto;
      pointer-events: none;
    }
  }
}

.geolocation_selection, .language_selection {
  margin-bottom: 20px;
  margin-bottom: 20px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-content: center;
}

.user_dni_selection, .user_email_selection, .club_profile_selection {
  margin-bottom: 30px;
}

.with_club_profile_selection, .club_profile_selection {
  width: calc((100% - 20px) / 3);
}

.start_date,
.start_date_list,
.end_date,
.end_date_list {
  position: relative;
  width: calc((100% - 20px) / 2);

  &.active {
    label {
      top: 0;
      font-size: 12px;
    }
  }
}

.occupancy_selection {
  position: relative;
  z-index: 10;

  label {
    top: -15px !important;
    left: 10px;
    position: absolute;
    z-index: 1;
    background-color: white;
    padding: 5px 7px;
    transform: none;
    margin-left: 0;
    font-size: 14px !important;
  }

  .occupancy_display {
    padding: 8px 18px;
    display: block;
    background-color: white;
    font-size: 16px;
    cursor: pointer;
  }

  .occupancy_selector {
    position: absolute;
    top: 110%;
    left: 50%;
    transform: translate(-50%, 0%);
    width: 320px;
    background: white;
    padding: 15px;
    border: 1px solid #D7DAE2;
    box-shadow: 0 3px 15px #00000029;
    text-align: center;
    display: none;

    &.active {
      display: block;
    }

    &.with_pets {
      width: 390px;
    }
  }

  .selector_block {
    &.rooms {
      margin-bottom: 20px;
    }

    &:not(.rooms) {
      width: calc(100% / 2);
    }
  }

  .room_selection {
    border-bottom: 1px solid #D7DAE2;

    .flex_wrapper {
      display: flex;
      align-items: center;
    }

    &.room_1 {
      border-top: 1px solid #D7DAE2;
    }

    .selector_block:not(:last-child) {
      border-right: 1px solid #D7DAE2;
    }
  }

  .occupancy_selector {
    .label {
      font-size: 16px;
    }

    input {
      padding: 0;
      font-size: 18px;
    }
  }

  .room {
    > div {
      display: inline-block;
    }

    .babies_age_wrapper {
      margin-top: 5px;
    }

    .children_age_wrapper .child_age {
      margin-right: 40px;
    }

    .babies_age_wrapper i, .children_age_wrapper i {
      font-size: 14px;
    }

    .room_title {
      position: absolute;
      top: -10px;
      background: white;
      padding: 0 10px;
      margin: 0;
      width: auto;
    }

    label {
      color: black;
      font-size: 14px;
      margin-right: 10px;
      width: 100%;
      text-align: center;

      .ages_range {
        font-size: 10px;
      }
    }

    select {
      position: relative;
      padding: 5px 10px;
      border-radius: 4px;
      border: 1px solid #D7DAE2;
      background-color: white;
      box-shadow: 0 2px 3px rgba(0, 0, 0, 0.05);
    }
  }
}

.children_age_wrapper {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}

.child_age, .baby_age {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  font-size: 14px;
  align-items: center;
}

.child_age:first-child, .baby_age:first-child {
  margin-top: 10px;
}

.child_age:last-child, .baby_age:last-child {
  margin-bottom: 5px;
}

.occupancy_rooms .room {
  margin-bottom: 40px !important;
  position: relative;

  .room_row {
    border: 0.5px solid #D7DAE2;
    border-radius: 4px;
    padding: 15px 20px;
    box-shadow: 0 2px 3px rgba(0, 0, 0, 0.05);
  }

  .occupancy_selector {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 20px;
  }

  .room_row {
    display: flex !important;
    align-content: space-around;
    justify-content: flex-start;
    align-items: stretch;
    width: 100%;
  }
}

.child_age .child_button_age, .baby_age .baby_button_age {
  margin-left: 10px;
  display: flex;
}

.child_age .child_button_age input, .baby_age .baby_button_age input {
  margin: 2px;
  position: relative;
  bottom: 2px;
}
