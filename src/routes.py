from handlers.api.dev_stuff import <PERSON><PERSON>tu<PERSON><PERSON>and<PERSON>
from handlers.api.hotels_api import <PERSON>adInventory, HotelApi
from handlers.booking_widget.booking_handler import BookingWidgetHandler
from handlers.users_list.users_list_handler import User<PERSON><PERSON><PERSON>andler
from handlers.users_list.users_list_handler import get_user_info, get_general_list_info, get_general_graph_info

from handlers.cache.clear_cache import CacheCleaner
from handlers.calls.report_calls_handler import Report<PERSON><PERSON>Handler
from handlers.pushtech.pushtech import Pushtech_call
from handlers.fideltour.fideltour import Fideltour_call
from handlers.url_called.called import Master_Called
from handlers.login.cleanup_sessions import SessionCleaner
from handlers.login.login_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>goutHandler, SetComments
from handlers.observations_handler import ParatyObser<PERSON>Handler
from handlers.utils.utilities import UtilsHandler
from handlers.web.notifications_handler import Notifications<PERSON><PERSON><PERSON>andler
from handlers.web.web_handler import <PERSON><PERSON><PERSON><PERSON>andler
from paraty import app


def build_routes():
    #Session endpoints
    app.add_url_rule("/cleanup_sessions", view_func=SessionCleaner.as_view("Cleanum sessions"), methods=['GET'])
    app.add_url_rule("/login", view_func=LoginHandler.as_view("Login"), methods=['GET', 'POST'])
    app.add_url_rule("/logout", view_func=LogoutHandler.as_view("Logout"), methods=['GET'])
    app.add_url_rule("/api/notifications", view_func=NotificationsAPIHandler.as_view("Api notifications"), methods=['GET'])

    # Default
    app.add_url_rule("/", view_func=MainWebHandler.as_view("Main"), methods=['GET'])
    app.add_url_rule("/web/<pageId>", view_func=MainWebHandler.as_view("Main web"), methods=['GET'])

    app.add_url_rule("/utils", view_func=UtilsHandler.as_view("Utils"), methods=['GET', 'POST'])

    app.add_url_rule("/clear_cache", view_func=CacheCleaner.as_view("Clear cache"), methods=['GET'])

    # API
    app.add_url_rule("/dev", view_func=DevStuffHandler.as_view("Dev"), methods=['GET'])
    app.add_url_rule("/api/hotel/<operation>/<hotel_id>", view_func=HotelApi.as_view("Api Hotel"), methods=['GET', 'POST'])
    app.add_url_rule("/api/loadInvetory", view_func=LoadInventory.as_view("Load invetory"), methods=['POST'])

    app.add_url_rule("/booking-widget/<hotel_id>", view_func=BookingWidgetHandler.as_view("Booking widget"), methods=['GET'])
    app.add_url_rule("/user-list/<hotel_id>", view_func=UserListHandler.as_view("user_list widget"), methods=['GET'])
    app.add_url_rule("/user-list/info/<start_date>/<end_date>", view_func=UserListHandler.as_view("user_list info"), methods=['POST'])
    app.add_url_rule("/user-list/info/<start_date>/<end_date>/<user>", view_func=get_user_info,
                     methods=['POST'])
    app.add_url_rule("/user-list/info/<start_date>/<end_date>/general", view_func=get_general_list_info,
                     methods=['POST'])
    app.add_url_rule("/user-graph/info/<start_date>/<end_date>/general", view_func=get_general_graph_info,
                     methods=['POST'])

    app.add_url_rule("/reports_call/push_call", view_func=ReportCallHandler.as_view("Reports push call"), methods=['GET', 'POST'])
    app.add_url_rule("/reports_call/<operation>", view_func=ReportCallHandler.as_view("Reports"), methods=['GET', 'POST'])

    app.add_url_rule("/pushtech/get_contact", view_func=Pushtech_call.as_view("Call pushtech"), methods=['POST'])

    app.add_url_rule("/fideltour/get_contact", view_func=Fideltour_call.as_view("Call fideltour"), methods=['GET','POST'])

    app.add_url_rule("/call_master/<path:url>", view_func=Master_Called.as_view("Call Master"),methods=['GET', 'POST'])


    app.add_url_rule("/comments/save", view_func=SetComments.as_view("Comments save"), methods=['POST'])
    app.add_url_rule("/comments/get", view_func=SetComments.as_view("Comments get"), methods=['GET'])
    app.add_url_rule("/paraty_observations", view_func=ParatyObservationsHandler.as_view("ParatyObservationsHandler"), methods=['GET', 'POST', 'PUT', 'DELETE'])
