<html lang="es">
<head>
    <meta http-equiv="Content-type" content="text/html; charset=utf-8"/>
    <meta name="robots" content="noindex,follow"/>
    <base target="_top">
    <title>booking_widget_container</title>
    <link href="https://fonts.googleapis.com/css2?family=Source+Sans+Pro:ital,wght@0,200;0,300;0,400;0,600;0,700;0,900;1,200;1,300;1,400;1,600;1,700;1,900&display=swap" rel="stylesheet">
    <link href="/static/template/assets/plugins/font-awesome-5-pro/css/font-awesome-5-pro.css" rel="stylesheet" type="text/css"/>

    <link type="text/css" href="/static/booking_widget/css/template_baseline.css?v=1.01" rel="stylesheet" media="screen"/>
    <link type="text/css" href="https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.8.16/themes/base/minified/jquery-ui.min.css" rel="stylesheet" media="screen"/>

     <link rel="stylesheet" href="https://cdn.datatables.net/1.11.3/css/jquery.dataTables.min.css">

    <link href="/static/css/booking_widget/booking_widget_general.css?v=1.10" rel="stylesheet" type="text/css"/>
    <link href="/static/lib/apexcharts/apex-charts.css" rel="stylesheet" type="text/css"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">


    <script src="//ajax.googleapis.com/ajax/libs/jquery/1.8.3/jquery.min.js"></script>
    <script type="text/javascript" src="/static/booking_widget/lib/jquery-ui-1.10.1.custom.min.modified.js"></script>
    <script type="text/javascript" src="/static/booking_widget/js/datepicker/jquery.ui.datepicker-es.js"></script>
    <script type="text/javascript" src="/static/booking_widget/js/mainWebSkeletonAux.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script src="https://cdn.datatables.net/1.11.3/js/jquery.dataTables.min.js"></script>
    <script type="text/javascript" src="/static/lib/apexcharts/apexcharts.js"></script>
    <script type="text/javascript" src="/static/js/booking_widget.js?v=1.21"></script>
    <script type="text/javascript" src="/static/js/zendesk_tickets.js?v=1.06"></script>
    <script type="text/javascript" src="/static/js/zendesk_tickets.js?v=1.06"></script>
</head>
<body>
<style>
    .odd{
        background-color: rgba(250, 239, 223, 0.93) !important;}
    .even{background-color: rgba(242, 142, 42, 0.46) !important;
        border-left-style: solid;
        border-right-style: solid;
    }
</style>
<div id="motorBusqueda" class="booking_widget_wrapper">
    <div style="display: flex; flex-direction: row; justify-content: center; align-items: center; height: 100vh">

        <div style="display: flex;
                justify-content: center;
                flex-direction: column;
                ">
            <h1 class="fa-2x">Listado de reservas por usuario </h1>
            <h2 class="fa-1x">Reservas realizadas entre:</h2>
            <div class="inputs_wrapper" style="    display: flex;
                                                    align-items: center;
                                                    flex-direction: row;
                                                    margin-top: 4vh;">
                <div class="input_wrapper">
                    <label for="start_date" class="email_label">Fecha de inicio</label>
                    <input type="text" name="start_date" id="start_date" style="margin-top: 10px;">
                </div>
                <div class="input_wrapper">
                    <label for="end_date" class="email_label">Fecha final</label>
                    <input type="text" name="end_date" id="end_date" style="margin-top: 10px;">
                </div>
            </div>
            <div  style="display: flex; flex-direction: row; justify-content: flex-start; align-items: center">
                <button class="btn_2 users_buttons" id="download_list" >Descargar Listado</button>
                <button class="btn_2 users_buttons" id="show_list" >Mostrar Listado </button>
            </div>
        </div>
        <div style="display: flex; flex-direction: column; align-items: center; width: 130vh; height: 100vh">
            <div id="div_selector" style="height: 5vh; display: none; flex-direction: row; justify-content: space-evenly; align-items: center; width: 50%;">
                <select id="select_scale">
                    <option value="10">10</option>
                    <option value="15">15</option>
                    <option value="20">20</option>
                    <option value="30">30</option>
                    <option value="auto">Auto</option>
                </select>
                <input type="number" id="scale_number" placeholder="Escala manual" style="max-width: 120px; border-style: solid; border: 1px solid #ccc;border-radius: 5px;font-size: 16px;outline: none;">
            </div>
            <div style="width: 50%">

                    <table id="user_list_table">
                        <thead>
                            <tr>
                                <th></th>
                                <th></th>
                                <th></th>
                                <th></th>
                            </tr>
                        </thead>
                        <tbody>
                        </tbody>
                    </table>

            </div>
                <dialog id="user_graph" >
                    <div id="dialog_header" style="display: flex; flex-direction: row; justify-content: space-between; align-items: flex-start; width: 80vh; height: 5vh">
                        <h2 id="dialog_user" style="font-size: large"></h2>
                        <button id="close_dialog"><i class="fas fa-times-circle"></i></button>
                    </div>
                    <div id="dialog_body" style="width: 80vh; ">

                    </div>
                    <div id="dialog_footer" style="display: flex; flex-direction: row; justify-content: flex-end; width: 80vh; height: 5vh">
                        <button id="download_graphic" class="btn_1">Download</button>
                    </div>
                </dialog>
        </div>
    </div>
<script>

    let start_date = document.querySelector("#start_date");
    let end_date = document.querySelector("#end_date");
    start_date.flatpickr({
        monthSelectorType: "static",
        dateFormat: "d/m/Y",
    })
    end_date.flatpickr({
        monthSelectorType: "static",
        dateFormat: "d/m/Y",
    })

    $(".users_buttons").on("click",function(){
        let button= $(this)[0]
        button.innerHTML+=' <i class="far fa-clock"></i>'

        let option= $(this)[0].id;

        start_date = document.querySelector("#start_date");
        end_date = document.querySelector("#end_date");

        let now=new Date();
        if(start_date.value === "") {
            start_date=now.getFullYear();
            start_date+="-";
             if(now.getMonth() + 1<10){ start_date+="0"+(now.getMonth() + 1)}else{start_date+=now.getMonth() + 1}
            start_date+="-"+(now.getDate() -1);
        }else{
            start_date = start_date.value.split("/").reverse().join("-");
        }
        if(end_date.value === "") {
            end_date=now.getFullYear();
            end_date+="-";
            if(now.getMonth() + 1<10){ end_date+="0"+(now.getMonth() + 1)}else{end_date+=now.getMonth() + 1}
            end_date+="-"+now.getDate();

        }else{

            end_date = end_date.value.split("/").reverse().join("-");
        }

        $.ajax({
            url: window.location.href.split("/").splice(0,3).join("/")+'/user-list/info/'+start_date+"/"+end_date,
            type: 'POST',
            dataType: 'json',
            success: function(data) {
                let table = $("#user_list_table");
                if(option==="show_list"){
                    if ( $.fn.dataTable.isDataTable( '#user_list_table' )){
                        table.DataTable().clear();
                        table.DataTable().destroy();

                        table = $("#user_list_table").DataTable({

                             columnDefs:[{ "width": "10%", "targets": 3 }],

                             columns: [
                                { data: 'agent', title: "Usuario" , render:function(data, type, row){
                                    return `<div class=${data}> ${data}<div>`
                                    }},
                                { data: 'datetime' , title: "Fecha"},
                                { data: 'count' , title: "Reservas"},
                                { data: 'agent', title: "Grafica", render:function(data, type,row){
                                    return `<button id=graphic_${data}     style="background-color: rgba(250, 239, 223, 0.93) !important;color: black;border-width: unset;">
                                            <i class="far fa-chart-bar" style="font-size: large;"></i>
                                            </button>`
                                    }}
                            ]

                        });

                    }else{
                        table = $("#user_list_table").DataTable({

                         columnDefs:[{ "width": "10%", "targets": 3 }],
                         columns: [
                             { data: 'agent', title: "Usuario" , render:function(data, type, row){
                                    return `<div class=${data}> ${data}<div>`
                                    }},
                            { data: 'datetime' , title: "Fecha"},
                            { data: 'count' , title: "Reservas"},
                            { data: 'agent', title: "Grafica", render:function(data, type,row){
                                return `<button id=graphic_${data}     style="background-color: rgba(250, 239, 223, 0.93) !important;color: black;border-width: unset;">
                                        <i class="far fa-chart-bar" style="font-size: large;"></i>
                                        </button>`
                                }
                            }
                        ]

                        });
                    }

                    JSON.parse(data.data).forEach(function(e, i){
                        table.row.add(e);
                    });
                    //"table.dataTable tbody tr{}"
                    button.innerHTML= button.innerHTML.split(" ").splice(0,2).join(" ");
                    $("#div_selector")[0].style.display="flex";
                    table.draw();
                    $(`[id^="graphic_"]`).on("click", function () {
                            let button_agent= $(this)[0];
                            button_agent.innerHTML='<i class="far fa-clock"></i>'

                            $("#dialog_user")[0].innerHTML = $(this)[0].id.replace("graphic_", "");
                            let dialog_body = $("#dialog_body");
                            $("." + $(this)[0].id.replace("graphic_", ""))
                            $.ajax({
                                url: window.location.href.split("/").splice(0,3).join("/")+'/user-list/info/' + start_date + "/" + end_date + "/" + $(this)[0].id.replace("graphic_", ""),
                                type: 'POST',
                                dataType: 'json',
                                success: function (data) {
                                    //console.log(data);
                                    let date = Object.keys(data);
                                    let user_data = Object.values(data);
                                    let num_scale;
                                    let Yscale;
                                    let select_scale=$("#select_scale")[0].value
                                    if($("#scale_number")[0].value){
                                        num_scale= parseInt($("#scale_number")[0].value);
                                         Yscale={
                                            show: true,
                                            max: num_scale,
                                            min: 0,
                                            labels: {
                                                show: true,
                                            }
                                        }
                                    }else{
                                        if(select_scale=="auto"){
                                            Yscale={
                                                show: true,
                                                min: 0,
                                                labels: {
                                                    show: true,
                                                }
                                            }
                                        }else{
                                            num_scale=parseInt(select_scale);
                                            Yscale={
                                            show: true,
                                            max: num_scale,
                                            min: 0,
                                            labels: {
                                                show: true,
                                            }
                                        }
                                        }
                                    }



                                    var options = {
                                        chart: {
                                            type: 'line',
                                            height: 350
                                        },
                                        series: [{
                                            name: 'Reservas',
                                            data: user_data
                                        }],
                                        xaxis: {
                                            categories: date
                                        },

                                        yaxis: Yscale
                                    }
                                    let graphic = new ApexCharts(dialog_body[0], options);
                                    graphic.render();
                                    button_agent.innerHTML='<i class="far fa-chart-bar" style="font-size: large;"></i>'
                                    $("#user_graph")[0].showModal();
                                    $(`#close_dialog`).on("click", function(){
                                         $(this)[0].parentElement.parentElement.close();
                                         graphic.destroy();
                                    });

                                }

                            });
                        });
                    $('#user_list_table').on('draw.dt', function () {

                        $(`[id^="graphic_"]`).on("click", function () {
                            let button_agent= $(this)[0];
                            button_agent.innerHTML='<i class="far fa-clock"></i>'

                            $("#dialog_user")[0].innerHTML = $(this)[0].id.replace("graphic_", "");
                            let dialog_body = $("#dialog_body");
                            $("." + $(this)[0].id.replace("graphic_", ""))
                            $.ajax({
                                url: window.location.href.split("/").splice(0,3).join("/")+'/user-list/info/' + start_date + "/" + end_date + "/" + $(this)[0].id.replace("graphic_", ""),
                                type: 'POST',
                                dataType: 'json',
                                success: function (data) {
                                    //console.log(data);
                                    let date = Object.keys(data);
                                    let user_data = Object.values(data);
                                    let num_scale;
                                    let Yscale;
                                    let select_scale=$("#select_scale")[0].value
                                    if($("#scale_number")[0].value){
                                        num_scale= parseInt($("#scale_number")[0].value);
                                         Yscale={
                                            show: true,
                                            max: num_scale,
                                            min: 0,
                                            labels: {
                                                show: true,
                                            }
                                        }
                                    }else{
                                        if(select_scale=="auto"){
                                            Yscale={
                                                show: true,
                                                min: 0,
                                                labels: {
                                                    show: true,
                                                }
                                            }
                                        }else{
                                            num_scale=parseInt(select_scale);
                                            Yscale={
                                            show: true,
                                            max: num_scale,
                                            min: 0,
                                            labels: {
                                                show: true,
                                            }
                                        }
                                        }
                                    }



                                    var options = {
                                        chart: {
                                            type: 'line',
                                            height: 350
                                        },
                                        series: [{
                                            name: 'Reservas',
                                            data: user_data
                                        }],
                                        xaxis: {
                                            categories: date
                                        },
                                        legend: {
                                            position: 'bottom',
                                            horizontalAlign: 'center',
                                            onItemClick: {
                                              toggleDataSeries: true
                                        }},
                                        yaxis: Yscale
                                    }
                                    let graphic = new ApexCharts(dialog_body[0], options);
                                    graphic.render();
                                    button_agent.innerHTML='<i class="far fa-chart-bar" style="font-size: large;"></i>'
                                    $("#user_graph")[0].showModal();
                                    $(`#close_dialog`).on("click", function(){
                                         $(this)[0].parentElement.parentElement.close();
                                         graphic.destroy();
                                    });

                                }

                            });
                        });
                    });
                }else{
                    if(option==="download_list"){
                        let table_clone= document.getElementById("user_list_table").cloneNode(true);
                        let tr;
                        table_clone.children[0].innerHTML=`<tr>
                                                            <th>Agente</th>
                                                            <th>Fecha</th>
                                                            <th>Reservas</th>
                                                           </tr>`
                        table_clone.children[1].innerHTML=""
                        JSON.parse(data.data).forEach(function(e, i){
                                tr=document.createElement("tr");
                                tr.innerHTML+=`<td> ${e["agent"]}</td>`;
                                tr.innerHTML+=`<td> ${e["datetime"]}</td>`;
                                tr.innerHTML+=`<td> ${e["count"]}</td>`;
                                table_clone.children[1].appendChild(tr);

                        });

                        let rows = table_clone.querySelectorAll('tr');
                        let csvContent = '';
                        rows.forEach(row => {
                            const cols = row.querySelectorAll('td, th');
                            const rowData = [];
                            cols.forEach(col => rowData.push(col.innerText));
                            csvContent += rowData.join(',') + '\n';
                        });
                        button.innerHTML= button.innerHTML.split(" ").splice(0,2).join(" ");

                        let blob = new Blob([csvContent], { type: 'text/csv' });
                        let link = document.createElement('a');
                        link.href = URL.createObjectURL(blob);
                        link.download = `reservas_por_usuario_${data.hotel_code}_(${start_date}-${end_date}).csv`;

                        document.body.appendChild(link);
                        link.click();

                        document.body.removeChild(link);
                    }
                }

            },
        });
    });
    $("#download_graphic").on("click", function(){
        let svgElement = $(this)[0].parentElement.parentElement.children[1].children[0].children[0]
        let serializer = new XMLSerializer();
        let svgBlob = new Blob([serializer.serializeToString(svgElement)], { type: 'image/svg+xml' });
        let svgUrl = URL.createObjectURL(svgBlob);
        let downloadLink = document.createElement('a');

        downloadLink.href = svgUrl;
        downloadLink.download = 'imagen.svg';
        document.body.appendChild(downloadLink);
        downloadLink.click();
        document.body.removeChild(downloadLink);
    });
</script>
</body>
</html>
