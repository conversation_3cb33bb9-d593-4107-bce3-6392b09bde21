<!-- B<PERSON>IN HEADER -->
<link href="/static/css/booking_widget/booking_widget_general.css?v=1.10" rel="stylesheet" type="text/css"/>
<link href="https://fonts.googleapis.com/css2?family=Source+Sans+Pro:ital,wght@0,200;0,300;0,400;0,600;0,700;0,900;1,200;1,300;1,400;1,600;1,700;1,900&display=swap" rel="stylesheet">
    <link href="/static/template/assets/plugins/font-awesome-5-pro/css/font-awesome-5-pro.css" rel="stylesheet" type="text/css"/>

    <link type="text/css" href="/static/booking_widget/css/template_baseline.css?v=1.01" rel="stylesheet" media="screen"/>
    <link type="text/css" href="https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.8.16/themes/base/minified/jquery-ui.min.css" rel="stylesheet" media="screen"/>

     <link rel="stylesheet" href="https://cdn.datatables.net/1.11.3/css/jquery.dataTables.min.css">

    <link href="/static/css/booking_widget/booking_widget_general.css?v=1.10" rel="stylesheet" type="text/css"/>
    <link href="/static/lib/apexcharts/apex-charts.css" rel="stylesheet" type="text/css"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">

    <script src="//ajax.googleapis.com/ajax/libs/jquery/1.8.3/jquery.min.js"></script>
    <script type="text/javascript" src="/static/booking_widget/lib/jquery-ui-1.10.1.custom.min.modified.js"></script>
    <script type="text/javascript" src="/static/booking_widget/js/datepicker/jquery.ui.datepicker-es.js"></script>
    <script type="text/javascript" src="/static/booking_widget/js/mainWebSkeletonAux.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script src="https://cdn.datatables.net/1.11.3/js/jquery.dataTables.min.js"></script>
    <script type="text/javascript" src="/static/lib/apexcharts/apexcharts.js"></script>
    <style>

    </style>
<div class="header navbar">
    <!-- BEGIN TOP NAVIGATION BAR -->
    <div class="header-inner">
        <!-- BEGIN LOGO -->
        <!--<a class="navbar-brand" href="index.html">-->
        <img id="logotype" src="/static/images/ring2travel.png" alt="logo" class="img-responsive"
             style="height:44px;padding-top: 3px;padding-bottom:3px;padding-left:11px; float:left"/>
        <!--<h4 class="page-title" style="float:left;padding-top: 7px;font-size: 20px; color: #eee;margin-left: 15px"><b>Call Seeker</b></h4>-->
        <!--</a>-->
        <!-- END LOGO -->

        <div class="client_tabs_wrapper">
            <input type="hidden" id="client_default_text" value="{{ T_CLIENT }}">
            <div class="content_wrapper">
                <div class="tabs">
                    <div id="tab_client_1" class="tab_client active" data-tab="1">
                        <input type="text" class="tab" placeholder="{{ T_CLIENT }} 1" value="{{ T_CLIENT }} 1"/>
                        <i class="fal fa-times remove_tab clickable" onclick="HotelsInfo.removeClientTab(1)"></i>
                    </div>
                </div>
                <div class="add_client_tab">
                    + {{ T_ADD_CLIENT }}
                </div>
            </div>
        </div>
        {% if permissions.admin %}
            <button id="general_list" style="border-color: #43425D;background-color: white;cursor: pointer;margin-right: 1.5vh;" >Mostrar Lista general</button>
            <button id="general_graph" style="border-color: #43425D;background-color: white;cursor: pointer;margin-right: 1.5vh;" >Mostrar gráfica general</button>

            <dialog id="general_graph_dialog" style="height: 100%; width: 80%">
                <div id="general_graph_dialog_header" style="display: flex; flex-direction: row; justify-content: space-between; align-items: flex-start; height: 5vh">
                    <h2 id="general_graph_dialog_user" style="font-size: large"></h2>
                    <button class="close_dialog_general"><i class="fas fa-times-circle"></i></button>
                </div>
                <div id="general_graph_dialog_body" style="height: 60vh">

                <h2 class="fa-1x">Reservas realizadas entre:</h2>
                <div class="inputs_wrapper" style="    display: flex;
                                                        align-items: center;
                                                        flex-direction: row;
                                                        justify-content: space-between;
                                                        width: 80vh;
                                                        ">
                    <div class="input_wrapper">
                        <input type="text" name="start_date_general_graph" id="start_date_general_graph" placeholder="Fecha de inicio" style="margin-top: 10px;">
                    </div>
                    <div class="input_wrapper">
                        <input type="text" placeholder="Fecha final" name="end_date_general_graph" id="end_date_general_graph" style="margin-top: 10px;">
                    </div>
                </div>
                <div style="display: flex; flex-direction: row; justify-content: space-between; align-items: center">
                    <button class="btn_2 users_buttons" id="download_general_graph" disabled >Descargar Grafica</button>
                    <button class="btn_2 users_buttons" id="show_general_graph" >Mostrar Grafica </button>
                    <select id="graph_select">
                        <option value="general">General</option>
                        <option value="agentes">Agentes</option>
                    </select>
                    <div id="div_selector" style="flex-direction: row; justify-content: space-evenly; align-items: center; width: 50%;">
                        <select id="select_scale">
                            <option value="auto">Auto</option>
                            <option value="10">10</option>
                            <option value="15">15</option>
                            <option value="20">20</option>
                            <option value="50">50</option>
                            <option value="100">100</option>
                            <option value="200">200</option>
                        </select>
                        <input type="number" id="scale_number" placeholder="Escala manual" style="max-width: 120px; border-style: solid; border: 1px solid #ccc;border-radius: 5px;font-size: 16px;outline: none;">
                    </div>
                </div>
                    <div id="general_graph_svg">

                    </div>
                    <button id="hide_all_series" style="display: none"> Ocultar todo</button>
                    <button id="show_all_series" style="display: none"> Mostrar todo</button>
                </div>
            </dialog>


            <dialog id="general_list_dialog" style="height: 100%;">
            <div id="general_list_dialog_header" style="display: flex; flex-direction: row; justify-content: space-between; align-items: flex-start; width: 80vh; height: 5vh">
                <h2 id="general_list_dialog_user" style="font-size: large"></h2>
                <button class="close_dialog_general"><i class="fas fa-times-circle"></i></button>
            </div>
            <div id="general_list_dialog_body" style="width: 80vh; height: 60vh">
                <div style="display: flex; justify-content: center; flex-direction: column;">
                    <h2 class="fa-1x">Reservas realizadas entre:</h2>
                    <div class="inputs_wrapper" style="margin-bottom: 20px; display: flex; align-items: center; flex-direction: row; justify-content: space-between;">
                        <div class="input_wrapper" style="margin-bottom: 20px !important">
                            <input type="text" name="start_date_general_list" placeholder="Fecha de inicio" id="start_date_general_list" style="margin-top: 10px;">
                        </div>
                        <div class="input_wrapper" style="margin-bottom: 20px !important">
                            <input type="text" placeholder="Fecha final" name="end_date_general_list" id="end_date_general_list" style="margin-top: 10px;">
                        </div>
                    </div>
                    <div style="display: flex; flex-direction: row; justify-content: flex-start; align-items: center">
                        <button class="btn_2 users_buttons" style="margin-bottom: 20px !important;" id="download_general_list">Descargar Listado</button>
                        <button class="btn_2 users_buttons" style="margin-bottom: 20px !important;" id="show_general_list">Mostrar Listado</button>
                    </div>
                </div>
                    <table id="table_general">
                        <thead>
                            <tr>
                                <th></th>
                                <th></th>
                                <th></th>
                            </tr>
                        </thead>
                        <tbody>

                        </tbody>
                    </table>
                </div>
            </dialog>
        {% endif %}

        <!-- BEGIN RESPONSIVE MENU TOGGLER -->
        <a href="javascript:;" class="navbar-toggle" data-toggle="collapse" data-target=".navbar-collapse">
            <img src="/static/template/assets/img/menu-toggler.png" alt=""/>
        </a>
        <!-- END RESPONSIVE MENU TOGGLER -->
        <!-- BEGIN TOP NAVIGATION MENU -->
        <ul class="nav navbar-nav">
            <!-- BEGIN NOTIFICATION DROPDOWN -->
            <li class="dropdown" id="header_notification_bar">
                <a href="#" class="dropdown-toggle" data-toggle="dropdown" data-hover="dropdown"
                   data-close-others="true">
                    <i class="fas fa-bell" id="iconNotificationsContainer"></i>
                    <span class="badge" id="numNotificationsContainer"></span>
                </a>
                <ul class="dropdown-menu extended notification">
                    <li>
                        <p id="textGeneralNotificationsContainer" data-default-text="{{ T_NO_NOTIFICATIONS }}">
                            {{ T_NO_NOTIFICATIONS }}
                        </p>
                    </li>

                    <li>
                        <ul class="dropdown-menu-list scroller" style="height: 250px;" id="notificationsContainer">

                        </ul>

                    </li>
                </ul>
            </li>
            <!-- END NOTIFICATION DROPDOWN -->

            <!-- BEGIN USER LOGIN DROPDOWN -->
            <li class="dropdown user">
                <a href="#" class="dropdown-toggle" data-toggle="dropdown" data-hover="dropdown"
                   data-close-others="true" id="userNameHeader" style="margin-top:5px;">

                    &nbsp;<span class="username">{{session.user.name}}</span>


                    <i class="fa fa-angle-down"></i>
                </a>
                <ul class="dropdown-menu">
                    <li>
                        <a href="/logout">
                            <i class="fa fa-key"></i> Log Out
                        </a>
                    </li>
                </ul>
            </li>
            <!-- END USER LOGIN DROPDOWN -->
        </ul>
        <!-- END TOP NAVIGATION MENU -->
    </div>
    <!-- END TOP NAVIGATION BAR -->
</div>
<!-- END HEADER -->
<div class="clearfix">
</div>
