<div id="zendesk_container_template" class="zendesk_container">
    <div class="top_wrapper">
        <span class="title">
            ¿El cliente ha reservado antes con nosotros?
        </span>
        <span class="new_client_wrapper">
            <span class="new_client_btn active" data-new_client="false">Si</span>
            <span class="new_client_btn" data-new_client="true">No</span>
        </span>
    </div>
    <form class="zendesk_form">
        <div class="inputs_wrapper">
            <div class="input_wrapper">
                <input class="user_field" type="text" name="name">
                <label for="name">Nombre *</label>
            </div>
            <div class="input_wrapper">
                <input class="user_field" type="text" name="email">
                <label for="email">Email *</label>
            </div>
            <div class="input_wrapper">
                <input class="user_field" type="text" name="phone">
                <label for="phone">Teléfono</label>
            </div>
            <div class="input_wrapper">
                <input class="user_field" type="text" name="whatsapp">
                <label for="whatsapp">Whatsapp</label>
            </div>
            {# <div class="input_wrapper">
                <label for="language">Idioma</label>
                <select class="user_field" name="language">
                    <option value="">Seleccionar</option>
                    {% for language in session.user.language_list %}
                        <option value="idioma_{{ language.code }}">{{ language.name }}</option>
                    {% endfor %}
                </select>
            </div> #}
            <div class="input_wrapper">
                <select class="user_field" name="country">
                    <option value="">País</option>
                    {% for code, country in session.user.country_list %}
                        <option value="{{ code }}">{{ country }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="input_wrapper">
                <select class="user_field" name="nationality">
                    <option value="">Nacionalidad</option>
                    <option value="nacional">Nacional</option>
                    <option value="internacional">Internacional</option>
                </select>
            </div>
        </div>

        <div class="buttons_block">
            <div class="search_loader">
                <img src="/static/images/loading_spinner.gif" alt="Loading">
            </div>
            <div class="find_user_btn" id ="div_select_register" style="display: none">
                <button type="button"  id="select_registers">
                    Seleccionar Registros
                </button>
            </div>
            <div class="find_user_btn">
                <button type="button">
                    Buscar usuario
                </button>
            </div>
            <div class="create_user_btn">
                <button type="button">
                    {{ T_CREATE_USER }}
                </button>
            </div>
            <div class="clear_search_btn">
                <button type="reset">
                    {{ T_CLEAR_SEARCH }}
                </button>
            </div>
        </div>
    </form>
    <div class="zendesk_results">
        <div class="user_element zendesk_user_template" data-client_id="">
            <form class="modify_form">
                <div class="user_name field_element">
                    <img class="user_field_icon" src="/static/images/zendesk/fi-rr-user.svg" alt="Icon">
                    <input class="user_field" type="text" name="name">
                </div>
                <div class="user_phone field_element">
                    <img class="user_field_icon" src="/static/images/zendesk/fi-rr-phone-call.svg" alt="Icon">
                    <input class="user_field" type="text" name="phone">
                </div>
                <div class="user_language field_element">
                    <img class="user_field_icon" src="/static/images/zendesk/idioma.svg" alt="Icon">
                    <select class="user_field" name="language" disabled>
                        <option value=""></option>
                        {% for language in session.user.language_list %}
                            <option value="idioma_{{ language.code }}">{{ language.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="user_email field_element">
                    <img class="user_field_icon" src="/static/images/zendesk/fi-rr-envelope.svg" alt="Icon">
                    <input class="user_field" type="text" name="email">
                </div>
                <div class="user_whatsapp field_element">
                    <img class="user_field_icon" src="/static/images/zendesk/WA_Logo.svg" alt="Icon">
                    <input class="user_field" type="text" name="whatsapp">
                </div>
                <div class="user_country field_element">
                    <img class="user_field_icon" src="/static/images/zendesk/globo.svg" alt="Icon">
                    <select class="user_field" name="country">
                        <option value=""></option>
                        {% for code, country in session.user.country_list %}
                            <option value="{{ code }}">{{ country }}</option>
                        {% endfor %}
                    </select>
                </div>
            </form>
            <div class="buttons_wrapper">
                <div class="modify_user_btn">
                    Editar usuario
                </div>
                <div class="save_user_btn">
                    Guardar cambios
                </div>
                <div class="select_user_btn">
                    Seleccionar
                </div>
                <div class="cancel_user_btn">
                    Cancelar selección
                </div>
            </div>
        </div>
        <div class="pagination">
            <i class="prev-page fal fa-angle-left" aria-hidden="true"></i>
            <span class="pages"></span>
            <i class="next-page fal fa-angle-right" aria-hidden="true"></i>
        </div>
    </div>
</div>