<html lang="es">
<head>
    <meta http-equiv="Content-type" content="text/html; charset=utf-8"/>
    <meta name="robots" content="noindex,follow"/>
    <base target="_top">
    <title>booking_widget_container</title>
    <link href="https://fonts.googleapis.com/css2?family=Source+Sans+Pro:ital,wght@0,200;0,300;0,400;0,600;0,700;0,900;1,200;1,300;1,400;1,600;1,700;1,900&display=swap" rel="stylesheet">
    <link href="/static/template/assets/plugins/font-awesome-5-pro/css/font-awesome-5-pro.css" rel="stylesheet" type="text/css"/>

    <link type="text/css" href="/static/booking_widget/css/template_baseline.css?v=1.01" rel="stylesheet" media="screen"/>
    <link type="text/css" href="https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.8.16/themes/base/minified/jquery-ui.min.css" rel="stylesheet" media="screen"/>
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-beta.1/dist/css/select2.min.css" rel="stylesheet" />

    <link href="/static/css/booking_widget/booking_widget_general.css?v=1.11" rel="stylesheet" type="text/css"/>

    <script src="//ajax.googleapis.com/ajax/libs/jquery/1.8.3/jquery.min.js"></script>
    <script type="text/javascript" src="/static/booking_widget/lib/jquery-ui-1.10.1.custom.min.modified.js"></script>
    <script type="text/javascript" src="/static/booking_widget/js/datepicker/jquery.ui.datepicker-es.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-beta.1/dist/js/select2.min.js"></script>
    <script type="text/javascript" src="/static/booking_widget/js/mainWebSkeletonAux.min.js"></script>
    <script type="text/javascript" src="/static/js/booking_widget.js?v=1.21"></script>
    <script type="text/javascript" src="/static/js/zendesk_tickets.js?v=1.06"></script>

    <meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body>

<div id="motorBusqueda" class="booking_widget_wrapper">
    {% if zendesk_enabled %}
        <input type="hidden" id="zendesk_enabled" value="true"/>
    {% endif %}
    {% if puschtech_enabled %}
        <input type="hidden" id="pushtech_enabled" class="puschtech_enabled" value="true"/>
    {% endif %}
    {% if fideltour_enabled %}
        <input type="hidden" id="fideltour_enabled" class="fideltour_enabled" value="true"/>
    {% endif %}
    {% if internal_url %}
        <input type="hidden" id="internal_url" value="{{ internal_url|safe }}">
    {% endif %}
    {% if book_yesterday %}
        <input type="hidden" id="book_yesterday" value="true"/>
    {% endif %}
    {% if booking_spa %}
        <input type="hidden" id="booking_spa" value="true"/>
    {% endif %}

    <div id="changes-pending" class="hidden">
        {{ T_PENDING_CHANGES }}
    </div>

    <form data-original-action="{{ booking_url|safe }}" action="{{ booking_url|safe }}" name="searchForm" id="searchForm" class="search_form" method="{{ "get" if booking_spa else "post" }}" target="booking_results">
        <div class="search_form_block">
            {% if booking_spa %}
                <input type="hidden" id="agentId" name="agentId" value="{{ agent_id }}"/>
            {% else %}
                <input type="hidden" id="agesKid1" name="agesKid1"/>
                <input type="hidden" id="agesKid2" name="agesKid2"/>
                <input type="hidden" id="agesKid3" name="agesKid3"/>
                <input type="hidden" id="agesBaby1" name="agesBaby1"/>
                <input type="hidden" id="agesBaby2" name="agesBaby2"/>
                <input type="hidden" id="agesBaby3" name="agesBaby3"/>
                <input type="hidden" id="namespace" name="namespace" value="{{ namespace }}"/>
                <input type="hidden" id="applicationIds" name="applicationIds" data-original-value="{{ applicationIds }}" value="{{ applicationIds }}"/>
                <input type="hidden" id="agentId" name="agentId" value="{{ agent_id }}"/>
                <input type="hidden" id="agentEmail" name="agentEmail" value="{{ agent_email }}"/>
                <input type="hidden" id="ignoreStats" name="ignoreStats" value="True"/>
                <input type="hidden" id="source" name="source" value="Callcenter"/>
                <input type="hidden" id="rescue_discount" name="rescue_discount"/>
                <input type="hidden" id="requestFrom" name="requestFrom" value="callcenter"/>
                <input type="hidden" id="ignoreSecurityHeaders" name="ignoreSecurityHeaders" value="True"/>
                <input type="hidden" id="utm_source" name="utm_source" value="Callcenter"/>
                <input type="hidden" id="utm_medium" name="utm_medium" value="Callcenter"/>
                <input type="hidden" id="utm_campaign" name="utm_campaign" value="Callcenter"/>
                <input type="hidden" id="utm_content" name="utm_content" value="{{ agent_id }}"/>
                <input type="hidden" name="limit_user_reservation" id="limit_user_reservation" value="true">
            {% endif %}

            <div class="switches_wrapper">
                {% if lite_version %}
                    <div class="lite_switch_wrapper switch_element_wrapper">
                        <span class="lite_label label">{{ T_USE_LITE_VERSION }}</span>
                        <div class="switch">
                            <input class="checkbox" type="checkbox" checked="checked">
                            <div class="knobs"></div>
                            <div class="layer"></div>
                        </div>
                    </div>
                {% endif %}
                {% if flight_hotel_data %}
                    <div class="flight_hotel_switch_wrapper switch_element_wrapper">
                        <span class="flight_hotel_label label">{{ T_HOTEL_FLIGHT }}</span>
                        <div class="switch">
                            <input class="checkbox" type="checkbox" checked="checked">
                            <div class="knobs"></div>
                            <div class="layer"></div>
                        </div>
                    </div>
                {% endif %}
            </div>
            <div class="top_inputs_wrapper">
                {% if flight_hotel_data %}
                    <div class="flight_hotel_selector_wrapper input_wrapper with_selector">
                        <label>{{ T_ORIGIN_AIRPORT|safe }}</label>
                        <select id="airport_selector" name="flight_hotel"></select>
                    </div>
                {% endif %}
            </div>

            {% if not booking_spa %}
                <div class="rooms_data_block">

                {% if hotel_groups %}
                    <div class="group_search">
                        <div class="group_search_label">
                            {{ T_WANT_TO_SEARCH_BY_DESTINATION }}
                        </div>
                        <div class="group_search_checkbox_wrapper">
                            <input type="checkbox" class="hidden_checkbox" id="group_search" style="display: none; !important;">
                            <div class="yes_no_checkbox">
                                <div class="yes_no_checkbox_option yes" data-value="yes">
                                    {{ T_YES }}
                                </div>
                                <div class="yes_no_checkbox_option no" data-value="no">
                                    {{ T_NO }}
                                </div>
                            </div>

                        </div>
                    </div>
                {% endif %}

                <div class="stay_selection input_wrapper">
                    <div class="start_date">
                        <label for="datepicker1" class="start_date_label">{{ T_ENTRY_DATE }}</label>
                        <input type="text" class="datepicker" name="startDate" value="" disabled="disabled" id="datepicker1"
                               tabindex="4" readonly="readonly"/>
                    </div>
                    <div class="end_date">
                        <label for="datepicker2" class="end_date_label">{{ T_DEPARTURE_DATE }}</label>
                        <input type="text" class="datepicker" name="endDate" value="" disabled="disabled" id="datepicker2"
                               tabindex="5" readonly="readonly"/>
                    </div>
                </div>

                <div class="geolocation_selection input_wrapper with_selector">
                    <select id="fromCountry" name="{{ "force_geo" if booking_spa else "fromCountry" }}" tabindex="6">
                       {% if countries_client %}
                             {% for country in countries_client %}
                                <option value={{ country.code }}   {% if loop.first %}selected{% endif %}>{{ country.value }}</option>
                            {% endfor %}
                        {% else %}
                            <option value="es" selected>{{ T_SPAIN }}</option>
                            <option value="gb">{{ T_UNITED_KINGDOM }}</option>
                            <option value="fr">{{ T_FRANCE }}</option>
                            <option value="de">{{ T_GERMANY }}</option>
                            <option value="pt">{{ T_PORTUGAL }}</option>
                            <option value="ca">{{ T_CANADA }}</option>
                            <option value="us">{{ T_USA }}</option>
                            <option value="nl">{{ T_NETHERLANDS }}</option>
                        {% endif %}
                       <option value="af">{{ T_REST_WORLD }}</option>
                    </select>
                </div>

                <div class="language_selection input_wrapper with_selector">
                    <select id="language" name="language" tabindex="6">
                        {% for language in languages_client %}
                            <option value={{ language.code }}  {% if language.code == 'es' %}selected{% endif %}>{{ language.value }}</option>
                        {% endfor %}
                    </select>
                </div>

                 {% if show_mobile_offers %}
                    <label class="checkbox_wrapper force_mobile_checkbox">
                        <input type="checkbox" name="forceMobile" id="forceMobile">
                        <span class="custom_checkbox"></span>
                        <div class="checkbox_label">{{ T_MOBILE_RATE }}</div>
                    </label>
                {% endif %}

                {% if show_limit_user_reservation %}
                    <label class="checkbox_wrapper limit_user_checkbox">
                        <input type="checkbox" name="limitUser" id="limitUser" checked>
                        <span class="custom_checkbox"></span>
                        <div class="checkbox_label">{{ T_LIMIT_USER }}</div>
                    </label>
                {% endif %}

                {% if show_hide_payment_gateway %}
                    <label class="checkbox_wrapper">
                        <input type="checkbox" name="gateway_less" id="gateway_less">
                        <span class="custom_checkbox"></span>
                        <div class="checkbox_label">{{ T_SKIP_PAYMENT_GATEWAY }}</div>
                    </label>
                {% endif %}

                {% if show_ignore_min_stay %}
                    <label class="checkbox_wrapper">
                        <input type="checkbox" name="ignore_min_stay" id="ignore_min_stay" value="True">
                        <span class="custom_checkbox"></span>

                        <div class="checkbox_label">{{ T_EXCLUDE_MIN_STAYS }}</div>
                    </label>
                {% endif %}

                {% if ignore_room_availability %}
                    <label class="checkbox_wrapper">
                        <input type="checkbox" name="ignore_room_availability" id="ignore_room_availability" value="True">
                        <span class="custom_checkbox"></span>
                        <div class="checkbox_label">{{ T_EXCLUDE_CLOSED_SALES }}</div>
                    </label>
                {% endif %}
            </div>
            {% endif %}

            <div class="search_filters_block">
                {% if hotel_groups %}
                    <div class="group_selector input_wrapper with_selector">
                        <select id="group_select" class="group_select">
                            {% if hotel_groups.all %}
                                <option value="{{ hotel_groups.all|join(';') }}">
                                    {{ T_ALL_DESTINATIONS }}
                                </option>
                            {% endif %}

                            {% if hotel_groups.destinations %}
                                <optgroup label="{{ T_DESTINATIONS }}">
                                    {% for group_name, group_hotels in hotel_groups.destinations.items() %}
                                        <option value="{{ group_hotels|join(';') }}">{{ group_name }}</option>
                                    {% endfor %}
                                </optgroup>
                            {% endif %}

                            {% if hotel_groups.countries %}
                                <optgroup label="{{ T_COUNTRIES }}">
                                    {% for group_name, group_hotels in hotel_groups.countries.items() %}
                                        <option value="{{ group_hotels|join(';') }}">{{ group_name }}</option>
                                    {% endfor %}
                                </optgroup>
                            {% endif %}

                            {% if hotel_groups.custom %}
                                <optgroup label="{{ T_CUSTOMIZED }}">
                                    {% for group_name, group_hotels in hotel_groups.custom.items() %}
                                        <option value="{{ group_hotels|join(';') }}">{{ group_name }}</option>
                                    {% endfor %}
                                </optgroup>
                            {% endif %}
                        </select>
                    </div>
                {% endif %}

                <div class="occupancy_selection input_wrapper with_selector">
                    <label class="occupancy_label">{{ T_OCCUPANCY }}</label>
                    <span class="occupancy_display">2/0{% if showBabies %}/0{% endif %}</span>
                    <div class="occupancy_selector {% if showPets %}with_pets{% endif %}">
                        <div class="selector_block rooms">
                            <div class="label adults_label">{{ T_N_ROOMS }}</div>
                            <button class="button_number minus"><i class="fal fa-minus" aria-hidden="true"></i></button>
                            <input class="occupancy_counter rooms" type="number" name="numRooms" value="1" min="1" max="{{ room_index_list|length }}" readonly>
                            <button class="button_number plus"><i class="fal fa-plus" aria-hidden="true"></i></button>
                        </div>
                        {% for room in room_index_list %}
                            <div id="room_{{ loop.index }}" class="room_selection room_{{ loop.index }}" {% if not loop.first %}style="display: none"{% endif %}>
                                <div class="flex_wrapper">
                                    <div class="selector_block adults">
                                        <div class="label adults_label">
                                            {{ T_ADULTS }}
                                            {% if kids_age_range %}<br><br>{% endif %}
                                        </div>
                                        <button class="button_number minus"><i class="fal fa-minus" aria-hidden="true"></i></button>
                                        <input class="occupancy_counter adults" type="number" name="adultsRoom{{ loop.index }}" value="2" min="1" readonly>
                                        <button class="button_number plus"><i class="fal fa-plus" aria-hidden="true"></i></button>
                                    </div>
                                    {% if not only_adults %}
                                        <div class="selector_block childrens">
                                            <div class="label adults_label">
                                                {{ T_KIDS }}
                                                {% if kids_age_range %}
                                                    <div class="ages_range">({{ kids_age_range.start }} - {{ kids_age_range.end }} {{ T_YEARS|lower }})</div>
                                                {% endif %}
                                            </div>
                                            <button class="button_number minus"><i class="fal fa-minus" aria-hidden="true"></i></button>
                                            <input class="occupancy_counter children" type="number"
                                                   name="childrenRoom{{ loop.index }}"
                                                   {% if kids_age_range %}
                                                        data-ages-min="{{ kids_age_range.start }}"
                                                        data-ages-max="{{ kids_age_range.end }}"
                                                    {% endif %}
                                                   value="0"
                                                   readonly>
                                            <button class="button_number plus"><i class="fal fa-plus" aria-hidden="true"></i></button>
                                        </div>
                                    {% endif %}
                                    {% if showBabies %}
                                        <div class="selector_block babies">
                                            <div class="label adults_label">
                                                {{ T_BABIES }}
                                                {% if babies_age_range %}
                                                    <div class="ages_range">({{ babies_age_range.start }} - {{ babies_age_range.end }} {{ T_YEARS|lower }})</div>
                                                {% endif %}
                                            </div>
                                            <button class="button_number minus"><i class="fal fa-minus" aria-hidden="true"></i></button>
                                            <input class="occupancy_counter babies" type="number"
                                                   name="babiesRoom{{ loop.index }}"
                                                   value="0"
                                                   {% if babies_age_range %}
                                                       data-ages-min="{{ babies_age_range.start }}"
                                                       data-ages-max="{{ babies_age_range.end }}"
                                                   {% endif %}
                                                   readonly>
                                            <button class="button_number plus"><i class="fal fa-plus" aria-hidden="true"></i></button>
                                        </div>
                                    {% endif %}
                                    {% if showPets %}
                                        <div class="selector_block pets">
                                            <div class="label pets_label">{{ T_PETS }}</div>
                                            <button class="button_number minus"><i class="fal fa-minus" aria-hidden="true"></i></button>
                                            <input class="occupancy_counter pets" type="number" name="petsRoom{{ loop.index }}" value="0" max="{{ showPets }}" readonly>
                                            <button class="button_number plus"><i class="fal fa-plus" aria-hidden="true"></i></button>
                                        </div>
                                    {% endif %}
                                </div>
                                {% if kids_age_range %}
                                    <div class="children_age_wrapper"></div>
                                {% endif %}
                                {% if show_babies_age_selector and babies_age_range %}
                                    <div class="babies_age_wrapper"></div>
                                {% endif %}
                            </div>
                        {% endfor %}
                    </div>
                </div>

                {% if promocode_list %}
                    <div class="promocode_selection input_wrapper">
                        <label for="promocodeInput" class="promocode_label">{{ T_PROMOCODE }}</label>
                        <select id="promocodeInput" name="promocode" tabindex="7">
                            <option value="">{{ T_NO_PROMOCODE }}</option>
                            {% for promocode in promocode_list %}
                                <option value="{{ promocode }}">{{ promocode }}</option>
                            {% endfor %}
                        </select>
                    </div>
                {% else %}
                    <div class="promocode_selection input_wrapper">
                        <label for="promocodeInput" class="promocode_label">{{ T_PROMOCODE }}</label>
                        <input type="text" name="promocode" id="promocodeInput" value="{{ default_promocode }}"/>
                    </div>
                {% endif %}

                <div class="user_params_wrapper">
                    {% if show_users_club %}
                        <div class="user_dni_selection input_wrapper {% if show_perfil_club_amigos %}with_club_profile_selection{% endif %}">
                            <label for="user_dni" class="user_dni_label">{{ T_USER_ID }}</label>
                            <input type="text" name="user_dni" id="user_dni" value=""/>
                        </div>
                        <div class="user_email_selection input_wrapper {% if show_perfil_club_amigos %}with_club_profile_selection{% endif %}">
                            <label for="user_email" class="user_email_label">{{ T_USER_EMAIL }}</label>
                            <input type="text" name="user_email" id="user_email" value=""/>
                        </div>
                    {% endif %}

                    {% if vacation_club %}
                        <div class="vacation_club_selection input_wrapper">
                            <label for="vacation_club_member_id" class="vacation_club_label">{{ T_VACATION_CLUB_MEMBER_ID }}</label>
                            <input type="text" name="vacation_club_member_id" id="vacation_club_member_id" value=""/>
                        </div>
                    {% endif %}

                    {% if show_perfil_club_amigos %}
                        <div class="club_profile_selection input_wrapper">
                            <label for="perfilClubAmigos" class="club_profile_label">{{ T_LEVEL_CLIENT_ID }}</label>
                            <input type="text" name="perfil" id="perfilClubAmigos"/>
                        </div>
                    {% endif %}
                </div>

                {% if not hide_comments %}
                    <div class="comments_wrapper">
                        <label for="comments" class="comments_label">{{ T_HOTEL_OBSERVATIONS }}</label>
                        <textarea id="comments" {% if not write_comments %}readonly{% endif %} class="input_wrapper">{{ data_comments }}</textarea>
                        {% if write_comments %}
                            <div class="buttons_block" style="margin-bottom: 20px; padding-top: 0px !important;">
                                <div class="submit_button comments">
                                    <button id="show_comments" name="show_comments" type="button">
                                        {{ T_SAVE }}
                                    </button>
                                </div>
                            </div>
                        {% endif %}
                    </div>
                {% endif %}
            </div>

            <div class="buttons_block">
                <button id="search_button" class="btn_1 submit_button" type="submit" onclick="return bookingSearch(this);">
                    {{ T_SEARCH }}
                </button>

                {% if related_hotels %}
                    <button class="btn_2 related_hotels_search_button" type="submit" data-related-hotels="{{ related_hotels }}" onclick="return bookingSearch(this);">
                        {{ T_SEARCH_RELATED_HOTELS }}
                    </button>
                {% endif %}

                {% if reload_button %}
                    <button class="btn_2 reload_button" type="submit" onclick="return reloadBookingIframe();">
                        {{ T_RELOAD_PROCESS }}
                    </button>
                {% endif %}

                <button class="btn_3 reset_button" type="reset" id="clear_search">
                    {{ T_CLEAR_SEARCH }}
                </button>

                {% if users %}
                <div class="user_selection input_wrapper" style="width: 100%;">
                    <label for="user" class="user_label">Usuario</label>
                    <select id="user" name="user_call" tabindex="6">
                    {% for user in users %}
                        <option value={{ user }}>{{ user }}</option>
                    {% endfor %}
                    </select>
                </div>
                {% endif %}


                {% if master_agents_selector %}
                    <div class="agent_selection input_wrapper with_selector" style="width: 100%;">
                        <label for="agent" class="agent_label">{{ T_AGENTE_ASOCIADO_RESERVA }}</label>
                        <select id="agent" name="agent_call" tabindex="6">
                            {% for user in master_agents_selector %}
                                <option value={{ user }}>{{ user }}</option>
                            {% endfor %}
                        </select>
                    </div>
                {% endif %}

            </div>
        </div>

        <div class="progress hotel_search">
          <div class="progress-bar" role="progressbar" aria-valuenow="70"
          aria-valuemin="0" aria-valuemax="100">
          </div>
        </div>

        {% if breadcrumbs_enabled %}
            <div class="breadcrumbs_wrapper">
                <div class="step booking1">
                    Booking 1
                </div>
                <div class="step booking2">
                    Booking 2
                </div>
                <div class="step booking3">
                    Booking 3
                </div>
            </div>
        {% endif %}

        <div class="booking_results_wrapper hidden" style="{{ "height: 600px" if booking_spa }}">
            <iframe name="booking_results" allowfullscreen="" allow="xr-spatial-tracking" {% if disable_booking_scroll %}data-disable-scroll="true"{% endif %}></iframe>
        </div>

    </form>
    {% if not booking_spa %}
        <div class="bottom_wrapper">

            {% if zendesk_enabled %}
                <div class="tickets_wrapper">
                    <button class="btn_1 create_ticket_btn" type="button">
                        Crear ticket
                    </button>
                    <button class="btn_1 modify_ticket_btn" type="button">
                        Modificar ticket
                    </button>

                    <div class="inputs_wrapper">
                        <div class="whatsapp_switch_wrapper">
                            <div class="switch">
                                <input class="checkbox" type="checkbox">
                                <div class="knobs"></div>
                                <div class="layer"></div>
                            </div>
                        </div>
                        <div class="bottom_flex">
                            <div class="input_wrapper id_ticket">
                                <input class="ticket_field" type="text" name="id_ticket">
                                <label for="id_ticket">ID ticket</label>
                            </div>
                            <div class="input_wrapper email">
                                <input class="ticket_field" type="text" name="email">
                                <label for="email">Email</label>
                            </div>
                            <button class="btn_2 send_ticket_btn" type="button">
                                Enviar
                            </button>
                            <div class="send_loader">
                                <img src="/static/images/loading_spinner.gif" alt="Loading">
                            </div>
                        </div>
                    </div>
                    <div class="response_wrapper hide">
                        <span class="label create_ticket">ID del ticket:</span>
                        <span class="ticket_id"></span>
                    </div>
                </div>
            {% endif %}

            <div class="bottom_flex_wrapper">
                {% if users %}
                    <div class="user_wrapper">
                        <div class="block_title">{{ T_RESERVATIONS_LIST_PER_USER }}</div>
                        <div>{{ T_RESERVATIONS_MADE_BETWEEN }}:</div>
                        <div class="stay_selection input_wrapper">
                            <div class="start_date_list">
                                <label for="datepicker3" class="start_date_label">{{ T_INITIAL_DATE }}</label>
                                <input type="text" class="datepicker" name="startDateList" value="" id="datepicker3"
                                       tabindex="6" readonly="readonly"/>
                            </div>
                            <div class="end_date_list">
                                <label for="datepicker4" class="end_date_label">{{ T_FINAL_DATE }}</label>
                                <input type="text" class="datepicker" name="endDateList" value=""  id="datepicker4"
                                       tabindex="7" readonly="readonly"/>
                            </div>

                        </div>

                         <div class="button_wrapper">
                             <button class="btn_2" type="submit" onclick="getListReservationsByUser();">{{ T_DOWNLOAD_LIST }}</button>
                         </div>
                        </br></br>
                    </div>
                {% endif %}

                <div class="right_wrapper">
                    {% if prebooking_config.prebooking_enabled %}
                        {% if not prebooking_config.hide_multiprebooking or permissions.show_multiprebooking %}
                            <input type="hidden" id="prebooking_enabled" value="true"/>
                            <div class="multi_prebooking_block">
                                <div class="block_title">
                                    {{ T_SEND_PREBOOKING_MULTI }}
                                    <span class="tooltip_info">
                                        <i class="fal fa-info-circle"></i>
                                        <span class="info_content">
                                            Se enviarán al email indicado todas las pestañas de hotel abiertas sobre el cliente elegido en la pestaña superior.
                                            Para que se envíe una pestaña esta necesariamente debe haber llegado al formulario de datos personales.
                                            Para enviar la cotización de un solo hotel, por favor hágala desde el último paso de la reserva de dicho hotel.
                                            El envío de este email llevará al cliente al paso final de la reserva sin rellenarse datos.
                                        </span>
                                    </span>
                                </div>
                                <div class="content_wrapper">
                                    <div class="flex_wrapper">
                                        <div class="email_selection input_wrapper">
                                            <label for="email" class="email_label">{{ T_DESTINATION_EMAIL }}</label>
                                            <input type="text" name="email" id="email"/>
                                        </div>
                                        <div id="multi_prebooking_loading" style="width: 215px;display: none">
                                            <img style="width: 50px;margin-left: 20px;" src="/static/images/loading_spinner.gif" alt="Loading">
                                        </div>
                                    </div>
                                    <div class="options_checkbox_wrapper">
                                        {% if prebooking_config.show_chk_block_availability %}
                                            <label class="checkbox_wrapper">
                                                <input type="checkbox" name="block_availability" id="block_availability">
                                                <span class="custom_checkbox"></span>
                                                <div class="checkbox_label">{{ T_BLOCK_AVAILABILITY }}</div>
                                            </label>
                                        {% endif %}
                                        {% if not prebooking_config.hide_chk_block_price %}
                                            <label class="checkbox_wrapper">
                                                <input type="checkbox" name="block_price" id="block_price">
                                                <span class="custom_checkbox"></span>
                                                <div class="checkbox_label">{{ T_BLOCK_PRICE }}</div>
                                            </label>
                                        {% endif %}
                                    </div>
                                    <div class="buttons_wrapper">
                                        <div class="button_wrapper">
                                            <button class="btn_2">{{ T_CREATE_PREBOOKING }}</button>
                                        </div>
                                        <div class="button_wrapper preview">
                                            <button class="btn_2 preview">{{ T_SEE_PREBOOKING }}</button>
                                        </div>
                                        <div id="multi_prebooking_loading" style="width: 215px;display: none">
                                            <img style="width: 50px;margin-left: 20px;" src="/static/images/loading_spinner.gif" alt="Loading">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        {% endif %}
                    {% endif %}


                    <div class="send_email_block">
                        <div class="block_title">{{ T_SEND_CONFIRMATION_EMAIL }}</div>
                        <form id="sendEmail" class="send_email_form">
                            <div class="identifier_selection input_wrapper">
                                <label for="emailIdentifier" class="identifier_label">{{ T_IDENTIFIER }}</label>
                                <input type="text" name="identifier" id="emailIdentifier"/>
                            </div>
                            <div class="email_selection input_wrapper">
                                <label for="emailEmail" class="email_label">{{ T_DESTINATION_EMAIL }}</label>
                                <input type="text" name="email" id="emailEmail"/>
                            </div>
                            <div class="button_wrapper">
                                <button class="btn_2" type="submit" onclick="requestEmail();return false;">{{ T_SEND_EMAIL }}</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            {% if is_ring2travel_user %}
                {% include "paraty_observations.html" %}
            {% endif %}
        </div>
    {% endif %}
</div>

{% if flight_hotel_data %}
    <script type="text/javascript" src="/static/js/flight_hotel.js?v=1.00"></script>
    <script>
        $(function() {
            flightHotelSelector.config.airportOptionsJSON = `{{ flight_hotel_data|tojson|safe|replace('`', '\`') }}`;
            flightHotelSelector.init();
        });
    </script>
{% endif %}
</body>
</html>