
<div class="paraty_observations_wrapper">
    <input type="hidden" name="hotel_code" value="{{ namespace }}">
    <div class="title_wrapper">
        {{ T_PARATY_OBSERVATIONS }}
    </div>

    <div class="observations_list">

    </div>

    <div class="observation_writter">
        <textarea name="" id="" cols="30" rows="4" placeholder="{{ T_NEW_OBSERVATION }}..."></textarea>
        <div class="button add-button">{{ T_SAVE }}</div>
    </div>

    <div class="observation_baseline">
        <div class="user_info"></div>
        <div class="observation_comment"></div>
        <div class="observation_date"></div>
        <div class="observation_modification"></div>
        <div class="observation_button">
            <div class="button delete-button" onclick="ParatyObservations.deleteObservation(this)">{{ T_DELETE }}</div>
        </div>
        <input type="hidden" name="observation_id">
    </div>
</div>

<script type="text/javascript" src="/static/js/paraty_observations.js"></script>
<script>
    ParatyObservations.init();
</script>