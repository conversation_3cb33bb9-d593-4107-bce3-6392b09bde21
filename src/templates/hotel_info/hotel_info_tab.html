<link type="text/css" href="/static/css/styles.css" rel="stylesheet" media="screen"/>
<div class="hidden_hotel_data" id="hotel_data_${tabId}" style="display: none !important;">
    <input type="hidden" class="web_link" value="${webIframe}">
    <input type="hidden" class="offers_link" value="${webIframe_offer}">
    <input type="hidden" class="hotel_name" value="${hotelName}"/>
</div>
<div class="row">
    <div class="col-md-12">
        <div class="col-md-10 col-sm-10">
            <ul class="nav nav-pills tabs_wrapper" style="display: none;">
                <li class="active">
                    <a href="#bookingEngine${tabId}" class="booking_engine_tab" data-toggle="tab">
                        {{ T_BOOKING_ENGINE }}
                    </a>
                </li>
                <li>
                    <a href="#info${tabId}" class="hotel_info_tab" data-toggle="tab">
                        {{ T_HOTEL_RECORD }}
                    </a>
                </li>
                <li>
                    <a href="#modifyBooking${tabId}" class="reservation_management_tab" data-toggle="tab">
                        {{ T_CANCEL_MODIFY_RESERVATION }}
                    </a>
                </li>
                <li>
                    <a href="#clubAmigos${tabId}" id="linkClubAmigos${tabId}" class="club_tab" data-toggle="tab"
                       style="display:none">
                        {{ T_FRIENDS_CLUB }}
                    </a>
                </li>
                <li>
                    <a href="#list_users${tabId}" class="list_users_tab" data-toggle="tab"
                       style="display:none">
                        {{ T_USER_LIST }}
                    </a>
                </li>
            </ul>
        </div>

    </div>
    <div class="col-md-12 col-sm-12">
        <div class="tab-content tabs_content_wrapper">
            <!-- BEGIN TAB BOOKING ENGINE -->
            <div class="tab-pane fade active in" id="bookingEngine${tabId}" data-menu-item="booking_engine">
                <!-- Here goes iframe url for booking engine -->
                <div class="col-md-12 booking_engine_iframe_wrapper">
                     <iframe src="${iframeSrc}" allowfullscreen="" allow="xr-spatial-tracking" data-tab="${tabId}" class="col-md-12" style=""></iframe>
               </div>
            </div>
            <!-- END TAB BOOKING ENGINE -->

            <!-- BEGIN TAB FICHA -->
            <div class="tab-pane fade" id="info${tabId}" data-menu-item="hotel_info">
                <div class="col-md-12 links_wrapper">
                    {% raw %}
                    {{if ficha.toLowerCase().indexOf("javascript") === -1}}
                    {% endraw %}
                        <a class="fichaLink${tabId}" href="${ficha}" target="_blank">{{ T_OPEN_HOTEL_RECORD }}</a>
                    {% raw %}
                    {{/if}}
                    {% endraw %}
                    {% if update_hotel_info %}
                        <a href="javascript:HotelsInfo.addFichaToHotel('${hotelId}', ${tabId})">{{ T_UPDATE_HOTEL_RECORD }}</a>
                    {% endif %}
                </div>
                {% raw %}
                {{if ficha.toLowerCase().indexOf("javascript") === -1}}
                    <div class="col-md-12 iframe_wrapper">
                        <iframe data-src="${ficha}" allowfullscreen="" allow="xr-spatial-tracking" class="col-md-12" style="height: 900px"></iframe>
                    </div>
                {{/if}}
                {% endraw %}
            </div>
            <!-- END TAB FICHA -->

            <!-- BEGIN TAB MODIFY BOOKING -->
            <div class="tab-pane fade" id="modifyBooking${tabId}" data-menu-item="reservation_management">
                <!-- Here goes iframe url for booking engine -->
                <div class="col-md-12">

                    <div id="modify_fuerte${tabId}" style="display:none;padding:10px;">
                        <form id="modifyBookingFuerte">
                            <input type="text" id="modifyIdentifier${tabId}" placeholder="Identificador de reserva">

                            <div style="margin-top:20px">
                                <button type="submit" onclick="javascript:showConditions();return false;">
                                    {{ T_SHOW_CONDITIONS }}
                                </button>
                                <pre id="modifyResultPlaceholder${tabId}"
                                     style="padding:10px;min-height:100px;width:50%;margin-top:20px"></pre>
                            </div>
                            <div style="margin-top:20px">
                                <button type="submit" onclick="javascript:offerFuerteBooking();return false;">
                                    {{ T_CHECK_OFFERS }}
                                </button>
                                <pre id="offerResultPlaceholder${tabId}"
                                     style="padding:10px;min-height:20px;width:50%;margin-top:20px"></pre>
                            </div>

                            <div class="wrapper_offers">

                            </div>

                            <div style="margin-top:20px">
                                <button type="submit" onclick="javascript:cancelFuerteBooking();return false;">
                                    {{ T_CHECK_CANCELLATION }}
                                </button>
                                <pre id="cancelResultPlaceholder${tabId}"
                                     style="padding:10px;min-height:20px;width:50%;margin-top:20px"></pre>
                            </div>

                            <div class="wrapper_cancel">

                            </div>
                        </form>

                    </div>

                    <iframe id="iframe_modification${tabId}" data-src="${webIframeReservation}#wrapper_content"
                            class="col-md-12" style="height: 1200px;width:100%">

                    </iframe>
                </div>
            </div>


            <div class="tab-pane fade" id="clubAmigos${tabId}" data-menu-item="hotel_club">
                <!-- Here goes iframe url for offers at booking engine -->
                <div class="col-md-12">
                    <form id="sendEmail">
                        <input name="identifier" id="identification${tabId}" value="" placeholder="Identificador"/>
                        <input name="email" id="email${tabId}" value="" placeholder="Email"/>
                        <input name="telephone" id="telephone${tabId}" value="" placeholder="Teléfono"/>

                        <button type="submit" onclick="javascript:callFuerteAdapter();return false;">Buscar</button>
                    </form>
                    <pre id="resultPlaceholder${tabId}"
                         style="padding:10px;min-height:100px;width:90%;margin-top:20px"></pre>

                    <input name="club-idperson" type="hidden" class="booking-form-control"/>

                    <div class="booking-form-field" style="{% if hide_field_options %}display: none{% endif %}">
                        <label for="club-email" id="club-email" class="title"
                               style="margin-top: 4px;margin-bottom: 4px;width: 100%;display: block;">{{ T_EMAIL }}:</label>
                        <input name="club-email" type="text" class="booking-form-control"/>
                    </div>

                    <div class="booking-form-field" style="{% if hide_field_options %}display: none{% endif %}">
                        <label for="club-mobile" id="club-mobile" class="title"
                               style="margin-top: 4px;margin-bottom: 4px;width: 100%;display: block;">{{ T_MOBILE }}:</label>
                        <input name="club-mobile" type="text" class="booking-form-control"/>
                    </div>

                    <div class="booking-form-field" style="{% if hide_field_options %}display: none{% endif %}">
                        <label for="club-telephone" id="club-telephone" class="title"
                               style="margin-top: 4px;margin-bottom: 4px;width: 100%;display: block;">{{ T_PHONE }}:</label>
                        <input name="club-telephone" type="text" class="booking-form-control"/>
                    </div>

                    <input class="save_club_button" type="button" value="Guardar"
                           onclick="javascript:saveUserClubFuerte();return false;"
                           style="width:100px;height:30px;margin-top:6px;margin-bottom:10px;">


                </div>
            </div>

            <script>

                if (hotelData.hotelCode.includes("amare") || hotelData.hotelCode.includes("fuerte") || hotelData.hotelCode.includes("olee")) {
                    document.getElementById("linkClubAmigos${tabId}").style.display = "block";
                    document.getElementById("modify_fuerte${tabId}").style.display = "block";

                    let club_input = document.createElement("input");
                    club_input.setAttribute("type", "hidden");
                    club_input.classList.add("has_club");
                    club_input.setAttribute("value", "true");
                    document.getElementById("hotel_data_${tabId}").appendChild(club_input);

                    var elem = document.getElementById("iframe_modification${tabId}");
                    elem.parentNode.removeChild(elem);
                }


                function verifyFuerteOffer() {

                    data = {
                        'identifier': document.getElementById("modifyIdentifier${tabId}").value,
                    };

                    $.ajax({
                            url: 'https://fuerte-adapter.appspot.com/confirm_offer',
                            method: 'GET',
                            data: data,
                            success: function (data, textStatus) {
                                document.getElementById("modifyResultPlaceholder${tabId}").innerHTML = JSON.stringify(data, null, 2);
                            },
                            async: false
                        }
                    );
                }

                function showConditions() {
                    document.getElementById("modifyResultPlaceholder${tabId}").innerHTML = "Consultando...";

                    var identifier = document.getElementById("modifyIdentifier${tabId}").value;
                    var namespace = hotelData.hotelCode;


                    if (!identifier) {
                        document.getElementById("modifyResultPlaceholder${tabId}").innerHTML = "Localizador de reserva incorrecto";
                    }

                    $.ajax({
                        url: 'https://fuerte-adapter.appspot.com/show_conditions?identifier=' + identifier + "&hotel_code=" + namespace,
                        method: 'GET',
                        success: function (data, textStatus) {
                            document.getElementById("modifyResultPlaceholder${tabId}").innerHTML = JSON.stringify(data, null, 2);
                        },
                        error: function () {
                            document.getElementById("modifyResultPlaceholder${tabId}").innerHTML = "Algo ha ido mal. Consulte con el administrador por favor."
                        }
                    });

                }

                async function call_offer_confirm_all(identifier, namespace) {
                    var url = new URL("https://fuerte-adapter.appspot.com/confirm_offer");
                    params = {'identifier': identifier, 'namespace': namespace};
                    Object.keys(params).forEach(key => url.searchParams.append(key, params[key]));

                    const response = await fetch(url);

                    if (response.status == 200 && response.statusText == "") {
                        alert("Ofertas activadas");
                    } else {
                        alert("No se han podido activar las ofertas. Por favor, contacta con el administrador");
                    }

                }

                async function call_offer_confirm(identifier, namespace, key) {
                    var offer = $('#offer' + key).val();

                    var url = new URL("https://fuerte-adapter.appspot.com/confirm_offer");
                    params = {'identifier': identifier, 'namespace': namespace, 'strOfertas': offer};
                    Object.keys(params).forEach(key => url.searchParams.append(key, params[key]));

                    const response = await fetch(url);

                    if (response.status == 200 && response.statusText == "") {
                        alert("Oferta activada");
                    } else {
                        alert("No se ha podido activar la oferta. Por favor, contacta con el administrador");
                    }

                }

                async function call_cancel_multi_booking_all(identifier, namespace) {
                    var url = new URL("https://fuerte-adapter.appspot.com/cancel_booking");
                    params = {'identifier': identifier, 'namespace': namespace, 'tms': ''};
                    Object.keys(params).forEach(key => url.searchParams.append(key, params[key]));

                    const response = await fetch(url);

                    if (response.status == 200 && response.statusText == "") {
                        alert("Reserva cancelada correctamente");
                    } else {
                        alert("No se ha podido cancelar la reserva. Por favor, contacta con el administrador");
                    }

                }

                async function call_cancel_multi_booking(identifier, namespace, tms) {
                    var url = new URL("https://fuerte-adapter.appspot.com/cancel_booking");
                    params = {'identifier': identifier, 'namespace': namespace, 'tms': tms};
                    Object.keys(params).forEach(key => url.searchParams.append(key, params[key]));

                    const response = await fetch(url);

                    if (response.status == 200 && response.statusText == "") {
                        alert("Reserva cancelada correctamente");
                    } else {
                        alert("No se ha podido cancelar la reserva. Por favor, contacta con el administrador");
                    }

                }

                async function get_info_cancelation(identifier, namespace) {
                    var url = new URL("https://fuerte-adapter.appspot.com/get_multi_booking_info");
                    params = {'identifier': identifier, 'namespace': namespace};
                    Object.keys(params).forEach(key => url.searchParams.append(key, params[key]));

                    const response = await fetch(url);

                    let data = await response.json();

                    return data;

                }

                async function get_info_offers(identifier, namespace) {
                    var url = new URL("https://fuerte-adapter.appspot.com/get_offers_booking_info");
                    params = {'identifier': identifier, 'namespace': namespace};
                    Object.keys(params).forEach(key => url.searchParams.append(key, params[key]));

                    const response = await fetch(url);

                    let data = await response.json();

                    return data;

                }

                function addAllOffer(identifier, namespace) {

                    var input_all = $("<input/>", {
                        type: 'button',
                        class: 'button_offer button_offer_all',
                        value: "{{ T_VALIDATE_FULL_OFFER }}"
                    });

                    input_all.click(function () {
                        call_confirm_offer_all(identifier, namespace);
                    });

                    $('.wrapper_offers').append(input_all);

                }


                function addAllCancel(identifier, namespace) {

                    var input_all = $("<input/>", {
                        type: 'button',
                        class: 'button_cancel button_cancel_all',
                        value: "{{ T_CANCEL_FULL_RESERVATION }}"
                    });

                    input_all.click(function () {
                        call_cancel_multi_booking_all(identifier, namespace);
                    });

                    $('.wrapper_cancel').append(input_all);

                }

                function addItemCancel(item, identifier, namespace) {

                    var newNode = $("<div/>", {class: 'entry_cancel'});

                    var input = $("<input/>", {type: 'button', class: 'button_cancel', value: "Cancelar " + item.Id});
                    var pre = $("<pre/>", {class: 'pre_cancel'});
                    pre.html(JSON.stringify(item, null, 2));

                    input.click(function () {
                        call_cancel_multi_booking(identifier, namespace, item.Id);
                    });

                    if (!item.Id) {
                        input.prop("disabled", true);
                        $(".button_cancel_all").prop("disabled", true);
                    }

                    newNode.append(input);
                    newNode.append(pre);

                    $('.wrapper_cancel').append(newNode);

                }

                function addItemOffer(item, identifier, namespace, key) {
                    var newNode = $("<div/>", {class: 'entry_offer'});

                    var input = $("<input/>", {type: 'button', class: 'button_offer', value: "Validar"});
                    var pre = $("<pre/>", {class: 'pre_offer'});

                    pre.html(JSON.stringify(item.info, null, 2));

                    input.click(function () {
                        call_offer_confirm(identifier, namespace, key);
                    });

                    var input_hidden = $("<input/>", {
                        type: 'hidden',
                        id: "offer" + key,
                        class: 'text',
                        value: JSON.stringify(item.content, null, 2)
                    });

                    newNode.append(input);
                    newNode.append(pre);
                    newNode.append(input_hidden);

                    $('.wrapper_offers').append(newNode);

                }

                async function offerFuerteBooking() {
                    document.getElementById("offerResultPlaceholder${tabId}").innerHTML = "Consultando...";
                    $('.wrapper_offers').html("");

                    var identifier = document.getElementById("modifyIdentifier${tabId}").value;
                    var namespace = hotelData.hotelCode;

                    if (!identifier) {
                        document.getElementById("offerResultPlaceholder${tabId}").innerHTML = "Localizador de reserva incorrecto";
                        return;
                    }

                    var info = await get_info_offers(identifier, namespace);
                    console.log(info);

                    addAllOffer(identifier, namespace);
                    $.each(info, function (key, value) {
                        console.log(key + ": " + value);
                        addItemOffer(value, identifier, namespace, key);

                        document.getElementById("offerResultPlaceholder${tabId}").innerHTML = "Consultando...OK";
                    });

                }

                async function cancelFuerteBooking() {
                    document.getElementById("cancelResultPlaceholder${tabId}").innerHTML = "Consultando...";
                    $('.wrapper_cancel').html("");

                    var identifier = document.getElementById("modifyIdentifier${tabId}").value;
                    var namespace = hotelData.hotelCode;

                    if (!identifier) {
                        document.getElementById("cancelResultPlaceholder${tabId}").innerHTML = "Localizador de reserva incorrecto";
                        return;
                    }

                    var info = await get_info_cancelation(identifier, namespace);
                    console.log(info);

                    addAllCancel(identifier, namespace);
                    $.each(info, function (key, value) {
                        console.log(key + ": " + value);
                        addItemCancel(value, identifier, namespace);

                        document.getElementById("cancelResultPlaceholder${tabId}").innerHTML = "Consultando...OK";
                    });


                }

                function saveUserClubFuerte() {

                    url = "https://fuerte-club.appspot.com/utils/save_person";

                    var email = $('[name=club-email]').val();
                    var regex = /^([a-zA-Z0-9_.+-])+\@(([a-zA-Z0-9-])+\.)+([a-zA-Z0-9]{2,4})+$/;
                    if (!regex.test(email)) {
                        alert("Email incorrecto");
                        return;
                    }

                    var data = $('[name=club-idperson]').val().split(",");
                    if (data.length > 1) {
                        response = confirm("{{ T_REPEATED_MEMBER_RECORD }}");
                        if (!response) return;
                    }

                    error = false;

                    $.each(data, function (i, datainfo) {
                        if (datainfo == "") return;
                        var fd = new FormData();
                        fd.append('idperson', datainfo);
                        fd.append('email', $('[name=club-email]').val());
                        fd.append('telephone', $('[name=club-telephone]').val());
                        fd.append('mobile', $('[name=club-mobile]').val());

                        $.ajax({
                            url: url,
                            type: 'POST',
                            data: fd,
                            async: false,
                            cache: false,
                            contentType: false,
                            processData: false,
                            success: function (data) {

                                data = JSON.parse(data);

                                if (data.status != 'ok') {

                                    error = true;

                                }
                            }
                        });

                    });

                    if (error) {
                        alert("{{ T_PROBLEM_SAVING_DATA }}");
                    } else {
                        alert("{{ T_DATA_SAVED_SUCCESSFULLY }}");
                    }

                }

                function callFuerteAdapter() {
                    data = {
                        'identification': document.getElementById("identification${tabId}").value,
                        'email': document.getElementById("email${tabId}").value,
                        'telephone': document.getElementById("telephone${tabId}").value
                    };

                    $("[name=club-idperson]").val("");
                    $("[name=club-email]").val("");
                    $("[name=club-mobile]").val("");
                    $("[name=club-telephone]").val("");

                    $.ajax({
                            url: 'https://fuerte-adapter.appspot.com/find_persona',
                            method: 'GET',
                            data: data,
                            success: function (data, textStatus) {
                                if (data.length > 0) {
                                    $.each(data, function (i, datainfo) {

                                        id = datainfo["nivel/idCliente"].split("/")[1];
                                        var content = $("[name=club-idperson]").val();
                                        content += (id + ",");
                                        $("[name=club-idperson]").val(content);
                                        email = datainfo["email"];
                                        $("[name=club-email]").val(email);
                                        mobile = datainfo["mobile"];
                                        $("[name=club-mobile]").val(mobile);
                                        telephone = datainfo["telephone"];
                                        $("[name=club-telephone]").val(telephone);

                                    });
                                }
                                document.getElementById("resultPlaceholder${tabId}").innerHTML = JSON.stringify(data, null, 2);

                            },
                            async: false
                        }
                    );
                }

            </script>

            <!-- END TAB OFFERS -->
        </div>
    </div>
</div>