<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
</head>
<body>
    <h1>Resumen de llamadas por agente:</h1>
    <h2>Total del día: {{ date }}</h2>
    <table>
        <tr>
            <th style="color:white; background:#2261a8; padding:5px">Agente</th>
            <th style="color:white; background:#2261a8; padding:5px">Calls Recibidas</th>
            <th style="color:white; background:#2261a8; padding:5px">Calls Contestadas</th>
            <th style="color:white; background:#2261a8; padding:5px"><PERSON><PERSON> Servicio (%)</th>
            <th style="color:white; background:#2261a8; padding:5px">TMO (s)</th>
            <th style="color:white; background:#2261a8; padding:5px">Reservas</th>
            <th style="color:white; background:#2261a8; padding:5px">Revenue (€)</th>
            <th style="color:white; background:#2261a8; padding:5px">Abor (€)</th>
            <th style="color:white; background:#2261a8; padding:5px">Conversion Recibidas (%)</th>
            <th style="color:white; background:#2261a8; padding:5px">Conversion Contestadas (%)</th>
            <th style="color:white; background:#2261a8; padding:5px">Conversion Contestadas Abor (€)</th>
        </tr>

        {% for key, value in day.items() %}
            <tr {% if loop.index is divisibleby 2 %}style="background-color: #EFEFEF;"{% endif %}>
                <td style="padding:15px; text-align:left;">{{ value.description }}</td>
                <td style="padding:15px; text-align:right;">{{ value.incoming_calls }}</td>
                <td style="padding:15px; text-align:right;">{{ value.answer_calls }}</td>
                <td style="padding:15px; text-align:right;">{{ value.service_level }}</td>
                <td style="padding:15px; text-align:right;">{{ value.TMO }}</td>
                <td style="padding:15px; text-align:right;">{{ value.reservations }}</td>
                <td style="padding:15px; text-align:right;">{{ value.revenue }}</td>
                <td style="padding:15px; text-align:right;">{{ value.abor }}</td>
                <td style="padding:15px; text-align:right;">{{ value.conversion_incoming }}</td>
                <td style="padding:15px; text-align:right;">{{ value.conversion_answer }}</td>
                <td style="padding:15px; text-align:right;">{{ value.conversion_answer_abor }}</td>
            </tr>
        {% endfor %}

        <tr style="background-color: #EFEFEF;">
            <td style="padding:15px; text-align:right; font-weight:bold; background:#FFDDB2;">Totales: </td>
            <td style="padding:15px; text-align:right; background:#FFDDB2;">{{ day_total.incoming_calls }}</td>
            <td style="padding:15px; text-align:right; background:#FFDDB2;">{{ day_total.answer_calls }}</td>
            <td style="padding:15px; text-align:right; background:#FFDDB2;">{{ day_total.service_level }}</td>
            <td style="padding:15px; text-align:right; background:#FFDDB2;">{{ day_total.TMO }}</td>
            <td style="padding:15px; text-align:right; background:#FFDDB2;">{{ day_total.reservations }}</td>
            <td style="padding:15px; text-align:right; background:#FFDDB2;">{{ day_total.revenue }}</td>
            <td style="padding:15px; text-align:right; background:#FFDDB2;">{{ day_total.abor }}</td>
            <td style="padding:15px; text-align:right; background:#FFDDB2;">{{ day_total.conversion_incoming }}</td>
            <td style="padding:15px; text-align:right; background:#FFDDB2;">{{ day_total.conversion_answer }}</td>
            <td style="padding:15px; text-align:right; background:#FFDDB2;">{{ day_total.conversion_answer_abor }}</td>
        </tr>

    </table>

    <h2>Total mensual: {{ date_month }}</h2>
    <table>
        <tr>
            <th style="color:white; background:#2261a8; padding:5px">Agente</th>
            <th style="color:white; background:#2261a8; padding:5px">Calls Recibidas</th>
            <th style="color:white; background:#2261a8; padding:5px">Calls Contestadas</th>
            <th style="color:white; background:#2261a8; padding:5px">Nivel Servicio (%)</th>
            <th style="color:white; background:#2261a8; padding:5px">TMO (s)</th>
            <th style="color:white; background:#2261a8; padding:5px">Reservas</th>
            <th style="color:white; background:#2261a8; padding:5px">Revenue (€)</th>
            <th style="color:white; background:#2261a8; padding:5px">Abor (€)</th>
            <th style="color:white; background:#2261a8; padding:5px">Conversion Recibidas (%)</th>
            <th style="color:white; background:#2261a8; padding:5px">Conversion Contestadas (%)</th>
            <th style="color:white; background:#2261a8; padding:5px">Conversion Contestadas Abor (€)</th>
        </tr>

        {% for key, value in month.items() %}
            <tr {% if loop.index is divisibleby 2 %}style="background-color: #EFEFEF;"{% endif %}>
                <td style="padding:15px; text-align:left;">{{ value.description }}</td>
                <td style="padding:15px; text-align:right;">{{ value.incoming_calls }}</td>
                <td style="padding:15px; text-align:right;">{{ value.answer_calls }}</td>
                <td style="padding:15px; text-align:right;">{{ value.service_level }}</td>
                <td style="padding:15px; text-align:right;">{{ value.TMO }}</td>
                <td style="padding:15px; text-align:right;">{{ value.reservations }}</td>
                <td style="padding:15px; text-align:right;">{{ value.revenue }}</td>
                <td style="padding:15px; text-align:right;">{{ value.abor }}</td>
                <td style="padding:15px; text-align:right;">{{ value.conversion_incoming }}</td>
                <td style="padding:15px; text-align:right;">{{ value.conversion_answer }}</td>
                <td style="padding:15px; text-align:right;">{{ value.conversion_answer_abor }}</td>
            </tr>
        {% endfor %}

        <tr style="background-color: #EFEFEF;">
            <td style="padding:15px; text-align:right; font-weight:bold; background:#FFDDB2;">Totales: </td>
            <td style="padding:15px; text-align:right; background:#FFDDB2;">{{ month_total.incoming_calls }}</td>
            <td style="padding:15px; text-align:right; background:#FFDDB2;">{{ month_total.answer_calls }}</td>
            <td style="padding:15px; text-align:right; background:#FFDDB2;">{{ month_total.service_level }}</td>
            <td style="padding:15px; text-align:right; background:#FFDDB2;">{{ month_total.TMO }}</td>
            <td style="padding:15px; text-align:right; background:#FFDDB2;">{{ month_total.reservations }}</td>
            <td style="padding:15px; text-align:right; background:#FFDDB2;">{{ month_total.revenue }}</td>
            <td style="padding:15px; text-align:right; background:#FFDDB2;">{{ month_total.abor }}</td>
            <td style="padding:15px; text-align:right; background:#FFDDB2;">{{ month_total.conversion_incoming }}</td>
            <td style="padding:15px; text-align:right; background:#FFDDB2;">{{ month_total.conversion_answer }}</td>
            <td style="padding:15px; text-align:right; background:#FFDDB2;">{{ month_total.conversion_answer_abor }}</td>
        </tr>

    </table>

    <h2>Total anual: {{ date_year }}</h2>
    <table>
        <tr>
            <th style="color:white; background:#2261a8; padding:5px">Agente</th>
            <th style="color:white; background:#2261a8; padding:5px">Calls Recibidas</th>
            <th style="color:white; background:#2261a8; padding:5px">Calls Contestadas</th>
            <th style="color:white; background:#2261a8; padding:5px">Nivel Servicio (%)</th>
            <th style="color:white; background:#2261a8; padding:5px">TMO (s)</th>
            <th style="color:white; background:#2261a8; padding:5px">Reservas</th>
            <th style="color:white; background:#2261a8; padding:5px">Revenue (€)</th>
            <th style="color:white; background:#2261a8; padding:5px">Abor (€)</th>
            <th style="color:white; background:#2261a8; padding:5px">Conversion Recibidas (%)</th>
            <th style="color:white; background:#2261a8; padding:5px">Conversion Contestadas (%)</th>
            <th style="color:white; background:#2261a8; padding:5px">Conversion Contestadas Abor (%)</th>
        </tr>

        {% for key, value in year.items() %}
            <tr {% if loop.index is divisibleby 2 %}style="background-color: #EFEFEF;"{% endif %}>
                <td style="padding:15px; text-align:left;">{{ value.description }}</td>
                <td style="padding:15px; text-align:right;">{{ value.incoming_calls }}</td>
                <td style="padding:15px; text-align:right;">{{ value.answer_calls }}</td>
                <td style="padding:15px; text-align:right;">{{ value.service_level }}</td>
                <td style="padding:15px; text-align:right;">{{ value.TMO }}</td>
                <td style="padding:15px; text-align:right;">{{ value.reservations }}</td>
                <td style="padding:15px; text-align:right;">{{ value.revenue }}</td>
                <td style="padding:15px; text-align:right;">{{ value.abor }}</td>
                <td style="padding:15px; text-align:right;">{{ value.conversion_incoming }}</td>
                <td style="padding:15px; text-align:right;">{{ value.conversion_answer }}</td>
                <td style="padding:15px; text-align:right;">{{ value.conversion_answer_abor }}</td>
            </tr>
        {% endfor %}

        <tr style="background-color: #EFEFEF;">
            <td style="padding:15px; text-align:right; font-weight:bold; background:#FFDDB2;">Totales: </td>
            <td style="padding:15px; text-align:right; background:#FFDDB2;">{{ year_total.incoming_calls }}</td>
            <td style="padding:15px; text-align:right; background:#FFDDB2;">{{ year_total.answer_calls }}</td>
            <td style="padding:15px; text-align:right; background:#FFDDB2;">{{ year_total.service_level }}</td>
            <td style="padding:15px; text-align:right; background:#FFDDB2;">{{ year_total.TMO }}</td>
            <td style="padding:15px; text-align:right; background:#FFDDB2;">{{ year_total.reservations }}</td>
            <td style="padding:15px; text-align:right; background:#FFDDB2;">{{ year_total.revenue }}</td>
            <td style="padding:15px; text-align:right; background:#FFDDB2;">{{ year_total.abor }}</td>
            <td style="padding:15px; text-align:right; background:#FFDDB2;">{{ year_total.conversion_incoming }}</td>
            <td style="padding:15px; text-align:right; background:#FFDDB2;">{{ year_total.conversion_answer }}</td>
            <td style="padding:15px; text-align:right; background:#FFDDB2;">{{ year_total.conversion_answer_abor }}</td>
        </tr>

    </table>


</body>
</html>



