<!DOCTYPE html>
<!--[if IE 8]> <html lang="en" class="ie8 no-js"> <![endif]-->
<!--[if IE 9]> <html lang="en" class="ie9 no-js"> <![endif]-->
<!--[if !IE]><!-->
<html lang="en" class="no-js">
<!--<![endif]-->
<!-- BEGIN HEAD -->
<head>
    {% include "_common_head.html" %}

     <style>
        .clickable:hover{
           cursor: pointer;
        }

        .session_alert {
            width: 100%;
            height: 100vh;
            position: absolute;
            background-color: rgba(49, 48, 59, 0.8);
            vertical-align: middle;
            z-index: 3;
            margin: 0;
            display: none;
        }
        .response_ko {
            border-radius: 10px!important;
            width: 30%;
            text-align: center;
            display: flex;
            flex-direction: column;
            font-size: 2rem;
            background: white;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            box-shadow: 0 0 20px rgba(255, 128, 0, 0.90);
            padding: 5px;
        }
    </style>

    <link rel="stylesheet" type="text/css" href="/static/template/assets/plugins/bootstrap-wysihtml5/bootstrap-wysihtml5.css"/>
    <link rel="stylesheet" type="text/css" href="/static/template/assets/plugins/bootstrap-markdown/css/bootstrap-markdown.min.css">
</head>
<!-- END HEAD -->

<!-- BEGIN BODY -->
<body class="page-header-fixed page-sidebar-fixed">
    {% include "_common_popup_error.html" %}
    {% include "_common_header.html" %}
    <input type="hidden" id="session_id" value="{{ session.session_id }}">

    <div id="template_client_tab" style="display: none">
        <div class="tab_client_content">
            <div class="row">
                 <div class="tabbable tabbable-custom tabbable-full-width">
                    <div class="hoteltabs-container-wrapper">
                        <div class="flex_container">
                            <div class="search_box">
                                <div class="input-group" style="padding-top: 1px;">
                                    <select class="form-control hotels_selector" name="hotels" data-placeholder="{{ T_SEARCH_HOTEL }}..." onchange="HotelsInfo.loadNewHotel(this)">
                                        <option value=""></option>
                                        {% for hotel in session.user.hotels %}
                                            <option value="{{hotel.key}}" data-namespace="{{ hotel.namespace }}">{{hotel.name}}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <ul class="nav nav-tabs hoteltabs-container">

                            </ul>
                        </div>
                        {# <div class="hoteltabs_mode_toggle" id="hoteltabs_mode_toggle" style="display: none;">
                            <i class="fas fa-angle-up"></i>
                        </div> #}
                        <div class="scroll_menu_fixed">
                            <div class="left_wrapper">
                                <div class="scroll_option" data-target="bookingConfig">
                                    1. {{ T_BOOKING_CONFIGURATION }}
                                </div>
                                <div class="scroll_option" data-target="bookingProcess">
                                    2. {{ T_BOOKING_PROCESS }}
                                </div>
                                <div class="scroll_option" data-target="advanceOptions">
                                    3. {{ T_ADVANCED_OPTIONS }}
                                </div>
                            </div>
                            <div class="reset_search_btn">
                                {{ T_CLEAR_SEARCH }}
                            </div>
                        </div>
                    </div>

                    <div class="progress hotel_selection">
                      <div class="progress-bar" role="progressbar" aria-valuenow="70"
                      aria-valuemin="0" aria-valuemax="100">
                        <span class="sr-only">70% Complete</span>
                      </div>
                    </div>

                    <div class="zendesk_wrapper"></div>

                    <div class="pushtech_wrapper"></div>

                    <div class="fideltour_wrapper"></div>

                    <div class="alert_block noHotelsMsg">
                        <div class="alert alert-block alert-info fade in">
                            <h4 class="alert-heading">{{ T_SELECT_HOTEL }}</h4>
                            <p>
                                 {{ T_SELECT_HOTEL_TO_ACCESS_INFO }}
                            </p>
                        </div>
                    </div>

                    <div class="tab-content hoteltabs-content">

                    </div>
                </div>
                <!--end tabbable-->
            </div>
        </div>
    </div>

    {% include 'zendesk.html' %}

    {% include 'pushtech.html' %}

    {% include 'fideltour.html' %}


    <!-- BEGIN CONTAINER -->
    <div class="page-container">
         {% include "_common_sidebar.html" %}

        <!-- BEGIN CONTENT -->
        <div class="page-content-wrapper">
            <div class="page-content">
                <!-- Begin Main content -->
                <div class="client-tab-content" id="clienttabs-content" ></div>
            </div>
		</div>

    </div>
    <!-- END CONTAINER -->

    {% include "_common_footer.html" %}

    {% include "_common_core_plugins.html" %}

    <!-- BEGIN PAGE LEVEL PLUGINS -->
    <script type="text/javascript" src="/static/template/assets/plugins/ckeditor/ckeditor.js"></script>
    <script type="text/javascript" src="/static/template/assets/plugins/bootstrap-wysihtml5/wysihtml5-0.3.0.js"></script>
    <script type="text/javascript" src="/static/template/assets/plugins/bootstrap-wysihtml5/bootstrap-wysihtml5.js"></script>
    <script src="/static/template/assets/plugins/bootstrap-markdown/lib/markdown.js" type="text/javascript"></script>
    <script src="/static/template/assets/plugins/bootstrap-markdown/js/bootstrap-markdown.js" type="text/javascript"></script>
    <script src="https://cdn.jsdelivr.net/npm/jquery-validation@1.19.5/dist/jquery.validate.min.js" type="text/javascript"></script>
    <!-- END PAGE LEVEL PLUGINS -->

    <!-- BEGIN PAGE LEVEL SCRIPTS -->
    <script src="/static/js/prebooking.js?v=1.08" type="text/javascript"></script>
    <script src="/static/js/zendesk.js?v=1.05" type="text/javascript"></script>
    <script src="/static/js/pushtech.js?v=1.07" type="text/javascript"></script>
    <script src="/static/js/fideltour.js?v=1.01" type="text/javascript"></script>
    <script src="/static/js/hotelInfo.js?v=1.14" type="text/javascript"></script>
    <!-- END PAGE LEVEL SCRIPTS -->

    <script>
        jQuery(document).ready(function() {
           App.init();
           ZendeskController.init();
           PushtechController.init();
           FideltourController.init();
           HotelsInfo.init();
        });
    </script>

    {% if enable_chat %}
        <!-- BEGIN SLAASK CHAT -->
        <script>
            window._slaaskSettings = {
                identify: function() {
                    return {
                        id: "{{session.user.name}}",
                        name: "{{session.user.name}}",
                    }
                },
                key: "a408a906cbc916b35260e80f50426e08",
            };
        </script>
        <script src="https://cdn.slaask.com/chat_loader.js" async="async"></script>
        <!-- END SLAASK CHAT -->
    {% endif %}

<!-- END JAVASCRIPTS -->
</body>
<!-- END BODY -->
</html>