
import pymysql
import csv
import sys

csv_file_path = "data.csv"

db_opts = {
    'user': 'root',
    'host': '*************',
    'database': 'asteriskcdrdb'
}

db = pymysql.connect(**db_opts)
cur = db.cursor()

try:

  mycursor = db.cursor()

  sql = "SELECT * FROM cdr"
  mycursor.execute(sql)
  rows = mycursor.fetchall()

finally:
    db.close()

if rows:

    result = list()

    column_names = list()
    for i in mycursor.description:
        column_names.append(i[0])

    result.append(column_names)
    for row in rows:
        result.append(row)

    with open(csv_file_path, 'w') as csvfile:
        csvwriter = csv.writer(csvfile, delimiter=',', quotechar='"', quoting=csv.QUOTE_MINIMAL, lineterminator='\n')
        for row in result:
            csvwriter.writerow(row)

else:
    sys.exit("No rows found for query: {}".format(sql))




