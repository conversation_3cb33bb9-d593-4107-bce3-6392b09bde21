/* line 8, ../../../sass/_defaults.scss */
.btn_1, .btn_2, .btn_3 {
  appearance: none;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 200px;
  height: 40px;
  border: none;
  border-radius: 4px;
  background: none;
  font: inherit;
  font-size: 18px;
  letter-spacing: 0.45px;
  text-align: center;
  cursor: pointer;
  transition: opacity 0.3s;
}
/* line 25, ../../../sass/_defaults.scss */
.btn_1:hover, .btn_2:hover, .btn_3:hover {
  opacity: 0.7;
}

/* line 30, ../../../sass/_defaults.scss */
.btn_1 {
  background-color: #F28E2A;
  color: white;
}

/* line 35, ../../../sass/_defaults.scss */
.btn_2 {
  border: 1px solid #F28E2A;
  background-color: white;
  color: #F28E2A;
}

/* line 41, ../../../sass/_defaults.scss */
.btn_3 {
  height: auto;
  font-size: 16px;
  color: #A5A4BF;
  letter-spacing: 0.4px;
  text-decoration: underline;
}

/* line 1, ../../../sass/booking_widget/_general.scss */
* {
  box-sizing: border-box;
}

/* line 5, ../../../sass/booking_widget/_general.scss */
body {
  height: 100%;
  display: flex;
  flex-direction: column;
  font-family: "Source Sans Pro", sans-serif;
  font-weight: 400;
  font-size: 0.8em;
  letter-spacing: 0;
  -webkit-font-smoothing: antialiased;
}

/* line 16, ../../../sass/booking_widget/_general.scss */
.booking_widget_wrapper {
  padding-bottom: 50px;
  margin: 0 15px 50px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

/* line 22, ../../../sass/booking_widget/_general.scss */
.block_title {
  width: 100%;
  margin-bottom: 20px;
  font-weight: 600;
  font-size: 20px;
  line-height: 20px;
  color: #4D4F5C;
}

/* line 31, ../../../sass/booking_widget/_general.scss */
.input_wrapper, .checkbox_wrapper {
  width: calc((100% - 20px) / 2);
  min-height: 40px;
}

/* line 36, ../../../sass/booking_widget/_general.scss */
#changes-pending {
  background: #ffbf6a;
  display: table;
  padding: 10px 20px;
}
/* line 41, ../../../sass/booking_widget/_general.scss */
#changes-pending.hidden {
  display: none;
}

/* line 46, ../../../sass/booking_widget/_general.scss */
.input_wrapper {
  margin-bottom: 40px;
  position: relative;
  border-radius: 4px;
  border: 1px solid #D7DAE2;
  background-color: white;
  box-shadow: 0 2px 3px rgba(0, 0, 0, 0.05);
}
/* line 54, ../../../sass/booking_widget/_general.scss */
.input_wrapper label {
  display: inline-block;
  position: absolute;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
  margin-left: 10px;
  padding: 0 4px;
  background-color: white;
  font-size: 16px;
  white-space: nowrap;
  pointer-events: none;
  transition: all 0.3s;
}
/* line 69, ../../../sass/booking_widget/_general.scss */
.input_wrapper input, .input_wrapper select {
  width: 100%;
  height: 100%;
  border: none;
  background: none;
  outline: none;
  padding: 0 10px;
}
/* line 78, ../../../sass/booking_widget/_general.scss */
.input_wrapper input:-webkit-autofill {
  -webkit-box-shadow: 0 0 0 30px white inset !important;
  border-radius: 4px;
}
/* line 82, ../../../sass/booking_widget/_general.scss */
.input_wrapper input:-webkit-autofill:hover, .input_wrapper input:-webkit-autofill:focus, .input_wrapper input:-webkit-autofill:active {
  -webkit-box-shadow: 0 0 0 30px white inset !important;
  border-radius: 4px;
}
/* line 88, ../../../sass/booking_widget/_general.scss */
.input_wrapper input {
  font-family: "Source Sans Pro", sans-serif;
  font-size: 16px;
}
/* line 93, ../../../sass/booking_widget/_general.scss */
.input_wrapper select {
  font-family: "Source Sans Pro", sans-serif;
  font-size: 16px;
  appearance: none;
}
/* line 99, ../../../sass/booking_widget/_general.scss */
.input_wrapper.with_selector:after {
  content: '\f078';
  font-family: "Font Awesome 5 Pro";
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  font-size: 15px;
  right: 10px;
  color: #A4AFB7;
}
/* line 110, ../../../sass/booking_widget/_general.scss */
.input_wrapper.active label {
  top: 0;
  font-size: 12px;
}

/* line 116, ../../../sass/booking_widget/_general.scss */
.checkbox_wrapper {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  align-self: flex-start;
}
/* line 122, ../../../sass/booking_widget/_general.scss */
.checkbox_wrapper input[type='checkbox'] {
  display: none;
}
/* line 126, ../../../sass/booking_widget/_general.scss */
.checkbox_wrapper .custom_checkbox {
  position: relative;
  width: 26px;
  height: 26px;
  min-width: 26px;
  min-height: 26px;
  margin: 7px 10px 10px 0;
  border: 2px solid #F08325;
  border-radius: 4px;
  cursor: pointer;
}
/* line 137, ../../../sass/booking_widget/_general.scss */
.checkbox_wrapper .custom_checkbox::before {
  content: '\f00c';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-family: "Font Awesome 5 Pro";
  font-weight: 600;
  font-size: 14px;
  color: #F08325;
  opacity: 0;
  transition: opacity 0.3s;
}
/* line 152, ../../../sass/booking_widget/_general.scss */
.checkbox_wrapper input[type='checkbox']:checked + .custom_checkbox::before {
  opacity: 1;
}
/* line 156, ../../../sass/booking_widget/_general.scss */
.checkbox_wrapper .checkbox_label {
  flex: 1;
  font-size: 12px;
  line-height: 18px;
  align-self: center;
  cursor: pointer;
}

/* line 165, ../../../sass/booking_widget/_general.scss */
.stay_selection {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
/* line 171, ../../../sass/booking_widget/_general.scss */
.stay_selection::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translateY(-50%);
  width: 1px;
  height: 30px;
  background-color: #EBEBF2;
}

/* line 184, ../../../sass/booking_widget/_general.scss */
input[type="number"] {
  max-width: 25px;
  border: none;
  text-align: center;
  font-size: 16px;
}
/* line 191, ../../../sass/booking_widget/_general.scss */
input[type=number] {
  -moz-appearance: textfield;
}
/* line 192, ../../../sass/booking_widget/_general.scss */
input[type=number]::-webkit-inner-spin-button, input[type=number]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* line 201, ../../../sass/booking_widget/_general.scss */
.progress {
  width: 400px;
  margin: 20px 30px;
  border: 1px solid #F28E2A;
  background: white;
  height: 25px;
  display: none;
}
/* line 209, ../../../sass/booking_widget/_general.scss */
.progress .progress-bar {
  background: #F28E2A;
  box-shadow: none;
  height: 100%;
  transition: width .6s ease;
}

/* line 217, ../../../sass/booking_widget/_general.scss */
.button_number {
  background-color: white;
  border: none;
  color: black;
  font-size: 16px;
  cursor: pointer;
}

/* line 1, ../../../sass/booking_widget/_breadcrumbs.scss */
.breadcrumbs_wrapper {
  display: none;
  justify-content: center;
  align-items: center;
  margin: 15px 0;
}
/* line 7, ../../../sass/booking_widget/_breadcrumbs.scss */
.breadcrumbs_wrapper .step {
  background-color: #ffbb77;
  font-size: 16px;
  letter-spacing: 0.45px;
  padding: 7px 70px;
  color: white;
  font-weight: 600;
  text-transform: uppercase;
  position: relative;
}
/* line 17, ../../../sass/booking_widget/_breadcrumbs.scss */
.breadcrumbs_wrapper .step:after, .breadcrumbs_wrapper .step:before {
  position: absolute;
  left: 100%;
  right: 0;
  bottom: 0;
  content: "";
  z-index: 9;
  width: 0;
  height: 0;
  border-left: 17px solid #ffbb77;
  border-right: 17px solid transparent;
  border-bottom: 17px solid transparent;
  border-top: 17px solid transparent;
}
/* line 32, ../../../sass/booking_widget/_breadcrumbs.scss */
.breadcrumbs_wrapper .step:before {
  transform: translate(2px, 0);
  border-left: 17px solid white;
}
/* line 37, ../../../sass/booking_widget/_breadcrumbs.scss */
.breadcrumbs_wrapper .step.active {
  background-color: #F28E2A;
  cursor: pointer;
}
/* line 41, ../../../sass/booking_widget/_breadcrumbs.scss */
.breadcrumbs_wrapper .step.active:after {
  border-left: 17px solid #F28E2A;
}
/* line 45, ../../../sass/booking_widget/_breadcrumbs.scss */
.breadcrumbs_wrapper .step.active:before {
  display: none;
}
/* line 51, ../../../sass/booking_widget/_breadcrumbs.scss */
.breadcrumbs_wrapper .step:last-child:after {
  display: none;
}
/* line 57, ../../../sass/booking_widget/_breadcrumbs.scss */
.breadcrumbs_wrapper.active {
  display: flex;
}

/* line 1, ../../../sass/booking_widget/_booking_form.scss */
.search_form_block {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  align-content: space-between;
  margin-top: 20px;
  gap: 20px;
  flex-wrap: wrap;
}
/* line 10, ../../../sass/booking_widget/_booking_form.scss */
.search_form_block .switches_wrapper {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 40px;
}
/* line 15, ../../../sass/booking_widget/_booking_form.scss */
.search_form_block .switches_wrapper .switch_element_wrapper {
  position: relative;
  box-sizing: border-box;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
}
/* line 23, ../../../sass/booking_widget/_booking_form.scss */
.search_form_block .switches_wrapper .switch_element_wrapper .label {
  font-size: 16px;
  letter-spacing: 0.45px;
}
/* line 28, ../../../sass/booking_widget/_booking_form.scss */
.search_form_block .switches_wrapper .switch_element_wrapper .switch {
  position: relative;
  width: 68px;
  height: 33px;
  overflow: visible;
  border-radius: 100px;
}
/* line 35, ../../../sass/booking_widget/_booking_form.scss */
.search_form_block .switches_wrapper .switch_element_wrapper .switch .checkbox {
  position: relative;
  width: 100%;
  height: 100%;
  padding: 0;
  margin: 0;
  opacity: 0;
  cursor: pointer;
  z-index: 3;
}
/* line 45, ../../../sass/booking_widget/_booking_form.scss */
.search_form_block .switches_wrapper .switch_element_wrapper .switch .checkbox:checked:active + .knobs:before {
  margin-left: -26px;
}
/* line 49, ../../../sass/booking_widget/_booking_form.scss */
.search_form_block .switches_wrapper .switch_element_wrapper .switch .checkbox:checked + .knobs:before {
  content: "\f00c";
  right: 3px;
  background-color: #F28E2A;
}
/* line 55, ../../../sass/booking_widget/_booking_form.scss */
.search_form_block .switches_wrapper .switch_element_wrapper .switch .checkbox:checked ~ .layer {
  background-color: transparent;
}
/* line 60, ../../../sass/booking_widget/_booking_form.scss */
.search_form_block .switches_wrapper .switch_element_wrapper .switch .knobs, .search_form_block .switches_wrapper .switch_element_wrapper .switch .layer {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}
/* line 68, ../../../sass/booking_widget/_booking_form.scss */
.search_form_block .switches_wrapper .switch_element_wrapper .switch .knobs {
  z-index: 2;
}
/* line 71, ../../../sass/booking_widget/_booking_form.scss */
.search_form_block .switches_wrapper .switch_element_wrapper .switch .knobs:before {
  content: "\f00d";
  font-family: "Font Awesome 5 Pro";
  position: absolute;
  top: 3px;
  left: auto;
  right: 37px;
  width: 27px;
  height: 27px;
  box-sizing: border-box;
  color: white;
  background-color: #92714f;
  font-size: 14px;
  font-weight: bold;
  text-align: center;
  line-height: 0.7;
  padding: 11px 4px;
  border-radius: 50%;
  transition: 0.3s ease all, left 0.3s cubic-bezier(0.18, 0.89, 0.35, 1.15);
}
/* line 93, ../../../sass/booking_widget/_booking_form.scss */
.search_form_block .switches_wrapper .switch_element_wrapper .switch .layer {
  width: 100%;
  background-color: rgba(146, 113, 79, 0.2);
  border: 1px solid #F28E2A;
  transition: 0.3s ease all;
  z-index: 1;
  border-radius: 100px;
}
/* line 104, ../../../sass/booking_widget/_booking_form.scss */
.search_form_block .top_inputs_wrapper {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 40px;
  position: relative;
}
/* line 110, ../../../sass/booking_widget/_booking_form.scss */
.search_form_block .top_inputs_wrapper:before {
  content: "";
  width: calc(100% - 325px);
  height: 1px;
  background: #E9E9F0;
  position: absolute;
  bottom: -80px;
  left: 15px;
}
/* line 119, ../../../sass/booking_widget/_booking_form.scss */
.search_form_block .top_inputs_wrapper .flight_hotel_selector_wrapper {
  font-size: 16px;
  margin-bottom: 24px;
}
/* line 122, ../../../sass/booking_widget/_booking_form.scss */
.search_form_block .top_inputs_wrapper .flight_hotel_selector_wrapper label {
  z-index: 1;
}
/* line 126, ../../../sass/booking_widget/_booking_form.scss */
.search_form_block .top_inputs_wrapper .flight_hotel_selector_wrapper .select2.select2-container .select2-selection {
  padding: 5px 8px 4px;
  border: none;
}
/* line 130, ../../../sass/booking_widget/_booking_form.scss */
.search_form_block .top_inputs_wrapper .flight_hotel_selector_wrapper .select2.select2-container .select2-selection__arrow {
  display: none;
}

/* line 138, ../../../sass/booking_widget/_booking_form.scss */
.comments_wrapper {
  position: relative;
  width: 100%;
}
/* line 142, ../../../sass/booking_widget/_booking_form.scss */
.comments_wrapper #comments {
  width: 100%;
  height: 75px;
  padding: 12px;
  margin-bottom: 10px;
}
/* line 149, ../../../sass/booking_widget/_booking_form.scss */
.comments_wrapper .comments_label {
  top: -8px;
  left: 10px;
  position: absolute;
  z-index: 1;
  background-color: white;
  padding: 0px 5px;
}

/* line 159, ../../../sass/booking_widget/_booking_form.scss */
.rooms_data_block, .search_filters_block {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: stretch;
  flex: 1;
}

/* line 168, ../../../sass/booking_widget/_booking_form.scss */
.rooms_data_block .group_search {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 15px;
  width: 100%;
  height: 40px;
  margin-bottom: 40px;
}
/* line 177, ../../../sass/booking_widget/_booking_form.scss */
.rooms_data_block .group_search .group_search_label {
  margin-bottom: 3px;
  font-weight: 600;
  font-size: 20px;
  letter-spacing: 0;
}
/* line 185, ../../../sass/booking_widget/_booking_form.scss */
.rooms_data_block .group_search .group_search_checkbox_wrapper .hidden_checkbox {
  display: none !important;
}
/* line 189, ../../../sass/booking_widget/_booking_form.scss */
.rooms_data_block .group_search .group_search_checkbox_wrapper .yes_no_checkbox {
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  cursor: pointer;
}
/* line 197, ../../../sass/booking_widget/_booking_form.scss */
.rooms_data_block .group_search .group_search_checkbox_wrapper .yes_no_checkbox .yes_no_checkbox_option {
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 50px;
  height: 24px;
  border: 1px solid #707070;
  font-weight: 300;
  font-size: 14px;
}
/* line 207, ../../../sass/booking_widget/_booking_form.scss */
.rooms_data_block .group_search .group_search_checkbox_wrapper .yes_no_checkbox .yes_no_checkbox_option:first-child {
  border-top-left-radius: 6px;
  border-bottom-left-radius: 6px;
}
/* line 212, ../../../sass/booking_widget/_booking_form.scss */
.rooms_data_block .group_search .group_search_checkbox_wrapper .yes_no_checkbox .yes_no_checkbox_option:last-child {
  border-top-right-radius: 6px;
  border-bottom-right-radius: 6px;
}
/* line 221, ../../../sass/booking_widget/_booking_form.scss */
.rooms_data_block .group_search .group_search_checkbox_wrapper .hidden_checkbox:not(:checked) + .yes_no_checkbox .yes_no_checkbox_option.yes {
  border-right: none;
}
/* line 225, ../../../sass/booking_widget/_booking_form.scss */
.rooms_data_block .group_search .group_search_checkbox_wrapper .hidden_checkbox:not(:checked) + .yes_no_checkbox .yes_no_checkbox_option.no {
  border-color: #F28E2A;
  background-color: rgba(242, 142, 42, 0.1);
}
/* line 234, ../../../sass/booking_widget/_booking_form.scss */
.rooms_data_block .group_search .group_search_checkbox_wrapper .hidden_checkbox:checked + .yes_no_checkbox .yes_no_checkbox_option.yes {
  border-color: #F28E2A;
  background-color: rgba(242, 142, 42, 0.1);
}
/* line 239, ../../../sass/booking_widget/_booking_form.scss */
.rooms_data_block .group_search .group_search_checkbox_wrapper .hidden_checkbox:checked + .yes_no_checkbox .yes_no_checkbox_option.no {
  border-left: none;
}

/* line 249, ../../../sass/booking_widget/_booking_form.scss */
.search_filters_block .group_selector {
  width: 100%;
}
/* line 257, ../../../sass/booking_widget/_booking_form.scss */
.search_filters_block .user_params_wrapper {
  display: flex;
  width: 100%;
  height: 100%;
  gap: 15px;
}
/* line 263, ../../../sass/booking_widget/_booking_form.scss */
.search_filters_block .user_params_wrapper .input_wrapper {
  width: 100%;
  margin-bottom: 30px;
}

/* line 270, ../../../sass/booking_widget/_booking_form.scss */
.buttons_block {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: stretch;
  width: 275px;
  gap: 25px;
}
/* line 278, ../../../sass/booking_widget/_booking_form.scss */
.buttons_block > * {
  width: 100%;
}
/* line 283, ../../../sass/booking_widget/_booking_form.scss */
.buttons_block .submit_button.comments:not(.active) {
  display: none;
}
/* line 287, ../../../sass/booking_widget/_booking_form.scss */
.buttons_block .submit_button.disabled {
  opacity: 0.3;
  cursor: auto;
  pointer-events: none;
}

/* line 295, ../../../sass/booking_widget/_booking_form.scss */
.geolocation_selection, .language_selection {
  margin-bottom: 20px;
  margin-bottom: 20px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-content: center;
}

/* line 304, ../../../sass/booking_widget/_booking_form.scss */
.user_dni_selection, .user_email_selection, .club_profile_selection {
  margin-bottom: 30px;
}

/* line 308, ../../../sass/booking_widget/_booking_form.scss */
.with_club_profile_selection, .club_profile_selection {
  width: calc((100% - 20px) / 3);
}

/* line 312, ../../../sass/booking_widget/_booking_form.scss */
.start_date,
.start_date_list,
.end_date,
.end_date_list {
  position: relative;
  width: calc((100% - 20px) / 2);
}
/* line 320, ../../../sass/booking_widget/_booking_form.scss */
.start_date.active label,
.start_date_list.active label,
.end_date.active label,
.end_date_list.active label {
  top: 0;
  font-size: 12px;
}

/* line 327, ../../../sass/booking_widget/_booking_form.scss */
.occupancy_selection {
  position: relative;
  z-index: 10;
}
/* line 331, ../../../sass/booking_widget/_booking_form.scss */
.occupancy_selection label {
  top: -15px !important;
  left: 10px;
  position: absolute;
  z-index: 1;
  background-color: white;
  padding: 5px 7px;
  transform: none;
  margin-left: 0;
  font-size: 14px !important;
}
/* line 343, ../../../sass/booking_widget/_booking_form.scss */
.occupancy_selection .occupancy_display {
  padding: 8px 18px;
  display: block;
  background-color: white;
  font-size: 16px;
  cursor: pointer;
}
/* line 351, ../../../sass/booking_widget/_booking_form.scss */
.occupancy_selection .occupancy_selector {
  position: absolute;
  top: 110%;
  left: 50%;
  transform: translate(-50%, 0%);
  width: 320px;
  background: white;
  padding: 15px;
  border: 1px solid #D7DAE2;
  box-shadow: 0 3px 15px #00000029;
  text-align: center;
  display: none;
}
/* line 364, ../../../sass/booking_widget/_booking_form.scss */
.occupancy_selection .occupancy_selector.active {
  display: block;
}
/* line 368, ../../../sass/booking_widget/_booking_form.scss */
.occupancy_selection .occupancy_selector.with_pets {
  width: 390px;
}
/* line 374, ../../../sass/booking_widget/_booking_form.scss */
.occupancy_selection .selector_block.rooms {
  margin-bottom: 20px;
}
/* line 378, ../../../sass/booking_widget/_booking_form.scss */
.occupancy_selection .selector_block:not(.rooms) {
  width: calc(100% / 2);
}
/* line 383, ../../../sass/booking_widget/_booking_form.scss */
.occupancy_selection .room_selection {
  border-bottom: 1px solid #D7DAE2;
}
/* line 386, ../../../sass/booking_widget/_booking_form.scss */
.occupancy_selection .room_selection .flex_wrapper {
  display: flex;
  align-items: center;
}
/* line 391, ../../../sass/booking_widget/_booking_form.scss */
.occupancy_selection .room_selection.room_1 {
  border-top: 1px solid #D7DAE2;
}
/* line 395, ../../../sass/booking_widget/_booking_form.scss */
.occupancy_selection .room_selection .selector_block:not(:last-child) {
  border-right: 1px solid #D7DAE2;
}
/* line 401, ../../../sass/booking_widget/_booking_form.scss */
.occupancy_selection .occupancy_selector .label {
  font-size: 16px;
}
/* line 405, ../../../sass/booking_widget/_booking_form.scss */
.occupancy_selection .occupancy_selector input {
  padding: 0;
  font-size: 18px;
}
/* line 412, ../../../sass/booking_widget/_booking_form.scss */
.occupancy_selection .room > div {
  display: inline-block;
}
/* line 416, ../../../sass/booking_widget/_booking_form.scss */
.occupancy_selection .room .babies_age_wrapper {
  margin-top: 5px;
}
/* line 420, ../../../sass/booking_widget/_booking_form.scss */
.occupancy_selection .room .children_age_wrapper .child_age {
  margin-right: 40px;
}
/* line 424, ../../../sass/booking_widget/_booking_form.scss */
.occupancy_selection .room .babies_age_wrapper i, .occupancy_selection .room .children_age_wrapper i {
  font-size: 14px;
}
/* line 428, ../../../sass/booking_widget/_booking_form.scss */
.occupancy_selection .room .room_title {
  position: absolute;
  top: -10px;
  background: white;
  padding: 0 10px;
  margin: 0;
  width: auto;
}
/* line 437, ../../../sass/booking_widget/_booking_form.scss */
.occupancy_selection .room label {
  color: black;
  font-size: 14px;
  margin-right: 10px;
  width: 100%;
  text-align: center;
}
/* line 444, ../../../sass/booking_widget/_booking_form.scss */
.occupancy_selection .room label .ages_range {
  font-size: 10px;
}
/* line 449, ../../../sass/booking_widget/_booking_form.scss */
.occupancy_selection .room select {
  position: relative;
  padding: 5px 10px;
  border-radius: 4px;
  border: 1px solid #D7DAE2;
  background-color: white;
  box-shadow: 0 2px 3px rgba(0, 0, 0, 0.05);
}

/* line 460, ../../../sass/booking_widget/_booking_form.scss */
.children_age_wrapper {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}

/* line 466, ../../../sass/booking_widget/_booking_form.scss */
.child_age, .baby_age {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  font-size: 14px;
  align-items: center;
}

/* line 474, ../../../sass/booking_widget/_booking_form.scss */
.child_age:first-child, .baby_age:first-child {
  margin-top: 10px;
}

/* line 478, ../../../sass/booking_widget/_booking_form.scss */
.child_age:last-child, .baby_age:last-child {
  margin-bottom: 5px;
}

/* line 482, ../../../sass/booking_widget/_booking_form.scss */
.occupancy_rooms .room {
  margin-bottom: 40px !important;
  position: relative;
}
/* line 486, ../../../sass/booking_widget/_booking_form.scss */
.occupancy_rooms .room .room_row {
  border: 0.5px solid #D7DAE2;
  border-radius: 4px;
  padding: 15px 20px;
  box-shadow: 0 2px 3px rgba(0, 0, 0, 0.05);
}
/* line 493, ../../../sass/booking_widget/_booking_form.scss */
.occupancy_rooms .room .occupancy_selector {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 20px;
}
/* line 500, ../../../sass/booking_widget/_booking_form.scss */
.occupancy_rooms .room .room_row {
  display: flex !important;
  align-content: space-around;
  justify-content: flex-start;
  align-items: stretch;
  width: 100%;
}

/* line 509, ../../../sass/booking_widget/_booking_form.scss */
.child_age .child_button_age, .baby_age .baby_button_age {
  margin-left: 10px;
  display: flex;
}

/* line 514, ../../../sass/booking_widget/_booking_form.scss */
.child_age .child_button_age input, .baby_age .baby_button_age input {
  margin: 2px;
  position: relative;
  bottom: 2px;
}

/* line 1, ../../../sass/booking_widget/_users.scss */
.user_wrapper {
  display: flex;
  flex-wrap: wrap;
  align-content: stretch;
  flex-direction: column;
  width: calc((100% - 180px - 100px) / 2);
  margin-right: 40px;
}
/* line 9, ../../../sass/booking_widget/_users.scss */
.user_wrapper .button_wrapper {
  width: 215px;
  margin-bottom: 0;
}
/* line 14, ../../../sass/booking_widget/_users.scss */
.user_wrapper .stay_selection.input_wrapper.active {
  margin-top: 15px;
}
/* line 18, ../../../sass/booking_widget/_users.scss */
.user_wrapper .stay_selection.input_wrapper.active label.start_date_label, .user_wrapper .stay_selection.input_wrapper.active label.end_date_label {
  top: -10px !important;
}

/* line 25, ../../../sass/booking_widget/_users.scss */
.bottom_wrapper {
  padding-top: 25px;
  background-color: #fff;
  border-top: 1px solid #ddd;
}
/* line 30, ../../../sass/booking_widget/_users.scss */
.bottom_wrapper .bottom_flex_wrapper {
  display: flex;
  align-content: flex-end;
  flex-direction: row;
}
/* line 36, ../../../sass/booking_widget/_users.scss */
.bottom_wrapper .stay_selection {
  width: 320px;
}

/* line 1, ../../../sass/booking_widget/_prebooking.scss */
.right_wrapper {
  width: calc((100% - 180px - 100px) / 2);
}

/* line 5, ../../../sass/booking_widget/_prebooking.scss */
.multi_prebooking_block {
  margin-bottom: 10px;
}
/* line 8, ../../../sass/booking_widget/_prebooking.scss */
.multi_prebooking_block .tooltip_info {
  margin-left: 5px;
  position: relative;
}
/* line 12, ../../../sass/booking_widget/_prebooking.scss */
.multi_prebooking_block .tooltip_info i {
  font-size: 20px;
  color: #F28E2A;
}
/* line 17, ../../../sass/booking_widget/_prebooking.scss */
.multi_prebooking_block .tooltip_info .info_content {
  position: absolute;
  padding: 10px;
  background-color: white;
  border: 1px solid #D7DAE2;
  border-radius: 4px;
  box-shadow: 0 2px 3px rgba(0, 0, 0, 0.05);
  z-index: 10;
  font-weight: 400;
  font-size: 16px;
  left: -30px;
  bottom: 45px;
  display: none;
  width: 300px;
}
/* line 32, ../../../sass/booking_widget/_prebooking.scss */
.multi_prebooking_block .tooltip_info .info_content:after {
  content: '';
  width: 20px;
  height: 20px;
  transform: rotate(45deg);
  background-color: white;
  position: absolute;
  bottom: -11px;
  left: 37px;
  margin-left: -10px;
  z-index: -1;
  border-right: 1px solid #D7DAE2;
  border-bottom: 1px solid #D7DAE2;
  border-radius: 4px;
}
/* line 49, ../../../sass/booking_widget/_prebooking.scss */
.multi_prebooking_block .tooltip_info:hover .info_content {
  display: block;
}
/* line 54, ../../../sass/booking_widget/_prebooking.scss */
.multi_prebooking_block .email_selection {
  width: 450px;
  margin-bottom: 10px;
}
/* line 58, ../../../sass/booking_widget/_prebooking.scss */
.multi_prebooking_block .email_selection.error {
  border-color: red;
}
/* line 63, ../../../sass/booking_widget/_prebooking.scss */
.multi_prebooking_block .button_wrapper {
  width: 215px;
  margin-bottom: 0;
}
/* line 67, ../../../sass/booking_widget/_prebooking.scss */
.multi_prebooking_block .button_wrapper.disabled {
  opacity: 0.6;
}
/* line 72, ../../../sass/booking_widget/_prebooking.scss */
.multi_prebooking_block .flex_wrapper {
  width: calc((215px * 3) + 40px);
  display: flex;
  justify-content: space-between;
  align-items: stretch;
}
/* line 79, ../../../sass/booking_widget/_prebooking.scss */
.multi_prebooking_block .options_checkbox_wrapper {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 30px;
}
/* line 86, ../../../sass/booking_widget/_prebooking.scss */
.multi_prebooking_block .checkbox_wrapper {
  width: auto;
}
/* line 90, ../../../sass/booking_widget/_prebooking.scss */
.multi_prebooking_block .buttons_wrapper {
  display: flex;
}

/* line 96, ../../../sass/booking_widget/_prebooking.scss */
.send_email_block .send_email_form {
  width: calc((215px * 3) + 40px);
  display: flex;
  justify-content: space-between;
  align-items: stretch;
}
/* line 103, ../../../sass/booking_widget/_prebooking.scss */
.send_email_block .input_wrapper, .send_email_block .button_wrapper {
  width: 215px;
  margin-bottom: 0;
}

/* line 1, ../../../sass/booking_widget/_zendesk_tickets.scss */
.tickets_wrapper {
  margin-bottom: 50px;
  border-bottom: 1px solid #DDD;
  padding-bottom: 30px;
}
/* line 6, ../../../sass/booking_widget/_zendesk_tickets.scss */
.tickets_wrapper .create_ticket_btn, .tickets_wrapper .modify_ticket_btn, .tickets_wrapper .send_ticket_btn {
  margin: 15px 0;
}
/* line 9, ../../../sass/booking_widget/_zendesk_tickets.scss */
.tickets_wrapper .create_ticket_btn.create_ticket_btn, .tickets_wrapper .create_ticket_btn.modify_ticket_btn, .tickets_wrapper .modify_ticket_btn.create_ticket_btn, .tickets_wrapper .modify_ticket_btn.modify_ticket_btn, .tickets_wrapper .send_ticket_btn.create_ticket_btn, .tickets_wrapper .send_ticket_btn.modify_ticket_btn {
  display: inline-flex;
  margin-bottom: 5px;
}
/* line 14, ../../../sass/booking_widget/_zendesk_tickets.scss */
.tickets_wrapper .create_ticket_btn.modify_ticket_btn, .tickets_wrapper .create_ticket_btn.send_ticket_btn, .tickets_wrapper .modify_ticket_btn.modify_ticket_btn, .tickets_wrapper .modify_ticket_btn.send_ticket_btn, .tickets_wrapper .send_ticket_btn.modify_ticket_btn, .tickets_wrapper .send_ticket_btn.send_ticket_btn {
  margin-left: 20px;
}
/* line 18, ../../../sass/booking_widget/_zendesk_tickets.scss */
.tickets_wrapper .create_ticket_btn.hide, .tickets_wrapper .modify_ticket_btn.hide, .tickets_wrapper .send_ticket_btn.hide {
  display: none;
}
/* line 23, ../../../sass/booking_widget/_zendesk_tickets.scss */
.tickets_wrapper .send_loader {
  width: 215px;
  margin-top: 10px;
  text-align: center;
  display: none;
}
/* line 29, ../../../sass/booking_widget/_zendesk_tickets.scss */
.tickets_wrapper .send_loader img {
  width: 50px;
}
/* line 34, ../../../sass/booking_widget/_zendesk_tickets.scss */
.tickets_wrapper .inputs_wrapper {
  width: 650px;
  display: none;
}
/* line 38, ../../../sass/booking_widget/_zendesk_tickets.scss */
.tickets_wrapper .inputs_wrapper .whatsapp_switch_wrapper {
  position: relative;
  box-sizing: border-box;
  margin-top: 10px;
  display: none;
}
/* line 44, ../../../sass/booking_widget/_zendesk_tickets.scss */
.tickets_wrapper .inputs_wrapper .whatsapp_switch_wrapper .switch {
  position: relative;
  width: 105px;
  height: 32px;
  overflow: hidden;
  border-radius: 100px;
}
/* line 51, ../../../sass/booking_widget/_zendesk_tickets.scss */
.tickets_wrapper .inputs_wrapper .whatsapp_switch_wrapper .switch .checkbox {
  position: relative;
  width: 100%;
  height: 100%;
  padding: 0;
  margin: 0;
  opacity: 0;
  cursor: pointer;
  z-index: 3;
}
/* line 61, ../../../sass/booking_widget/_zendesk_tickets.scss */
.tickets_wrapper .inputs_wrapper .whatsapp_switch_wrapper .switch .checkbox:active + .knobs:before {
  width: 46px;
  border-radius: 100px;
}
/* line 66, ../../../sass/booking_widget/_zendesk_tickets.scss */
.tickets_wrapper .inputs_wrapper .whatsapp_switch_wrapper .switch .checkbox:checked:active + .knobs:before {
  margin-left: -26px;
}
/* line 70, ../../../sass/booking_widget/_zendesk_tickets.scss */
.tickets_wrapper .inputs_wrapper .whatsapp_switch_wrapper .switch .checkbox:checked + .knobs:before {
  content: "Whatsapp";
  left: 42px;
  background-color: #F28E2A;
}
/* line 76, ../../../sass/booking_widget/_zendesk_tickets.scss */
.tickets_wrapper .inputs_wrapper .whatsapp_switch_wrapper .switch .checkbox:checked ~ .layer {
  background-color: rgba(242, 142, 42, 0.2);
}
/* line 81, ../../../sass/booking_widget/_zendesk_tickets.scss */
.tickets_wrapper .inputs_wrapper .whatsapp_switch_wrapper .switch .knobs, .tickets_wrapper .inputs_wrapper .whatsapp_switch_wrapper .switch .layer {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}
/* line 89, ../../../sass/booking_widget/_zendesk_tickets.scss */
.tickets_wrapper .inputs_wrapper .whatsapp_switch_wrapper .switch .knobs {
  z-index: 2;
}
/* line 92, ../../../sass/booking_widget/_zendesk_tickets.scss */
.tickets_wrapper .inputs_wrapper .whatsapp_switch_wrapper .switch .knobs:before {
  content: "Email";
  position: absolute;
  top: 4px;
  left: 4px;
  width: 50px;
  height: 7px;
  color: #fff;
  font-size: 11px;
  font-weight: bold;
  text-align: center;
  line-height: 0.7;
  padding: 9px 4px;
  background-color: #F28E2A;
  border-radius: 30px;
  transition: 0.3s ease all, left 0.3s cubic-bezier(0.18, 0.89, 0.35, 1.15);
}
/* line 111, ../../../sass/booking_widget/_zendesk_tickets.scss */
.tickets_wrapper .inputs_wrapper .whatsapp_switch_wrapper .switch .layer {
  width: 100%;
  background-color: rgba(242, 142, 42, 0.2);
  transition: 0.3s ease all;
  z-index: 1;
  border-radius: 100px;
}
/* line 121, ../../../sass/booking_widget/_zendesk_tickets.scss */
.tickets_wrapper .inputs_wrapper .input_wrapper {
  position: relative;
  border-radius: 4px !important;
  border: 1px solid #D7DAE2;
  background-color: white;
  box-shadow: 0 2px 3px rgba(0, 0, 0, 0.05);
  margin: 15px 0;
  width: calc((100% - 40px) / 3);
  min-height: 40px;
}
/* line 131, ../../../sass/booking_widget/_zendesk_tickets.scss */
.tickets_wrapper .inputs_wrapper .input_wrapper input {
  height: 40px !important;
}
/* line 135, ../../../sass/booking_widget/_zendesk_tickets.scss */
.tickets_wrapper .inputs_wrapper .input_wrapper label {
  display: inline-block;
  position: absolute;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
  margin-left: 16px;
  padding: 0 4px;
  background-color: white;
  font-size: 16px;
  white-space: nowrap;
  pointer-events: none;
  transition: all 0.3s;
}
/* line 150, ../../../sass/booking_widget/_zendesk_tickets.scss */
.tickets_wrapper .inputs_wrapper .input_wrapper input, .tickets_wrapper .inputs_wrapper .input_wrapper select {
  width: 100%;
  height: 100%;
  border: none;
  background: none;
  outline: none;
  padding: 0 20px;
  font-family: "Source Sans Pro", sans-serif;
  font-size: 16px;
}
/* line 161, ../../../sass/booking_widget/_zendesk_tickets.scss */
.tickets_wrapper .inputs_wrapper .input_wrapper select {
  width: 95%;
}
/* line 165, ../../../sass/booking_widget/_zendesk_tickets.scss */
.tickets_wrapper .inputs_wrapper .input_wrapper.active label:not(.error) {
  top: 0;
  font-size: 12px;
}
/* line 170, ../../../sass/booking_widget/_zendesk_tickets.scss */
.tickets_wrapper .inputs_wrapper .input_wrapper:not(:first-child) {
  margin-left: 20px;
}
/* line 174, ../../../sass/booking_widget/_zendesk_tickets.scss */
.tickets_wrapper .inputs_wrapper .input_wrapper.error {
  border-color: red;
}
/* line 179, ../../../sass/booking_widget/_zendesk_tickets.scss */
.tickets_wrapper .inputs_wrapper.creating {
  display: block;
}
/* line 182, ../../../sass/booking_widget/_zendesk_tickets.scss */
.tickets_wrapper .inputs_wrapper.creating .bottom_flex {
  display: flex;
}
/* line 186, ../../../sass/booking_widget/_zendesk_tickets.scss */
.tickets_wrapper .inputs_wrapper.creating .input_wrapper.id_ticket {
  display: none;
}
/* line 190, ../../../sass/booking_widget/_zendesk_tickets.scss */
.tickets_wrapper .inputs_wrapper.creating .input_wrapper.email {
  margin-left: 0;
}
/* line 195, ../../../sass/booking_widget/_zendesk_tickets.scss */
.tickets_wrapper .inputs_wrapper.modifyng {
  display: block;
}
/* line 198, ../../../sass/booking_widget/_zendesk_tickets.scss */
.tickets_wrapper .inputs_wrapper.modifyng .whatsapp_switch_wrapper {
  display: block;
}
/* line 202, ../../../sass/booking_widget/_zendesk_tickets.scss */
.tickets_wrapper .inputs_wrapper.modifyng .bottom_flex {
  display: flex;
}
/* line 206, ../../../sass/booking_widget/_zendesk_tickets.scss */
.tickets_wrapper .inputs_wrapper.modifyng .input_wrapper.email {
  display: none;
}
/* line 212, ../../../sass/booking_widget/_zendesk_tickets.scss */
.tickets_wrapper .response_wrapper {
  font-size: 22px;
  line-height: 0.6px;
  margin-top: 20px;
}
/* line 217, ../../../sass/booking_widget/_zendesk_tickets.scss */
.tickets_wrapper .response_wrapper .label {
  font-weight: 600;
}
/* line 221, ../../../sass/booking_widget/_zendesk_tickets.scss */
.tickets_wrapper .response_wrapper .ticket_id {
  font-size: 24px;
  line-height: 1.5px;
}
/* line 226, ../../../sass/booking_widget/_zendesk_tickets.scss */
.tickets_wrapper .response_wrapper.hide {
  display: none;
}

/* line 1, ../../../sass/booking_widget/_booking_results.scss */
.booking_results_wrapper {
  flex: 1;
}
/* line 4, ../../../sass/booking_widget/_booking_results.scss */
.booking_results_wrapper iframe {
  display: block;
  width: 100%;
  height: 100%;
  margin: 0 auto;
}
/* line 11, ../../../sass/booking_widget/_booking_results.scss */
.booking_results_wrapper.mobile {
  margin: 20px auto;
}
/* line 14, ../../../sass/booking_widget/_booking_results.scss */
.booking_results_wrapper.mobile iframe {
  max-width: 400px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  height: 700px !important;
  min-height: 700px;
}
/* line 22, ../../../sass/booking_widget/_booking_results.scss */
.booking_results_wrapper.hidden {
  margin: 0;
}
/* line 25, ../../../sass/booking_widget/_booking_results.scss */
.booking_results_wrapper.hidden iframe {
  height: 0 !important;
  min-height: auto;
  border: 0;
}

/* line 1, ../../../sass/booking_widget/_paraty_observations.scss */
.paraty_observations_wrapper {
  padding: 0 20px 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #D7DAE2;
  margin-bottom: 20px;
  border-radius: 5px;
  width: 70%;
  margin-top: 40px;
}
/* line 10, ../../../sass/booking_widget/_paraty_observations.scss */
.paraty_observations_wrapper .title_wrapper {
  font-size: 12px;
  color: #333;
  margin-bottom: 15px;
  margin-top: -8px;
  background: white;
  display: table;
  padding: 0 20px;
}
/* line 20, ../../../sass/booking_widget/_paraty_observations.scss */
.paraty_observations_wrapper .observations_list {
  margin-bottom: 20px;
  max-height: 170px;
  overflow: scroll;
  overflow-x: hidden;
}
/* line 28, ../../../sass/booking_widget/_paraty_observations.scss */
.paraty_observations_wrapper .observation_writter textarea {
  width: 100%;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
}
/* line 35, ../../../sass/booking_widget/_paraty_observations.scss */
.paraty_observations_wrapper .button {
  background-color: #0056b3;
  color: white;
  padding: 4px 11px;
  text-align: center;
  cursor: pointer;
  border-radius: 5px;
  display: inline-block;
  margin: 5px 0;
}
/* line 46, ../../../sass/booking_widget/_paraty_observations.scss */
.paraty_observations_wrapper .observation_baseline {
  display: none;
}
/* line 50, ../../../sass/booking_widget/_paraty_observations.scss */
.paraty_observations_wrapper .add-button {
  margin: 10px 5px;
}
/* line 54, ../../../sass/booking_widget/_paraty_observations.scss */
.paraty_observations_wrapper .modify-button, .paraty_observations_wrapper .delete-button {
  margin-bottom: 0;
  margin-right: 5px;
}
/* line 59, ../../../sass/booking_widget/_paraty_observations.scss */
.paraty_observations_wrapper .add-button:hover, .paraty_observations_wrapper .modify-button:hover, .paraty_observations_wrapper .delete-button:hover {
  background-color: #004494;
}
/* line 63, ../../../sass/booking_widget/_paraty_observations.scss */
.paraty_observations_wrapper .observation_element {
  border-top: 1px solid #ccc;
  padding: 10px;
  position: relative;
}
/* line 68, ../../../sass/booking_widget/_paraty_observations.scss */
.paraty_observations_wrapper .observation_element .user_info {
  position: absolute;
  right: 20px;
  top: 0;
  background: lightgray;
  padding: 2px 5px;
}
/* line 76, ../../../sass/booking_widget/_paraty_observations.scss */
.paraty_observations_wrapper .observation_element .observation_comment {
  margin-bottom: 10px;
}
/* line 80, ../../../sass/booking_widget/_paraty_observations.scss */
.paraty_observations_wrapper .observation_element .observation_date {
  position: absolute;
  right: 12px;
  bottom: 3px;
  font-size: 11px;
  color: gray;
}
/* line 88, ../../../sass/booking_widget/_paraty_observations.scss */
.paraty_observations_wrapper .observation_element:first-of-type {
  border-top: none;
}

@media screen and (max-width: 991px) {
  /* line 1, ../../../sass/booking_widget/mobile/_main_mobile.scss */
  .search_form_block {
    flex-direction: column;
    gap: 0;
  }
  /* line 5, ../../../sass/booking_widget/mobile/_main_mobile.scss */
  .search_form_block:before {
    display: none;
  }
  /* line 10, ../../../sass/booking_widget/mobile/_main_mobile.scss */
  .search_form_block .input_wrapper input, .search_form_block .input_wrapper select, .search_form_block .input_wrapper .occupancy_display {
    padding: 15px;
  }
  /* line 15, ../../../sass/booking_widget/mobile/_main_mobile.scss */
  .search_form_block .stay_selection {
    margin-bottom: 20px;
  }
  /* line 19, ../../../sass/booking_widget/mobile/_main_mobile.scss */
  .search_form_block .force_mobile_checkbox {
    display: none;
  }
  /* line 23, ../../../sass/booking_widget/mobile/_main_mobile.scss */
  .search_form_block .search_filters_block {
    width: 100%;
    margin-top: 15px;
    flex-direction: column;
  }
  /* line 28, ../../../sass/booking_widget/mobile/_main_mobile.scss */
  .search_form_block .search_filters_block .user_params_wrapper {
    display: block;
    margin-bottom: 10px;
  }
  /* line 33, ../../../sass/booking_widget/mobile/_main_mobile.scss */
  .search_form_block .search_filters_block .input_wrapper {
    width: 100%;
    margin-bottom: 10px;
  }
  /* line 39, ../../../sass/booking_widget/mobile/_main_mobile.scss */
  .search_form_block .buttons_block {
    margin: auto auto 30px;
  }

  /* line 46, ../../../sass/booking_widget/mobile/_main_mobile.scss */
  .bottom_wrapper .tickets_wrapper .create_ticket_btn, .bottom_wrapper .tickets_wrapper .modify_ticket_btn {
    width: 100%;
    margin: auto;
  }
  /* line 51, ../../../sass/booking_widget/mobile/_main_mobile.scss */
  .bottom_wrapper .tickets_wrapper .modify_ticket_btn {
    margin-top: 20px;
  }

  /* line 57, ../../../sass/booking_widget/mobile/_main_mobile.scss */
  .bottom_wrapper .bottom_flex_wrapper {
    width: 100%;
  }
  /* line 60, ../../../sass/booking_widget/mobile/_main_mobile.scss */
  .bottom_wrapper .bottom_flex_wrapper .right_wrapper {
    width: 100%;
  }
  /* line 64, ../../../sass/booking_widget/mobile/_main_mobile.scss */
  .bottom_wrapper .bottom_flex_wrapper .send_email_block .send_email_form {
    width: auto;
    flex-direction: column;
    gap: 15px;
  }
  /* line 69, ../../../sass/booking_widget/mobile/_main_mobile.scss */
  .bottom_wrapper .bottom_flex_wrapper .send_email_block .send_email_form .input_wrapper {
    width: 100%;
  }
  /* line 73, ../../../sass/booking_widget/mobile/_main_mobile.scss */
  .bottom_wrapper .bottom_flex_wrapper .send_email_block .send_email_form .button_wrapper {
    width: 100%;
  }
  /* line 76, ../../../sass/booking_widget/mobile/_main_mobile.scss */
  .bottom_wrapper .bottom_flex_wrapper .send_email_block .send_email_form .button_wrapper .btn_2 {
    width: 100%;
  }

  /* line 83, ../../../sass/booking_widget/mobile/_main_mobile.scss */
  .paraty_observations_wrapper {
    width: 100%;
  }

  /* line 87, ../../../sass/booking_widget/mobile/_main_mobile.scss */
  #ui-datepicker-div {
    z-index: 10 !important;
  }

  /* line 91, ../../../sass/booking_widget/mobile/_main_mobile.scss */
  .send_email_block {
    margin-top: 40px;
  }

  /* line 2, ../../../sass/booking_widget/mobile/_multiprebooking.scss */
  .multi_prebooking_block .flex_wrapper {
    width: 100%;
    flex-direction: column;
  }
  /* line 6, ../../../sass/booking_widget/mobile/_multiprebooking.scss */
  .multi_prebooking_block .flex_wrapper .email_selection {
    width: 100%;
  }
  /* line 10, ../../../sass/booking_widget/mobile/_multiprebooking.scss */
  .multi_prebooking_block .flex_wrapper .button_wrapper {
    margin: 10px 0;
  }
  /* line 15, ../../../sass/booking_widget/mobile/_multiprebooking.scss */
  .multi_prebooking_block .checkbox_label {
    font-size: 15px;
  }
}
