<!doctype html>
<html>
  <head>
    <link rel="stylesheet" href="../vendor/qunit-1.14.0.css" type="text/css" />
    <link rel="stylesheet" href="../../dist/css/select2.css" type="text/css" />
  </head>
  <body>
    <div id="qunit"></div>
    <div id="qunit-fixture">
      <select class="single">
        <option value="default">Default</option>
        <option value="1">One</option>
        <option>2</option>
      </select>

      <select class="multiple" multiple="multiple">
        <option value="default">Default</option>
        <option value="1">One</option>
        <option>2</option>
      </select>

      <select class="groups">
        <optgroup label="Test">
          <option value="one">One</option>
          <option value="two">Two</option>
        </optgroup>
        <optgroup label="Empty"></optgroup>
      </select>

      <select class="duplicates">
        <option value="one">One</option>
        <option value="two">Two</option>
        <option value="one">Uno</option>
      </select>

      <select class="duplicates-multi" multiple="multiple">
        <option value="one">One</option>
        <option value="two">Two</option>
        <option value="one">Uno</option>
      </select>
    </div>

    <script src="../vendor/qunit-1.14.0.js" type="text/javascript"></script>
    <script src="../../vendor/jquery-2.1.0.js" type="text/javascript"></script>
    <script src="../../dist/js/select2.full.js" type="text/javascript"></script>

    <script src="../helpers.js" type="text/javascript"></script>

    <script src="select-tests.js" type="text/javascript"></script>
  </body>
</html>
