<!doctype html>
<html>
  <head>
    <link rel="stylesheet" href="../vendor/qunit-1.14.0.css" type="text/css" />
    <link rel="stylesheet" href="../../dist/css/select2.css" type="text/css" />
  </head>
  <body>
    <div id="qunit"></div>
    <div id="qunit-fixture">
      <select class="single">
        <option>One</option>
      </select>

      <select class="single-with-placeholder">
        <option>placeholder</option>
        <option>One</option>
      </select>

      <select class="multiple" multiple="multiple">
        <option>One</option>
        <option>Two</option>
      </select>
    </div>

    <script src="vendor/qunit-1.14.0.js" type="text/javascript"></script>
    <script src="../vendor/jquery-2.1.0.js" type="text/javascript"></script>
    <script src="../dist/js/select2.full.js" type="text/javascript"></script>

    <script src="helpers.js" type="text/javascript"></script>

    <script src="selection/allowClear-tests.js" type="text/javascript"></script>
    <script src="selection/multiple-tests.js" type="text/javascript"></script>
    <script src="selection/placeholder-tests.js" type="text/javascript"></script>
    <script src="selection/single-tests.js" type="text/javascript"></script>
    <script src="selection/stopPropagation-tests.js" type="text/javascript"></script>
  </body>
</html>
