---
layout: default
title: Options - Select2
slug: options
---

<div class="container">
  <section id="core">
    <div class="page-header">
      <h1>Core options</h1>
    </div>

    <p>
      Select2 supports a small subset of options in every build that is
      generated. Each option typically has a decorator that is required that
      wraps an adapter, adding support for the option. This is only required
      when a custom adapter is being used, as Select2 will build the required
      adapters by default.
    </p>

    <p>
      Select2 will automatically apply decorators to any adapters which have not
      been manually overridden. The only time you need to decorate adapters is
      when you are using third-party adapters not provided by Select2, or you
      are using features not provided in the Select2 core. You can apply a
      decorator to an adapter using the
      <code title="select2/utils">Utils.Decorate</code> method provided with
      Select2.
    </p>

<pre class="prettyprint linenums">
$.fn.select2.amd.require(
    ["select2/utils", "select2/selection/single", "select2/selection/placeholder"],
    function (Utils, SingleSelection, Placeholder) {
  var CustomSelectionAdapter = Utils.Decorate(SingleSelection, Placeholder);
});
</pre>

    <p>
      All core options that use decorators or adapters will clearly state it
      in the "Decorator" or "Adapter" part of the documentation. Decorators are
      typically only compatible with a specific type of adapter, so make sure to
      note what adapter is given.
    </p>

    <h2 id="data-attributes">
      Declaring configuration in the <code>data-*</code> attributes
    </h2>

    <p>
      It is recommended that you declare your configuration options for Select2
      when initializing Select2. You can also define your configuration options
      by using the HTML5 <code>data-*</code> attributes, which will override
      any options set when initializing Select2 and any defaults.
    </p>

    <p>
      This means that if you declare your <code>&lt;select&gt;</code> tag as...
    </p>

<pre class="prettyprint linenums">
&lt;select data-tags="true" data-placeholder="Select an option"&gt;&lt;/select&gt;
</pre>

    <p>
      Will be interpreted the same as initializing Select2 as...
    </p>

<pre class="prettyprint linenums">
$("select").select2({
  tags: "true",
  placeholder: "Select an option"
});
</pre>

    <p>
      You can also define nested configurations, which are typically needed for
      options such as AJAX. Each level of nesting should be separated by two
      dashes (<code>--</code>) instead of one. Due to
      <a href="https://github.com/jquery/jquery/issues/2070">a jQuery bug</a>,
      nested options using <code>data-*</code> attributes
      <a href="https://github.com/select2/select2/issues/2969">do not work in jQuery 1.x</a>.
    </p>

<pre class="prettyprint linenums">
&lt;select data-ajax--url="http://example.org/api/test" data-ajax--cache="true"&gt;&lt;/select&gt;
</pre>

    <p>
      Which will be interpreted the same as initializing Select2 with...
    </p>

<pre class="prettyprint linenums">
$("select").select2({
  ajax: {
    url: "http://example.org/api/test",
    cache: "true"
  }
});
</pre>

    <p>
      The value of the option is subject to jQuery's
      <a href="https://api.jquery.com/data/#data-html5">parsing rules</a> for
      HTML5 data attributes.
    </p>

    <h2 id="amd">
      AMD compatibility
    </h2>

    <p>
      You can find more information on how to integrate Select2 with your
      existing AMD-based project by
      <a href="announcements-4.0.html#amd-builds">viewing the 4.0 release notes</a>.
      Select2 automatically loads some modules when the adapters are being
      automatically constructed, so those who are using Select2 with a custom
      AMD build using their own system will need to specify the paths that are
      generated to the Select2 modules.
    </p>

    <div class="row">
      <div class="col-sm-6">
        <dl class="dl-horizontal">
          <dt>Key</dt>
          <dd>
            <code>amdBase</code>
          </dd>

          <dt>Default</dt>
          <dd>
            <code>select2/</code>
          </dd>
        </dl>
      </div>
      <div class="col-sm-6">
        <dl class="dl-horizontal">
          <dt>Key</dt>
          <dd>
            <code>amdLanguageBase</code>
          </dd>

          <dt>Default</dt>
          <dd>
            <code>select2/i18n/</code>
          </dd>
        </dl>
      </div>
    </div>

    <p>
      If you are using Select2 in your own code base with your own AMD build
      system, it is recommended to use the full source of Select2 instead of the
      custom AMD build (<code>select2.amd.js</code>). This will allow you to
      apply your own build steps, such as minification and concatenation,
      independent of the Select2 defaults. You will need to set the path to the
      source directories in the <code>amdBase</code> and
      <code>amdLanguage</code> base options, which can be set globally through
      <a href="#defaults">the default options</a>.
    </p>

    <h2>
      Display
    </h2>

    <p>
      Select2 provides options that allow you to directly affect how the
      container that holds the current selection is displayed.
    </p>

    <h3 id="placeholder">
      Placeholders
    </h3>

    <p>
      Select2 can display a placeholder for a single-value select that will
      replace an option, or be shown when no options are selected for
      multiple-value selects. You can find an example on the
      <a href="examples.html#placeholders">example page</a>.
    </p>

    <div class="row">
      <div class="col-sm-4">
        <dl class="dl-horizontal">
          <dt>Key</dt>
          <dd>
            <code>placeholder</code>
          </dd>

          <dt>Value</dt>
          <dd>string or object</dd>
        </dl>

        <hr />

        <dl class="dl-horizontal">
          <dt>Adapter</dt>
          <dd>
            <code title="select2/selection/base">SelectionAdapter</code>
          </dd>

          <dt>Decorator</dt>
          <dd>
            <code title="select2/selection/placeholder">Placeholder</code>
            and
            <code title="select2/dropdown/hidePlaceholder">HidePlaceholder</code>
          </dd>
        </dl>
      </div>

      <div class="col-sm-8">
        <div class="alert alert-warning">
          <strong>Heads up!</strong>
          Because browsers assume that the first <code>option</code> in
          single-value select boxes is selected, you should add an empty
          <code>&lt;option&gt;&lt;/option&gt;</code> tag that the placeholder
          should use or it may not work.
        </div>
      </div>
    </div>

    <p>
      If the <strong>value is a string</strong>, the placeholder will be
      displayed when a <strong>blank option</strong> is used as the placeholder.
      The <strong>value</strong> will be the message to show to users as the
      placeholders.
    </p>

<pre class="prettyprint linenums">
placeholder: "Select a repository",
</pre>

    <p>
      If the <strong>value is an object</strong>, the object should be
      compatible with Select2's internal objects. The <code>id</code> should
      be the id to look for when determining if the placeholder should be
      displayed. The <code>text</code> should be the placeholder to display
      when that option is selected.
    </p>

<pre class="prettyprint linenums">
placeholder: {
  id: "-1",
  text: "Select a repository"
}
</pre>

    <div class="alert alert-info">
      You should <strong>pass in an object</strong> when you are using a
      framework that <strong>creates its own placeholder option</strong>. The
      <strong>id</strong> should be the same as the <code>value</code>
      attribute on the <code>option</code>.
    </div>

    <p id="allowClear">
      You can allow a selected option to be cleared back to the placeholder by
      enabling the <code>allowClear</code> option.
    </p>

    <div class="row">
      <div class="col-sm-6">
        <dl class="dl-horizontal">
          <dt>Key</dt>
          <dd><code>allowClear</code></dd>

          <dt>Value</dt>
          <dd>boolean</dd>
        </dl>
      </div>

      <div class="col-sm-6">
        <dl class="dl-horizontal">
          <dt>Adapter</dt>
          <dd>
            <code title="select2/selection/base">SelectionAdapter</code>
          </dd>

          <dt>Decorator</dt>
          <dd>
            <code title="select2/selection/allowClear">AllowClear</code>
          </dd>
        </dl>
      </div>
    </div>

    <p>
      This will display an "x" that the user can click to clear the current
      selection. It is designed to be used for cases where a single selection
      can be made.
    </p>

    <h3 id="multiple">
      Multiple selections
    </h3>

    <p>
      Select2 can display either a single selection or multiple selections.
    </p>

    <dl class="dl-horizontal">
      <dt>Key</dt>
      <dd><code>multiple</code></dd>

      <dt>Value</dt>
      <dd>boolean (<code>true</code> or <code>false</code>)</dd>
    </dl>

    <p>
      This option will determine what the <code>SelectAdapter</code> (used by
      default) should use to set the value of the underlying <code>select</code>
      element. It will also determine if the <code>MultipleSelection</code>
      adapter should be used.
    </p>

    <h3 id="width">
      Container width
    </h3>

    <p>
      Select2 will try to match the width of the original element as closely as
      possible. Sometimes this isn't perfect, which is what you can tell Select2
      how to determine the width.
    </p>

    <div class="row">
      <div class="col-sm-8">
        <table class="table table-striped table-bordered">
          <thead>
            <tr>
              <th>Value</th>
              <th>Description</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td><code>"element"</code></td>
              <td>
                Uses javascript to calculate the width of the source element.
              </td>
            </tr>
            <tr>
              <td><code>"style"</code></td>
              <td>
                Copies the value of the width <code>style</code> attribute set on the source element.
              </td>
            </tr>
            <tr>
              <td><code>"resolve"</code></td>
              <td>
                Tries to use <code>style</code> to determine the width, falling back to <code>element</code>.
              </td>
            </tr>
            <tr>
              <td>Anything else</td>
              <td>
                The value of the <code>width</code> option is directly set as the width of the container.
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="col-sm-4">
        <dl class="dl-horizontal">
          <dt>Key</dt>
          <dd><code>width</code></dd>

          <dt>Value</dt>
          <dd>string</dd>
        </dl>
      </div>
    </div>

    <h3 id="language">
      Internationalization (Language support)
    </h3>

    <p>
      Messages will be displayed to users when necessary, such as when no
      search results were found or more characters need to be entered in order
      for a search to be made. These messages have been
      <a href="community.html#translations">translated into many languages</a>
      by contributors to Select2, but you can also provide your own
      translations.
    </p>

    <div class="row">
      <div class="col-sm-4">
        <dl class="dl-horizontal">
          <dt>Key</dt>
          <dd><code>language</code></dd>

          <dt>Value</dt>
          <dd>object or string</dd>
        </dl>

        <hr />

        <dl class="dl-horizontal">
          <dt>Module</dt>
          <dd>
            <code title="select2/translation">Translation</code>
          </dd>
        </dl>
      </div>

      <div class="col-sm-8">
        <p class="alert alert-warning">
          <strong>Heads up!</strong> When using translations provided by Select2,
          you must make sure to include the translation file in your page after
          Select2.
        </p>
      </div>
    </div>

    <p>
      When a string is passed in as the language, Select2 will try to resolve
      it into a language file. This allows you to specify your own language
      files, which must be defined as an AMD module. If the language file
      cannot be found, Select2 will assume it is a language code controlled by
      Select2, and it will try to load the translations for that language
      instead.
    </p>

    <p>
      You can include your own translations by providing an object similar to
      the one below.
    </p>

<pre class="prettyprint linenums">
language: {
  // You can find all of the options in the language files provided in the
  // build. They all must be functions that return the string that should be
  // displayed.
  inputTooShort: function () {
    return "You must enter more characters...";
  }
}
</pre>

    <h3 id="templating">
      Templating results and selections
    </h3>

    <p>
      By default, Select2 will display the option text within the list of
      results and when the option has been selected.  Select2 comes with options
      that allow you to further customize the display of results and selections,
      allowing you to display them however you want.
    </p>

    <h4 id="templateSelection">
      Customizing the display of selections
    </h4>

    <p>
      When an option is displayed after it has been selected, it is passed
      through a formatting function that determines what is displayed. By
      default, the function only returns the <code>text</code> key of the data
      object.
    </p>

    <dl class="dl-horizontal">
      <dt>Key</dt>
      <dd><code>templateSelection</code></dd>

      <dt>Value</dt>
      <dd>A function taking a <code>selection</code> object</dd>
    </dl>

    <div class="alert alert-info">
      <strong>Anything rendered as a selection is templated.</strong>
      This includes placeholders and pre-existing selections that are displayed,
      so you must ensure that your templating functions can support them.
    </div>

    <p>
      The <code>templateSelection</code> function should return a string
      containing the text to be displayed, or an object (such as a jQuery
      object) that contains the data that should be displayed.
    </p>

    <p>
      <strong>Strings are assumed to contain only text</strong> and will be
      passed through the <code>escapeMarkup</code> function, which strips any
      HTML markup.
    </p>

    <p>
      <strong>
        Anything else will be passed
        <a href="https://api.jquery.com/append/">directly to <code>jQuery.fn.append</code></a>
      </strong> and will be handled directly by jQuery.  Any markup, such as
      HTML, returned will not be escaped and it is up to you to escape any
      malicious input provided by users.
    </p>

    <h4 id="templateResult">
      Customizing the display of results
    </h4>

    <p>
      When an option is displayed after it has been selected, it is passed
      through a formatting function that determines what is displayed. By
      default, the function only returns the <code>text</code> key of the data
      object.
    </p>

    <dl class="dl-horizontal">
      <dt>Key</dt>
      <dd><code>templateSelection</code></dd>

      <dt>Value</dt>
      <dd>A function taking a <code>selection</code> object</dd>
    </dl>

    <div class="alert alert-info">
      <strong>Anything rendered in the results is templated.</strong>
      This includes results such as the "Searching..." and "Loading more..."
      text which will periodically be displayed, which allows you to add more
      advanced formatting to these automatically generated options.
    </div>

    <p>
      The <code>templateResult</code> function should return a string
      containing the text to be displayed, or an object (such as a jQuery
      object) that contains the data that should be displayed.  It can also
      return <code>null</code>, which will prevent the option from being
      displayed in the results list.
    </p>

    <p>
      <strong>Strings are assumed to contain only text</strong> and will be
      passed through the <code>escapeMarkup</code> function, which strips any
      HTML markup.
    </p>

    <p>
      <strong>
        Anything else will be passed
        <a href="https://api.jquery.com/append/">directly to <code>jQuery.fn.append</code></a>
      </strong> and will be handled directly by jQuery.  Any markup, such as
      HTML, returned will not be escaped and it is up to you to escape any
      malicious input provided by users.
    </p>

    <h2>
      Results
    </h2>

    <p>
      Select2 can work on many different data sets ranging from local options,
      the same way that a <code>&lt;select&gt;</code> typically works, from
      remote options where a server generates the results that users can select
      from.
    </p>

    <h3 id="data">
      Array
    </h3>

    <p>
      Select2 allows creating the results based on an array of data objects that
      is included when initializing Select2.
    </p>

    <div class="row">
      <div class="col-sm-6">
        <dl class="dl-horizontal">
          <dt>Key</dt>
          <dd><code>data</code></dd>

          <dt>Value</dt>
          <dd>array of objects</dd>
        </dl>
      </div>

      <div class="col-sm-6">
        <dl class="dl-horizontal">
          <dt>Adapter</dt>
          <dd>
            <code title="select2/data/array">ArrayAdapter</code>
          </dd>
        </dl>
      </div>
    </div>

    <p>
      The objects that the users can select from should be passed as an array
      with each object containing <code>id</code> and <code>text</code>
      properties.
    </p>

    <h3 id="ajax">
      AJAX
    </h3>

    <p>
      Select2 allows searching for results from remote data sources using AJAX
      requests.
    </p>

    <div class="row">
      <div class="col-sm-6">
        <dl class="dl-horizontal">
          <dt>Key</dt>
          <dd><code>ajax</code></dd>

          <dt>Value</dt>
          <dd>object</dd>
        </dl>
      </div>

      <div class="col-sm-6">
        <dl class="dl-horizontal">
          <dt>Adapter</dt>
          <dd>
            <code title="select2/data/ajax">AjaxAdapter</code>
          </dd>
        </dl>
      </div>
    </div>

    <p>
      All options passed to this option will be directly passed to the
      <code>$.ajax</code> function that executes AJAX requests. There are a few
      custom options that Select2 will intercept, allowing you to customize the
      request as it is being made.

<pre class="prettyprint linenums">
ajax: {
  // The number of milliseconds to wait for the user to stop typing before
  // issuing the ajax request.
  delay: 250,
  // You can craft a custom url based on the parameters that are passed into the
  // request. This is useful if you are using a framework which has
  // JavaScript-based functions for generating the urls to make requests to.
  //
  // @param params The object containing the parameters used to generate the
  //   request.
  // @returns The url that the request should be made to.
  url: function (params) {
    return UrlGenerator.Random();
  },
  // You can pass custom data into the request based on the parameters used to
  // make the request. For `GET` requests, the default method, these are the
  // query parameters that are appended to the url. For `POST` requests, this
  // is the form data that will be passed into the request. For other requests,
  // the data returned from here should be customized based on what jQuery and
  // your server are expecting.
  //
  // @param params The object containing the parameters used to generate the
  //   request.
  // @returns Data to be directly passed into the request.
  data: function (params) {
    var queryParameters = {
      q: params.term
    }

    return queryParameters;
  },
  // You can modify the results that are returned from the server, allowing you
  // to make last-minute changes to the data, or find the correct part of the
  // response to pass to Select2. Keep in mind that results should be passed as
  // an array of objects.
  //
  // @param data The data as it is returned directly by jQuery.
  // @returns An object containing the results data as well as any required
  //   metadata that is used by plugins. The object should contain an array of
  //   data objects as the `results` key.
  processResults: function (data) {
    return {
      results: data
    };
  },
  // You can use a custom AJAX transport function if you do not want to use the
  // default one provided by jQuery.
  //
  // @param params The object containing the parameters used to generate the
  //   request.
  // @param success A callback function that takes `data`, the results from the
  //   request.
  // @param failure A callback function that indicates that the request could
  //   not be completed.
  // @returns An object that has an `abort` function that can be called to abort
  //   the request if needed.
  transport: function (params, success, failure) {
    var $request = $.ajax(params);

    $request.then(success);
    $request.fail(failure);

    return $request;
  }
}
</pre>
    </p>

    <h3 id="tags">
      Tags
    </h3>

    <p>
      Users can create their own options based on the text that they have
      entered.
    </p>

    <div class="row">
      <div class="col-sm-6">
        <dl class="dl-horizontal">
          <dt>Key</dt>
          <dd><code>tags</code></dd>

          <dt>Value</dt>
          <dd>boolean / array of objects</dd>
        </dl>
      </div>

      <div class="col-sm-6">
        <dl class="dl-horizontal">
          <dt>Adapter</dt>
          <dd>
            <code title="select2/data/base">DataAdapter</code>
          </dd>

          <dt>Decorator</dt>
          <dd>
            <code title="select2/data/tags">Tags</code>
          </dd>
        </dl>
      </div>
    </div>

    <p>
      If the <code>tags</code> option is passed into Select2, if a user types
      anything into the search box which doesn't already exist, it will be
      displayed at the top and the user will be able to select it.
    </p>

    <p>
      <strong>For backwards compatibility</strong>, if an array of objects is
      passed in with the <code>tags</code> option, the options will be
      automatically created and the user will be able to select from them.
      This is the <strong>same as how <a href="#data">array data</a>
      works</strong>, and has similar limitations.
    </p>

    <h3 id="matcher">
      Change how options are matched when searching
    </h3>

    <p>
      When users filter down the results by entering search terms into the
      search box, Select2 uses an internal "matcher" to match search terms to
      results. <strong>When a remote data set is used, Select2 expects that the
      returned results have already been filtered.</strong>
    </p>

    <dl class="dl-horizontal">
      <dt>Key</dt>
      <dd>
        <code>matcher</code>
      </dd>

      <dt>Value</dt>
      <dd>
        A function taking search <code>params</code> and the
        <code>data</code> object.
      </dd>
    </dl>

    <p>
      Select2 will pass the individual data objects that have been passed back
      from the data adapter into the <code>matcher</code> individually to
      determine if they should be displayed. Only the first-level objects will
      be passed in, so <strong>if you are working with nested data, you need to
      match those individually</strong>.
    </p>

<pre class="prettyprint linenums">
matcher: function (params, data) {
  // If there are no search terms, return all of the data
  if ($.trim(params.term) === '') {
    return data;
  }

  // `params.term` should be the term that is used for searching
  // `data.text` is the text that is displayed for the data object
  if (data.text.indexOf(params.term) > -1) {
    var modifiedData = $.extend({}, data, true);
    modifiedData.text += ' (matched)';

    // You can return modified objects from here
    // This includes matching the `children` how you want in nested data sets
    return modifiedData;
  }

  // Return `null` if the term should not be displayed
  return null;
}
</pre>

    <p>
      This allows for more advanced matching when working with nested objects,
      allowing you to handle them however you want. For those who are not
      looking to implement highly customized matching, but instead are just
      looking to change the matching algorithm for the text, a
      <a href="#compat-matcher">compatibility modules</a> has been created to
      make it easier.
    </p>
  </section>

  <section id="dropdown">
    <div class="page-header">
      <h1>Dropdown</h1>
    </div>

    <p>
      Select2 allows you to change the way that the dropdown works, allowing you
      to do anything from attach it to a different location in the document or
      add a search box.
    </p>

    <h2 id="dropdownParent">
      Attached to body
    </h2>

    <p>
      By default, Select2 will attach the dropdown to the end of the body and
      will absolutely position it to appear below the selection container.
    </p>

    <div class="row">
      <div class="col-sm-4">
        <dl class="dl-horizontal">
          <dt>Key</dt>
          <dd><code>dropdownParent</code></dd>

          <dt>Value</dt>
          <dd>jQuery element or DOM node</dd>

          <hr />

          <dt>Adapter</dt>
          <dd>
            <code title="select2/dropdown">DropdownAdapter</code>
          </dd>

          <dt>Decorator</dt>
          <dd>
            <code title="select2/dropdown/attachBody">AttachBody</code>
          </dd>
        </dl>
      </div>

      <div class="col-sm-8">
        <div class="alert alert-warning">
          <strong>Heads up!</strong>
          This will cause DOM events to be raised outside of the standard
          Select2 DOM container. This can cause issues with
          third-party components such as modals.
        </div>
      </div>
    </div>

    <p>
      When the dropdown is attached to the body, you are not limited to just
      displaying the dropdown below the container. Select2 will display above
      the container if there is not enough space below the container, but there
      is enough space above it. You are also not limited to displaying the
      dropdown within the parent container, which means Select2 will render
      correctly inside of modals and other small containers.
    </p>

    <h2 id="dropdown-attachContainer">
      Attached below the container
    </h2>

    <p>
      Select2 can place the dropdown directly after the selection container, so
      it will appear in the same location within the DOM as the rest of Select2.
    </p>

    <div class="row">
      <div class="col-sm-4">
        <dl class="dl-horizontal">
          <dt>Adapter</dt>
          <dd>
            <code title="select2/dropdown">DropdownAdapter</code>
          </dd>

          <dt>Decorator</dt>
          <dd>
            <code title="select2/dropdown/attachContainer">AttachContainer</code>
          </dd>
        </dl>
      </div>

      <div class="col-sm-8">
        <div class="alert alert-warning">
          <strong>Check your build.</strong> This module is only included in the
          <a href="index.html#builds-full" class="alert-link">full builds</a> of
          Select2.
        </div>
      </div>
    </div>

    <div class="alert alert-info">
      <strong>
        <a href="https://harvesthq.github.io/chosen/">Harvest Chosen</a>
        migrators!
      </strong>
      If you are migrating to Select2 from Chosen, this option will cause
      Select2 to position the dropdown in a similar way.
    </div>

    <h2 id="dropdown-search">
      Search
    </h2>

    <p>
      Users can filter down the results by typing a search term into a box that
      is displayed at the top of the dropdown.
    </p>

    <dl class="dl-horizontal">
      <dt>Adapter</dt>
      <dd>
        <code title="select2/dropdown">DropdownAdapter</code>
      </dd>

      <dt>Decorator</dt>
      <dd>
        <code title="select2/dropdown/search">DropdownSearch</code>
      </dd>
    </dl>

    <p>
      A search box is added to the top of the dropdown automatically for select
      boxes where only a single option can be selected.
    </p>

    <h3 id="dropdown-minimumInputLength">
      Minimum search term length to filter results
    </h3>

    <p>
      Sometimes when working with large data sets, it is more efficient to start
      filtering the results when the search term is a certain length. This is
      very common when working with remote data sets, as allows for only
      significant search terms to be used.
    </p>

    <div class="row">
      <div class="col-sm-6">
        <dl class="dl-horizontal">
          <dt>Key</dt>
          <dd><code>minimumInputLength</code></dd>

          <dt>Value</dt>
          <dd>integer</dd>
        </dl>
      </div>

      <div class="col-sm-6">
        <dl class="dl-horizontal">
          <dt>Adapter</dt>
          <dd>
            <code title="select2/data/base">DataAdapter</code>
          </dd>

          <dt>Decorator</dt>
          <dd>
            <code title="select2/data/minimumInputLength">MinimumInputLength</code>
          </dd>
        </dl>
      </div>
    </div>

    <h3 id="dropdown-maximumInputLength">
      Maximum search term length to filter results
    </h3>

    <p>
      In some cases, search terms need to be limited to a certain range. Select2
      allows you to limit the length of the search term such that it does not
      exceed a certain length.
    </p>

    <div class="row">
      <div class="col-sm-6">
        <dl class="dl-horizontal">
          <dt>Key</dt>
          <dd><code>maximumInputLength</code></dd>

          <dt>Value</dt>
          <dd>integer</dd>
        </dl>
      </div>

      <div class="col-sm-6">
        <dl class="dl-horizontal">
          <dt>Adapter</dt>
          <dd>
            <code title="select2/data/base">DataAdapter</code>
          </dd>

          <dt>Decorator</dt>
          <dd>
            <code title="select2/data/maximumInputLength">MaximumInputLength</code>
          </dd>
        </dl>
      </div>
    </div>

    <h3 id="dropdown-maximumInputLength">
      Minimum results to display the search box
    </h3>

    <p>
      When working with smaller data sets, the search box can take up more space
      that is necessary, as there are not enough results for filtering to be
      effective. Select2 allows you to only display the search box when the
      number of search results reaches a certain threshold.
    </p>

    <div class="row">
      <div class="col-sm-6">
        <dl class="dl-horizontal">
          <dt>Key</dt>
          <dd><code>minimumResultsForSearch</code></dd>

          <dt>Value</dt>
          <dd>integer</dd>
        </dl>
      </div>

      <div class="col-sm-6">
        <dl class="dl-horizontal">
          <dt>Adapter</dt>
          <dd>
            <code title="select2/dropdown">DropdownAdapter</code>
          </dd>

          <dt>Decorator</dt>
          <dd>
            <code title="select2/dropdown/minimumResultsForSearch">MinimumResultsForSearch</code>
          </dd>
        </dl>
      </div>
    </div>

    <h2 id="dropdown-select-on-close">
      Select the highlighted option on close
    </h2>

    <p>
      When users close the dropdown, the last highlighted option can be
      automatically selected. This is commonly used in combination with
      <a href="#tags">tagging</a> and <a href="#placeholder">placeholders</a>
      and other situations where the user is required to select an option, or
      they need to be able to quickly select multiple options.
    </p>

    <dl class="dl-horizontal">
      <dt>Adapter</dt>
      <dd>
        <code title="select2/results">ResultsAdapter</code>
      </dd>

      <dt>Decorator</dt>
      <dd>
        <code title="select2/dropdown/selectOnClose">SelectOnClose</code>
      </dd>
    </dl>

    <h2 id="closeOnSelect">
      Close the dropdown when a result is selected
    </h2>

    <p>
      Select2 will automatically close the dropdown when an element is selected,
      similar to what is done with a normal select box.  This behavior can be
      changed though to keep the dropdown open when results are selected,
      allowing for multiple options to be selected quickly.
    </p>

    <div class="row">
      <div class="col-sm-6">
        <dl class="dl-horizontal">
          <dt>Key</dt>
          <dd><code>closeOnSelect</code></dd>

          <dt>Default</dt>
          <dd><code>true</code></dd>
        </dl>
      </div>

      <div class="col-sm-6">
        <dl class="dl-horizontal">
          <dt>Adapter</dt>
          <dd>
            <code title="select2/dropdown">DropdownAdapter</code>
          </dd>

          <dt>Decorator</dt>
          <dd>
            <code title="select2/dropdown/closeOnSelect">CloseOnSelect</code>
          </dd>
        </dl>
      </div>
    </div>

    <p>
      If this decorator is not used (or <code>closeOnSelect</code> is set to
      <code>false</code>), the dropdown will not automatically close when a
      result is selected.  The dropdown will also never close if the
      <kbd>ctrl</kbd> key is held down when the result is selected.
    </p>
  </section>

  <section id="events">
    <div class="page-header">
      <h1>Events</h1>
    </div>

    <p>
      Select2 has an internal event system that is used to notify parts of the
      component that state has changed, as well as an adapter that allows some
      of these events to be relayed to the outside word.
    </p>

    <dl class="dl-horizontal">
      <dt>Adapter</dt>
      <dd>
        <code title="select2/selection">SelectionAdapter</code>
      </dd>

      <dt>Decorator</dt>
      <dd>
        <code title="select2/selection/eventRelay">EventRelay</code>
      </dd>
    </dl>

    <h2>
      Public events
    </h2>

    <p>
      All public events are relayed using the jQuery event system, and they are
      triggered on the <code>&lt;select&gt;</code> element that Select2 is
      attached to. You can attach to them using the
      <a href="https://api.jquery.com/on/"><code>.on</code> method</a> provided
      by jQuery.
    </p>

    <h2>
      Internal events
    </h2>

    <p>
      Select2 triggers internal events using its own internal event system,
      which allows adapters to communicate with each other. These events are not
      accessible through the jQuery event system.
    </p>

    <p>
      You can find more information on the public events triggered by individual
      adapters in <a href="#adapters">the individual adapter documentation</a>.
    </p>
  </section>

  <section id="adapters">
    <div class="page-header">
      <h1>Adapters</h1>
    </div>

    <p>
      Select2 allows plugins to add additional functionality through the core
      adapters. You can change almost anything involving the way Select2 works
      to the way Select2 interacts with the page by modifying the core adapters.
      Most third-party plugins should provide decorators (used to wrap adapters)
      and custom adapters that you can use.
    </p>

    <p>
      Each adapter contains a set of methods which will must always be defined.
      Along with the global methods that all adapters must implement, these
      methods must be implemented.
    </p>

    <h2>
      All adapters
    </h2>

    <p>
      All adapters must implement a set of methods that Select2 will use to
      display them and bind any internal events.
    </p>

<pre class="prettyprint linenums">
// The basic HTML that should be rendered by Select2. A jQuery or DOM element
// should be returned, which will automatically be placed by Select2 within the
// DOM.
//
// @returns A jQuery or DOM element that contains any elements that must be
//   rendered by Select2.
Adapter.render = function () {
  return $jq;
};

// Bind to any Select2 or DOM events.
//
// @param container The Select2 object that is bound to the jQuery element.  You
//   can listen to Select2 events with `on` and trigger Select2 events using the
//   `trigger` method.
// @param $container The jQuery DOM node that all default adapters will be
//   rendered within.
Adapter.bind = function (container, $container) { };

// Position the DOM element within the Select2 DOM container, or in another
// place. This allows adapters to be located outside of the Select2 DOM,
// such as at the end of the document or in a specific place within the Select2
// DOM node.
//
// Note: This method is not called on data adapters.
//
// @param $rendered The rendered DOM element that was returned from the call to
//   `render`. This may have been modified by Select2, but the root element
//   will always be the same.
// @param $defaultContainer The default container that Select2 will typically
//   place the rendered DOM element within. For most adapters, this is the
//   Select2 DOM element.
Adapter.position = function ($rendered, $defaultContainer) { };

// Destroy any events or DOM elements that have been created.
// This is called when `select2("destroy")` is called on an element.
Adapter.destroy = function () { };
</pre>

    <h2 id="selectionAdapter">
      Container (selection)
    </h2>

    <p>
      The selection is what is shown to the user as a replacement of the
      standard <code>&lt;select&gt;</code> box. It controls the display of the
      selection option(s), as well anything else that needs to be embedded
      within the container, such as a search box.
    </p>

    <dl class="dl-horizontal">
      <dt>Key</dt>
      <dd>
        <code>selectionAdapter</code>
      </dd>

      <dt>Default</dt>
      <dd>
        <code title="select2/selection/single">SingleSelection</code> or
        <code title="select2/selection/multiple">MultipleSelection</code>
      </dd>

      <dt>Base</dt>
      <dd>
        <code title="select2/selection/base">BaseSelection</code>
      </dd>
    </dl>

<pre class="prettyprint linenums">
// Update the selected data.
//
// @param data An array of data objects that have been generated by the data
//   adapter. If no objects should be selected, an empty array will be passed.
//
// Note: An array will always be passed into this method, even if Select2 is
// attached to a source which only accepts a single selection.
SelectionAdapter.update = function (data) { };
</pre>

    <h2 id="dataAdapter">
      Data set
    </h2>

    <p>
      The data set is what Select2 uses to generate the possible results that
      can be selected, as well as the currently selected results.
    </p>

    <dl class="dl-horizontal">
      <dt>Key</dt>
      <dd>
        <code>dataAdapter</code>
      </dd>

      <dt>Default</dt>
      <dd>
        <code title="select2/data/select">SelectAdapter</code>
      </dd>

      <dt>Base</dt>
      <dd>
        <code title="select2/data/base">BaseAdapter</code>
      </dd>
    </dl>

<pre class="prettyprint linenums">
// Get the currently selected options. This is called when trying to get the
// initial selection for Select2, as well as when Select2 needs to determine
// what options within the results are selected.
//
// @param callback A function that should be called when the current selection
//   has been retrieved. The first parameter to the function should be an array
//   of data objects.
DataAdapter.current = function (callback) {
  callback(currentData);
}

// Get a set of options that are filtered based on the parameters that have
// been passed on in.
//
// @param params An object containing any number of parameters that the query
//   could be affected by. Only the core parameters will be documented.
// @param params.term A user-supplied term. This is typically the value of the
//   search box, if one exists, but can also be an empty string or null value.
// @param params.page The specific page that should be loaded. This is typically
//   provided when working with remote data sets, which rely on pagination to
//   determine what objects should be displayed.
// @param callback The function that should be called with the queried results.
DataAdapter.query = function (params, callback) {
  callback(queryiedData);
}
</pre>

    <h2 id="dropdownAdapter">
      Dropdown
    </h2>

    <p>
      The dropdown adapter defines the main container that the dropdown should
      be held in. <strong>It does not define any extra methods that can be used
      for decorators</strong>, but it is common for decorators to attach to the
      <code>render</code> and <code>position</code> methods to alter how the
      dropdown is altered and positioned.
    </p>

    <dl class="dl-horizontal">
      <dt>Key</dt>
      <dd>
        <code>dropdownAdapter</code>
      </dd>

      <dt>Default</dt>
      <dd>
        <code title="select2/dropdown">DropdownAdapter</code>
      </dd>
    </dl>

    <h2 id="resultsAdapter">
      Results
    </h2>

    <p>
      The results adapter controls the list of results that the user can select
      from. While the results adapter does not define any additional methods
      that must be implemented, it makes extensive use of the Select2 event
      system for controlling the display of results and messages.
    </p>

    <dl class="dl-horizontal">
      <dt>Key</dt>
      <dd>
        <code>resultsAdapter</code>
      </dd>

      <dt>Default</dt>
      <dd>
        <code title="select2/results">ResultsAdapter</code>
      </dd>
    </dl>
  </section>

  <section id="defaults">
    <div class="page-header">
      <h1>Setting default options</h1>
    </div>

    <p>
      In some cases, you need to set the default options for all instances of
      Select2 in your web application. This is especially useful when you are
      migrating from past versions of Select2, or you are using non-standard
      options <a href="#amd">like custom AMD builds</a>. Select2 exposes the
      default options through <code>$.fn.select2.defaults</code>, which allows
      you to set them globally.
    </p>

    <p>
      When setting options globally, any past defaults that have been set will
      be overriden. Default options are only used when an option is requested
      that has not been set during initialization.
    </p>

    <p>
      <strong>You can set default options</strong> by calling
      <code>$.fn.select2.defaults.set("key", "value")</code>. The key that is
      set should take the same format as keys set using
      <a href="#data-attributes">HTML <code>data-*</code> attributes</a> which
      means that two dashes (<code>--</code>) will be replaced by a level of
      nesting, and a single dash (<code>-</code>) will convert it to a camelCase
      string.
    </p>

<pre class="prettyprint linenums">
$.fn.select2.defaults.set("theme", "classic");
</pre>

    <p>
      <strong>You can reset the default options</strong> by calling
      <code>$.fn.select2.defaults.reset()</code>.
    </p>
  </section>

  <section id="compatibility">
    <div class="page-header">
      <h1>Backwards compatibility</h1>
    </div>

    <p>
      Select2 offers limited backwards compatibility with the previously 3.5.x
      release line, allowing people more efficiently transfer across releases
      and get the latest features. For many of the larger changes, such as the
      change in how custom data adapters work, compatibility modules were
      created that will be used to assist in the upgrade process. It is not
      recommended to rely on these compatibility modules, as they will not
      always exist, but they make upgrading easier for major changes.
    </p>

    <p>
      <strong>The compatibility modules are only included in the
      <a href="index.html#builds-full" class="alert-link">full builds</a> of
      Select2</strong>. These files end in <code>.full.js</code>, and the
      compatibility modules are prefixed with <code>select2/compat</code>.
    </p>

    <h2 id="compat-matcher">
      Simplified function for matching data objects
    </h2>

    <p class="alert alert-info">
      <a href="announcements-4.0.html#new-matcher" class="alert-link">Added in Select2 4.0.0.</a>
      This method was added to make upgrading easier from earlier versions of
      Select2.
    </p>

    <p>
      During the <a href="announcements-4.0.html">Select2 4.0.0 release</a>, the
      <code>matcher</code> function was changed to allow for more complex
      matching of nested objects.
    </p>

    <div class="row">
      <div class="col-sm-6">
        <dl class="dl-horizontal">
          <dt>Key</dt>
          <dd>
            <code>matcher</code>
          </dd>

          <dt>Value</dt>
          <dd>
            A function taking a search <code>term</code> and the data object
            <code>text</code>.
          </dd>
        </dl>
      </div>

      <div class="col-sm-6">
        <dl class="dl-horizontal">
          <dt>Adapter</dt>
          <dd>
            <code title="select2/compat/matcher">oldMatcher</code>
          </dd>
        </dl>
      </div>
    </div>

    <p>
      The <a href="examples.html#matcher">custom matcher example</a> provides a
      guide for how to use this in your own application. For those upgrading
      from older versions of Select2, you just need to wrap your old
      <code>matcher</code> with this function to maintain compatibility.
    </p>

    <h2 id="initSelection">
      Old initial selections with <code>initSelection</code>
    </h2>

    <p class="alert alert-warning">
      <a href="announcements-4.0.html#removed-initselection" class="alert-link">Deprecated in Select2 4.0.</a>
      This has been replaced by another option and is only available in the
      <a href="index.html#builds-full" class="alert-link">full builds</a> of
      Select2.
    </p>

    <p>
      In the past, Select2 required an option called <code>initSelection</code>
      that was defined whenever a custom data source was being used, allowing
      for the initial selection for the component to be determined. This has
      been replaced by the <code>current</code> method on the
      <a href="#dataAdapter">data adapter</a>.
    </p>

    <div class="row">
      <div class="col-sm-6">
        <dl class="dl-horizontal">
          <dt>Key</dt>
          <dd>
            <code>initSelection</code>
          </dd>

          <dt>Value</dt>
          <dd>
            A function taking a <code>callback</code>
          </dd>
        </dl>
      </div>

      <div class="col-sm-6">
        <dl class="dl-horizontal">
          <dt>Adapter</dt>
          <dd>
            <code title="select2/data/base">DataAdapter</code>
          </dd>

          <dt>Decorator</dt>
          <dd>
            <code title="select2/compat/initSelection">InitSelection</code>
          </dd>
        </dl>
      </div>
    </div>

    <h2 id="query">
      Querying old data with <code>query</code>
    </h2>

    <p class="alert alert-warning">
      <a href="announcements-4.0.html#query-to-data-adapter" class="alert-link">Deprecated in Select2 4.0.</a>
      This has been replaced by another option and is only available in the
      <a href="index.html#builds-full" class="alert-link">full builds</a> of
      Select2.
    </p>

    <p>
      In the past, Select2 supported an option called <code>query</code> that
      allowed for a custom data source to be used. This option has been replaced
      by the <code>query</code> method on the
      <a href="#dataAdapter">data adapter</a> and takes a very similar set of
      parameters.
    </p>

    <div class="row">
      <div class="col-sm-6">
        <dl class="dl-horizontal">
          <dt>Key</dt>
          <dd>
            <code>query</code>
          </dd>

          <dt>Value</dt>
          <dd>
            A function taking <code>params</code> (including a <code>callback</code>)
          </dd>
        </dl>
      </div>

      <div class="col-sm-6">
        <dl class="dl-horizontal">
          <dt>Adapter</dt>
          <dd>
            <code title="select2/data/base">DataAdapter</code>
          </dd>

          <dt>Decorator</dt>
          <dd>
            <code title="select2/compat/query">Query</code>
          </dd>
        </dl>
      </div>
    </div>
  </section>

  <h2 id="input-fallback">
    Compatibility with <code>&lt;input type="text" /&gt;</code>
  </h2>

  <p class="alert alert-warning">
    <a href="announcements-4.0.html#hidden-input" class="alert-link">Deprecated in Select2 4.0.</a>
    It is now encouraged to use the <code>&lt;select&gt;</code> tag instead.
  </p>

  <p>
    In past versions of Select2, a <code>&lt;select&gt;</code> element could
    only be used with a limited subset of options. An
    <code>&lt;input type="hidden" /&gt;</code> was required instead, which did
    not allow for a graceful fallback for users who did not have JavaScript
    enabled. Select2 now supports the <code>&lt;select&gt;</code> element for
    all options, so it is no longer required to use <code>&lt;input /&gt;</code>
    elements with Select2.
  </p>

  <dl class="dl-horizontal">
    <dt>Adapter</dt>
    <dd>
      <code title="select2/data/base">DataAdapter</code>
    </dd>

    <dt>Decorator</dt>
    <dd>
      <code title="select2/compat/inputData">InputData</code>
    </dd>
  </dl>
</section>
</div>

<script type="text/javascript">
prettyPrint();
</script>
