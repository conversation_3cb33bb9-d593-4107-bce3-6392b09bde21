---
layout: home
title: Community - Select2
slug: community
---

<section class="jumbotron">
  <div class="container">
    <h1>
      <i class="fa fa-comments"></i>
      Community
    </h1>
  </div>
</section>

<div class="container">
  <p class="lead">
    Select2 has an active community that enjoys helping each other out.
  </p>

  <section id="support">
    <div class="page-header">
      <h1>Getting support</h1>
    </div>

    <p>
      Having trouble getting Select2 working on your website? Is it not working
      together with another plugin, even though you think it should? Select2 has a
      few communities that you can go to for help getting it all working together.
    </p>

    <ol>
      <li>
        Try sending a message to the
        <a href="https://groups.google.com/d/forum/select2">
          Select2 discussion group
        </a>
        or searching the archives.
      </li>
      <li>
        Ask in the <code>#select2</code> channel on
        <code>chat.freenode.net</code> or use the
        <a href="https://webchat.freenode.net/?channels=select2">
          web irc client.
        </a>
      </li>
      <li>
        Raise a question on
        <a href="http://stackoverflow.com/">Stack Overflow</a>,
        make sure to include the
        <a href="http://stackoverflow.com/questions/tagged/jquery-select2?sort=votes">
          "jquery-select2"
        </a>
        tag.
      </li>
    </ol>
  </section>

  <section id="reporting-bugs">
    <div class="page-header">
      <h1>Reporting bugs</h1>
    </div>

    <p>
      Found a problem with Select2? Feel free to open a ticket on the Select2
      repository on GitHub, but you should keep a few things in mind:
    </p>

    <ol>
      <li>
        Use the
        <a href="https://github.com/select2/select2/search?q=&type=Issues">
          GitHub issue search
        </a>
        to check if your issue has already been reported.
      </li>
      <li>
        Try to isolate your problem as much as possible, so we can easily test if
        the issue has been fixed.
      </li>
      <li>
        Once you are sure the issue is with Select2, and not a third party
        library,
        <a href="https://github.com/select2/select2/issues/new">
          submit a ticket
        </a>
        to the repository.
      </li>
    </ol>

    <p>
      You can find more information on reporting bugs in the
      <a href="https://github.com/select2/select2/blob/master/CONTRIBUTING.md#reporting-bugs-with-select2">
        contributing guide,
      </a>
      including tips on what information to include.
    </p>
  </section>

  <section id="requesting-features">
    <div class="page-header">
      <h1>Requesting new features</h1>
    </div>

    <p>
      New feature requests are usually requested by the
      <a href="https://github.com/select2/select2/issues">
        Select2 community on GitHub,
      </a>
      and are often fulfilled by
      <a href="#contributing">
        fellow contributors.
      </a>
    </p>

    <ol>
      <li>
        Use the
        <a href="https://github.com/select2/select2/search?q=&type=Issues">
          GitHub issue search
        </a>
        to check if your feature has already been requested.
      </li>
      <li>
        Check if it hasn't already been implemented as a
        <a href="">
          third party plugin.
        </a>
      </li>
      <li>
        Please make sure you are only requesting a single feature, and not a
        collection of smaller features.
      </li>
    </ol>

    <p>
      You can find more information on requesting new features in the
      <a href="https://github.com/select2/select2/blob/master/CONTRIBUTING.md#requesting-features-in-select2">
        contributing guide.
      </a>
    </p>
  </section>

  <section id="contributing">
    <div class="page-header">
      <h1>Getting involved</h1>
    </div>

    <p>
      You can find more information on triaging tickets in the
      <a href="https://github.com/select2/select2/blob/master/CONTRIBUTING.md#triaging-issues-and-pull-requests">
        contributing guide.
      </a>
    </p>
  </section>
</div>
