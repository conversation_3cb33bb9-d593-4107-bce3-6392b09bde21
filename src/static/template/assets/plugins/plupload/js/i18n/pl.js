// Polish (pl)
plupload.addI18n({"Stop Upload":"Przerwij transfer.","Upload URL might be wrong or doesn't exist.":"Upload URL might be wrong or doesn't exist.","tb":"","Size":"Rozmiar","Close":"Close","Init error.":"Błąd inicjalizacji.","Add files to the upload queue and click the start button.":"Dodaj pliki i kliknij 'Rozpocznij transfer'.","Filename":"Nazwa pliku","Image format either wrong or not supported.":"Image format either wrong or not supported.","Status":"Status","HTTP Error.":"Błąd HTTP.","Start Upload":"Start Upload","mb":"","kb":"","Duplicate file error.":"","File size error.":"Plik jest zbyt duży.","N/A":"Nie dostępne","gb":"","Error: Invalid file extension:":"Error: Invalid file extension:","Select files":"Wybierz pliki:","%s already present in the queue.":"","File: %s":"File: %s","b":"","Uploaded %d/%d files":"Wysłano %d/%d plików","Upload element accepts only %d file(s) at a time. Extra files were stripped.":"Upload element accepts only %d file(s) at a time. Extra files were stripped.","%d files queued":"%d plików w kolejce.","File: %s, size: %d, max file size: %d":"","Drag files here.":"Przeciągnij tu pliki","Runtime ran out of available memory.":"Runtime ran out of available memory.","File count error.":"File count error.","File extension error.":"Nieobsługiwany format pliku.","Error: File too large:":"Error: File too large:","Add Files":"Dodaj pliki"});