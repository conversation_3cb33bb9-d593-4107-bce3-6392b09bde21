<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1" />
<title>Test for jQuery validate() plugin</title>
<link rel="stylesheet" type="text/css" media="screen" href="css/screen.css" />
<style type="text/css">
.warning { color: red; }
</style>
<script src="../lib/jquery.js"></script>
<script src="../lib/jquery.mockjax.js"></script>
<script src="../lib/jquery.form.js"></script>
<script src="../jquery.validate.js"></script>

<script type="text/javascript">
	jQuery(function() {
		$.mockjax({
			url: "login.action",
			response: function(settings) {
				var user = settings.data.match( /user=(.+?)($|&)/ )[ 1 ],
					password = settings.data.match( /password=(.+?)($|&)/ )[ 1 ];
				if ( password !== "foobar" ) {
					this.responseText = "Your password is wrong (must be foobar).";
					return;
				}
				this.responseText = "Hi " + user + ", welcome back.";
			},
			responseStatus: 200,
			responseTime: 500
		});

		// show a simple loading indicator
		var loader = jQuery('<div id="loader"><img src="images/loading.gif" alt="loading..." /></div>')
			.css({position: "relative", top: "1em", left: "25em", display: "inline"})
			.appendTo("body")
			.hide();
		jQuery().ajaxStart(function() {
			loader.show();
		}).ajaxStop(function() {
			loader.hide();
		}).ajaxError(function(a, b, e) {
			throw e;
		});

		var v = jQuery("#form").validate({
			submitHandler: function(form) {
				jQuery(form).ajaxSubmit({
					target: "#result"
				});
			}
		});

		jQuery("#reset").click(function() {
			v.resetForm();
		});
	});
</script>

</head>
<body>

<h1 id="banner"><a href="http://bassistance.de/jquery-plugins/jquery-plugin-validation/">jQuery Validation Plugin</a> Demo</h1>
<div id="main">

<form method="post" class="cmxform" id="form" action="login.action">
	<fieldset>
		<legend>Login Form (Enter "foobar" as password)</legend>
		<p>
			<label for="user">Username</label>
			<input id="user" name="user" title="Please enter your username (at least 3 characters)" class="required" minlength="3" />
		</p>
		<p>
			<label for="pass">Password</label>
			<input type="password" name="password" id="password" class="required" minlength"5" />
		</p>
		<p>
			<input class="submit" type="submit" value="Login"/>
		</p>
	</fieldset>
</form>

<div id="result" class="warning">Please login!</div>

<br/>

<button id="reset">Programmatically reset above form!</button>

<a href="index.html">Back to main page</a>

</div>


</body>
</html>
