{"name": "validation", "title": "j<PERSON>uery <PERSON>", "description": "Form validation made easy. Validate a simple comment form with inline rules, or a complex signup form with powerful remote checks.", "keywords": ["forms", "validation", "validate"], "version": "1.11.1", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "joern.z<PERSON><PERSON><PERSON>@gmail.com", "url": "http://bassistance.de"}, "licenses": [{"type": "MIT", "url": "http://www.opensource.org/licenses/MIT"}], "bugs": "https://github.com/jzaefferer/jquery-validation/issues", "homepage": "https://github.com/jzaefferer/jquery-validation", "docs": "http://docs.jquery.com/Plugins/Validation", "download": "http://bassistance.de/jquery-plugins/jquery-plugin-validation/", "dependencies": {"jquery": ">=1.4.4"}}