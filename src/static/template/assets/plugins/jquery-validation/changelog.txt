1.11.1 / 2013-03-22
==================

  * Revert to also converting parameters of range method to numbers. Closes gh-702
  * Replace most usage of PHP with mockjax handlers. Do some demo cleanup as well, update to newer masked-input plugin. Keep captcha demo in PHP. Fixes #662
  * Remove inline code highlighting from milk demo. View source works fine.
  * Fix dynamic-totals demo by trimming whitespace from template content before passing to jQuery constructor
  * Fix min/max validation. Closes gh-666. Fixes #648
  * Fixed 'messages' coming up as a rule and causing an exception after being updated through rules("add"). Closes gh-670, fixes #624
  * Add Korean (ko) localization. Closes gh-671
  * Improved the UK postcode method to filter out more invalid postcodes. Closes #682
  * Update messages_sv.js. Closes #683
  * Change grunt link to the project website. Closes #684
  * Move remote method down the list to run last, after all other methods applied to a field. Fixes #679
  * Update plugin.json description, should include the word 'validate'
  * Fix typos
  * Fix jQuery loader to use path of itself. Fixes nested demos.
  * Update grunt-contrib-qunit to make use of PhantomJS 1.8, when installed through node module 'phantomjs'
  * Make valid() return a boolean instead of 0 or 1. Fixes #109 - valid() does not return boolean value

1.11.0 / 2013-02-04
==================

  * Remove clearing as numbers of `min`, `max` and `range` rules. Fixes #455. Closes gh-528.
  * Update pre-existing labels - fixes #430 closes gh-436
  * Fix $.validator.format to avoid group interpolation, where at least IE8/9 replaces -bash with the match. Fixes #614
  * Fix mimetype regex
  * Add plugin manifest and update headers to just MIT license, drop unnecessary dual-licensing (like jQuery).
  * Hebrew messages: Removed dots at end of sentences - Fixes gh-568
  * French translation for require_from_group validation. Fixes gh-573.
  * Allow groups to be an array or a string - Fixes #479
  * Removed spaces with multiple MIME types
  * Fix some date validations, JS syntax errors.
  * Remove support for metadata plugin, replace with data-rule- and data-msg- (added in 907467e8) properties.
  * Added sftp as a valid url-pattern
  * Add Malay (my) localization
  * Update localization/messages_hu.js
  * Remove focusin/focusout polyfill. Fixes #542 - Inclusion of jquery.validate interfers with focusin and focusout events in IE9
  * Localization: Fixed typo in finnish translation
  * Fix RTM demo to show invalid icon when going from valid back to invalid
  * Fixed premature return in remote function which prevented ajax call from being made in case an input was entered too quickly. Ensures remote validation always validates the newest value.
  * Undo fix for #244. Fixes #521 - E-mail validation fires immediately when text is in the field.

1.10.0 / 2012-09-07
===================

  * Corrected French strings for nowhitespace, phoneUS, phoneUK and mobileUK based upon community feedback.
  * rename files for language_REGION according to the standard ISO_3166-1 (http://en.wikipedia.org/wiki/ISO_3166-1), for Taiwan tha language is Chinese (zh) and the region is Taiwan (TW)
  * Optimise RegEx patterns, especially for UK phone numbers.
  * Add Language Name for each file, rename the language code according to the standard ISO 639 for Estonian, Georgian, Ukrainian and Chinese (http://en.wikipedia.org/wiki/List_of_ISO_639-1_codes)
  * Added croatian (HR) localization
  * Existing French translations were edited and French translations for the additional methods were added.
  * Merged in changes for specifying custom error messages in data attributes
  * Updated UK Mobile phone number regex for new numbers. Fixes #154
  * Add element to success call with test. Fixes #60
  * Fixed regex for time additional method. Fixes #131
  * resetForm now clears old previousValue on form elements. Fixes #312
  * Added checkbox test to require_from_group and changed require_from_group to use elementValue. Fixes #359
  * Fixed dataFilter response issues in jQuery 1.5.2+. Fixes #405
  * Added jQuery Mobile demo. Fixes #249
  * Deoptimize findByName for correctness. Fixes #82 - $.validator.prototype.findByName breaks in IE7
  * Added US zip code support and test. Fixes #90
  * Changed lastElement to lastActive in keyup, skip validation on tab or empty element. Fixes #244
  * Removed number stripping from stripHtml. Fixes #2
  * Fixed invalid count on invalid to valid remote validation. Fixes #286
  * Add link to file_input to demo index
  * Moved old accept method to extension additional-method, added new accept method to handle standard browser mimetype filtering. Fixes #287 and supersedes #369
  * Disables blur event when onfocusout is set to false. Test added.
  * Fixed value issue for radio buttons and checkboxes. Fixes #363
  * Added test for rangeWords and fixed regex and bounds in method. Fixes #308
  * Fixed TinyMCE Demo and added link on demo page. Fixes #382
  * Changed localization message for min/max. Fixes #273
  * Added pseudo selector for text input types to fix issue with default empty type attribute. Added tests and some test markup. Fixes #217
  * Fixed delegate bug for dynamic-totals demo. Fixes #51
  * Fix incorrect message for alphanumeric validator
  * Removed incorrect false check on required attribute
  * required attribute fix for non-html5 browsers. Fixes #301
  * Added methods "require_from_group" and "skip_or_fill_minimum"
  * Use correct iso code for swedish
  * Updated demo HTML files to use HTML5 doctype
  * Fixed regex issue for decimals without leading zeroes. Added new methods test. Fixes #41
  * Introduce a elementValue method that normalizes only string values (don't touch array value of multi-select). Fixes #116
  * Support for dynamically added submit buttons, and updated test case. Uses validateDelegate. Code from PR #9
  * Fix bad double quote in test fixtures
  * Fix maxWords method to include the upper bound, not exclude it. Fixes #284
  * Fixed grammar error in german range validator message. Fixes #315
  * Fixed handling of multiple class names for errorClass option. Test by Max Lynch. Fixes #280
  * Fix jQuery.format usage, should be $.validator.format. Fixes #329
  * Methods for 'all' UK phone numbers + UK postcodes
  * Pattern method: Convert string param to RegExp. Fixes issue #223
  * grammar error in german localization file
  * Added Estonian localization for messages
  * Improve tooltip handling on themerollered demo
  * Add type="text" to input fields without type attribute to please qSA
  * Update themerollered demo to use tooltip to show errors as overlay.
  * Update themerollered demo to use latest jQuery UI (along with newer jQuery version). Move code around to speed up page load.
  * Fixed min error message broken in Japanese.
  * Update form plugin to latest version. Enhance the ajaxSubmit demo.
  * Drop dateDE and numberDE methods from classRuleSettings, leftover from moving those to localized methods
  * Passing submit event to submitHandler callback
  * Fixed #219 - Fix valid() on elements with dependency-callback or dependency-expression.
  * Improve build to remove dist dir to ensure only the current release gets zipped up

1.9.0
---
* Added Basque (EU) localization
* Added Slovenian (SL) localization
* Fixed issue #127 - Finnish translations has one : instead of ;
* Fixed Russian localization, minor syntax issue
* Added in support for HTML5 input types, fixes #97
* Improved HTML5 support by setting novalidate attribute on the form, and reading the type attribute.
* Fixed showLabel() removing all classes from error element. Remove only settings.validClass. Fixes #151.
* Added 'pattern' to additional-methods to validate against arbitrary regular expressions.
* Improved email method to not allow the dot at the end (valid by RFC, but unwanted here). Fixes #143
* Fixed swedish and norwegian translations, min/max messages got switched. Fixes #181
* Fixed #184 - resetForm: should unset lastElement
* Fixed #71 - improve existing time method and add time12h method for 12h am/pm time format
* Fixed #177 - Fix validation of a single radio or checkbox input
* Fixed #189 - :hidden elements are now ignored by default
* Fixed #194 - Required as attribute fails if jQuery>=1.6 - Use .prop instead of .attr
* Fixed #47, #39, #32 - Allowed credit card numbers to contain spaces as well as dashes (spaces are commonly input by users).

1.8.1
---
* Added Thai (TH) localization, fixes #85
* Added Vietnamese (VI) localization, thanks Ngoc
* Fixed issue #78. Error/Valid styling applies to all radio buttons of same group for required validation.
* Don't use form.elements as that isn't supported in jQuery 1.6 anymore. Its buggy as hell anyway (IE6-8: form.elements === form).

1.8.0
---
* Improved NL localization (http://plugins.jquery.com/node/14120)
* Added Georgian (GE) localization, thanks Avtandil Kikabidze
* Added Serbian (SR) localization, thanks Aleksandar Milovac
* Added ipv4 and ipv6 to additional methods, thanks Natal Ngétal
* Added Japanese (JA) localization, thanks Bryan Meyerovich
* Added Catalan (CA) localization, thanks Xavier de Pedro
* Fixed missing var statements within for-in loops
* Fix for remote validation, where a formatted message got messed up (https://github.com/jzaefferer/jquery-validation/issues/11)
* Bugfixes for compatibility with jQuery 1.5.1, while maintaining backwards-compatibility

1.7
---
* Added Lithuanian (LT) localization
* Added Greek (EL) localization (http://plugins.jquery.com/node/12319)
* Added Latvian (LV) localization (http://plugins.jquery.com/node/12349)
* Added Hebrew (HE) localization (http://plugins.jquery.com/node/12039)
* Fixed Spanish (ES) localization (http://plugins.jquery.com/node/12696)
* Added jQuery UI themerolled demo
* Removed cmxform.js
* Fixed four missing semicolons (http://plugins.jquery.com/node/12639)
* Renamed phone-method in additional-methods.js to phoneUS
* Added phoneUK and mobileUK methods to additional-methods.js (http://plugins.jquery.com/node/12359)
* Deep extend options to avoid modifying multiple forms when using the rules-method on a single element (http://plugins.jquery.com/node/12411)
* Bugfixes for compatibility with jQuery 1.4.2, while maintaining backwards-compatibility

1.6
---
* Added Arabic (AR), Portuguese (PTPT), Persian (FA), Finnish (FI) and Bulgarian (BR) localization
* Updated Swedish (SE) localization (some missing html iso characters)
* Fixed $.validator.addMethod to properly handle empty string vs. undefined for the message argument
* Fixed two accidental global variables
* Enhanced min/max/rangeWords (in additional-methods.js) to strip html before counting; good when counting words in a richtext editor
* Added localized methods for DE, NL and PT, removing the dateDE and numberDE methods (use messages_de.js and methods_de.js with date and number methods instead)
* Fixed remote form submit synchronization, kudos to Matas Petrikas
* Improved interactive select validation, now validating also on click (via option or select, inconsistent across browsers); doesn't work in Safari, which doesn't trigger a click event at all on select elements; fixes http://plugins.jquery.com/node/11520
* Updated to latest form plugin (2.36), fixing http://plugins.jquery.com/node/11487
* Bind to blur event for equalTo target to revalidate when that target changes, fixes http://plugins.jquery.com/node/11450
* Simplified select validation, delegating to jQuery's val() method to get the select value; should fix http://plugins.jquery.com/node/11239
* Fixed default message for digits (http://plugins.jquery.com/node/9853)
* Fixed issue with cached remote message (http://plugins.jquery.com/node/11029 and http://plugins.jquery.com/node/9351)
* Fixed a missing semicolon in additional-methods.js (http://plugins.jquery.com/node/9233)
* Added automatic detection of substitution parameters in messages, removing the need to provide format functions (http://plugins.jquery.com/node/11195)
* Fixed an issue with :filled/:blank somewhat caused by Sizzle (http://plugins.jquery.com/node/11144)
* Added an integer method to additional-methods.js (http://plugins.jquery.com/node/9612)
* Fixed errorsFor method where the for-attribute contains characters that need escaping to be valid inside a selector (http://plugins.jquery.com/node/9611)

1.5.5
---
* Fix for http://plugins.jquery.com/node/8659
* Fixed trailing comma in messages_cs.js

1.5.4
---
* Fixed remote method bug (http://plugins.jquery.com/node/8658)

1.5.3
---
* Fixed a bug related to the wrapper-option, where all ancestor-elements that matched the wrapper-option where selected (http://plugins.jquery.com/node/7624)
* Updated multipart demo to use latest jQuery UI accordion
* Added dateNL and time methods to additionalMethods.js
* Added Traditional Chinese (Taiwan, tw) and Kazakhstan (KK) localization
* Moved jQuery.format (formerly String.format) to jQuery.validator.format, jQuery.format is deprecated and will be removed in 1.6 (see http://code.google.com/p/jquery-utils/issues/detail?id=15 for details)
* Cleaned up messages_pl.js and messages_ptbr.js (still defined messages for max/min/rangeValue, which were removed in 1.4)
* Fixed flawed boolean logic in valid-plugin-method for multiple elements; now all elements need to be valid for a boolean-true result (http://plugins.jquery.com/node/8481)
* Enhancement $.validator.addMethod: An undefined third message-argument won't overwrite an existing message (http://plugins.jquery.com/node/8443)
* Enhancement to submitHandler option: When used, click events on submit buttons are captured and the submitting button is inserted into the form before calling submitHandler, and removed afterwards; keeps submit buttons intact (http://plugins.jquery.com/node/7183#comment-3585)
* Added option validClass, default "valid", which adds that class to all valid elements, after validation (http://dev.jquery.com/ticket/2205)
* Added creditcardtypes method to additionalMethods.js, including tests (via http://dev.jquery.com/ticket/3635)
* Improved remote method to allow serverside message as a string, or true for valid, or false for invalid using the clientside defined message (http://dev.jquery.com/ticket/3807)
* Improved accept method to also accept a Drupal-style comma-seperated list of values (http://plugins.jquery.com/node/8580)

1.5.2
---
* Fixed messages in additional-methods.js for maxWords, minWords, and rangeWords to include call to $.format
* Fixed value passed to methods to exclude carriage return (\r), same as jQuery's val() does
* Added slovak (sk) localization
* Added demo for integration with jQuery UI tabs
* Added selects-grouping example to tabs demo (see second tab, birthdate field)

1.5.1
---
* Updated marketo demo to use invalidHandler option instead of binding invalid-form event
* Added TinyMCE integration example
* Added ukrainian (ua) localization
* Fixed length validation to work with trimmed value (regression from 1.5 where general trimming before validation was removed)
* Various small fixes for compatibility with both 1.2.6 and 1.3

1.5
---
* Improved basic demo, validating confirm-password field after password changed
* Fixed basic validation to pass the untrimmed input value as the first parameter to validation methods, changed required accordingly; breaks existing custom method that rely on the trimming
* Added norwegian (no), italian (it), hungarian (hu) and romanian (ro) localization
* Fixed #3195: Two flaws in swedish localization
* Fixed #3503: Extended rules("add") to accept messages property: use to specify add custom messages to an element via rules("add", { messages: { required: "Required! " } });
* Fixed #3356: Regression from #2908 when using meta-option
* Fixed #3370: Added ignoreTitle option, set to skip reading messages from the title attribute, helps to avoid issues with Google Toolbar; default is false for compatibility
* Fixed #3516: Trigger invalid-form event even when remote validation is involved
* Added invalidHandler option as a shortcut to bind("invalid-form", function() {})
* Fixed Safari issue for loading indicator in ajaxSubmit-integration-demo (append to body first, then hide)
* Added test for creditcard validation and improved default message
* Enhanced remote validation, accepting options to passthrough to $.ajax as parameter (either url string or options, including url property plus everything else that $.ajax supports)

1.4
---
* Fixed #2931, validate elements in document order and ignore type=image inputs
* Fixed usage of $ and jQuery variables, now fully compatible with all variations of noConflict usage
* Implemented #2908, enabling custom messages via metadata ala class="{required:true,messages:{required:'required field'}}", added demo/custom-messages-metadata-demo.html
* Removed deprecated methods minValue (min), maxValue (max), rangeValue (rangevalue), minLength (minlength), maxLength (maxlength), rangeLength (rangelength)
* Fixed #2215 regression: Call unhighlight only for current elements, not everything
* Implemented #2989, enabling image button to cancel validation
* Fixed issue where IE incorrectly validates against maxlength=0
* Added czech (cs) localization
* Reset validator.submitted on validator.resetForm(), enabling a full reset when necessary
* Fixed #3035, skipping all falsy attributes when reading rules (0, undefined, empty string), removed part of the maxlength workaround (for 0)
* Added dutch (nl) localization (#3201)

1.3
---
* Fixed invalid-form event, now only triggered when form is invalid
* Added spanish (es), russian (ru), portuguese brazilian (ptbr), turkish (tr), and polish (pl) localization
* Added removeAttrs plugin to facilitate adding and removing multiple attributes
* Added groups option to display a single message for multiple elements, via groups: { arbitraryGroupName: "fieldName1 fieldName2[, fieldNameN" }
* Enhanced rules() for adding and removing (static) rules: rules("add", "method1[, methodN]"/{method1:param[, method_n:param]}) and rules("remove"[, "method1[, method_n]")
* Enhanced rules-option, accepts space-seperated string-list of methods, eg. {birthdate: "required date"}
* Fixed checkbox group validation with inline rules: As long as the rules are specified on the first element, the group is now properly validated on click
* Fixed #2473, ignoring all rules with an explicit parameter of boolean-false, eg. required:false is the same as not specifying required at all (it was handled as required:true so far)
* Fixed #2424, with a modified patch from #2473: Methods returning a dependency-mismatch don't stop other rules from being evaluated anymore; still, success isn't applied for optional fields
* Fixed url and email validation to not use trimmed values
* Fixed creditcard validation to accept only digits and dashes ("asdf" is not a valid creditcard number)
* Allow both button and input elements for cancel buttons (via class="cancel")
* Fixed #2215: Fixed message display to call unhighlight as part of showing and hiding messages, no more visual side-effects while checking an element and extracted validator.checkForm to validate a form without UI sideeffects
* Rewrote custom selectors (:blank, :filled, :unchecked) with functions for compatibility with AIR

1.2.1
-----

* Bundled delegate plugin with validate plugin - its always required anyway
* Improved remote validation to include parts from the ajaxQueue plugin for proper synchronization (no additional plugin necessary)
* Fixed stopRequest to prevent pendingRequest < 0
* Added jQuery.validator.autoCreateRanges property, defaults to false, enable to convert min/max to range and minlength/maxlength to rangelength; this basically fixes the issue introduced by automatically creating ranges in 1.2
* Fixed optional-methods to not highlight anything at all if the field is blank, that is, don't trigger success
* Allow false/null for highlight/unhighlight options instead of forcing a do-nothing-callback even when nothing needs to be highlighted
* Fixed validate() call with no elements selected, returning undefined instead of throwing an error
* Improved demo, replacing metadata with classes/attributes for specifying rules
* Fixed error when no custom message is used for remote validation
* Modified email and url validation to require domain label and top label
* Fixed url and email validation to require TLD (actually to require domain label); 1.2 version (TLD is optional) is moved to additions as url2 and email2
* Fixed dynamic-totals demo in IE6/7 and improved templating, using textarea to store multiline template and string interpolation
* Added login form example with "Email password" link that makes the password field optional
* Enhanced dynamic-totals demo with an example of a single message for two fields

1.2
---

* Added AJAX-captcha validation example (based on http://psyrens.com/captcha/)
* Added remember-the-milk-demo (thanks RTM team for the permission!)
* Added marketo-demo (thanks Glen Lipka!)
* Added support for ajax-validation, see method "remote"; serverside returns JSON, true for valid elements, false or a String for invalid, String is used as message
* Added highlight and unhighlight options, by default toggles errorClass on element, allows custom highlighting
* Added valid() plugin method for easy programmatic checking of forms and fields without the need to use the validator API
* Added rules() plugin method to read and write rules for an element (currently read only)
* Replaced regex for email method, thanks to the contribution by Scott Gonzalez, see http://projects.scottsplayground.com/email_address_validation/
* Restructured event architecture to rely solely on delegation, both improving performance, and ease-of-use for the developer (requires jquery.delegate.js)
* Moved documentation from inline to http://docs.jquery.com/Plugins/Validation - including interactive examples for all methods
* Removed validator.refresh(), validation is now completely dynamic
* Renamed minValue to min, maxValue to max and rangeValue to range, deprecating the previous names (to be removed in 1.3)
* Renamed minLength to minlength, maxLength to maxlength and rangeLength to rangelength, deprecating the previous names (to be removed in 1.3)
* Added feature to merge min + max into and range and minlength + maxlength into rangelength
* Added support for dynamic rule parameters, allowing to specify a function as a parameter eg. for minlength, called when validating the element
* Allow to specify null or an empty string as a message to display nothing (see marketo demo)
* Rules overhaul: Now supports combination of rules-option, metadata, classes (new) and attributes (new), see rules() for details

1.1.2
---

* Replaced regex for URL method, thanks to the contribution by Scott Gonzalez, see http://projects.scottsplayground.com/iri/
* Improved email method to better handle unicode characters
* Fixed error container to hide when all elements are valid, not only on form submit
* Fixed String.format to jQuery.format (moving into jQuery namespace)
* Fixed accept method to accept both upper and lowercase extensions
* Fixed validate() plugin method to create only one validator instance for a given form and always return that one instance (avoids binding events multiple times)
* Changed debug-mode console log from "error" to "warn" level

1.1.1
-----

* Fixed invalid XHTML, preventing error label creation in IE since jQuery 1.1.4
* Fixed and improved String.format: Global search & replace, better handling of array arguments
* Fixed cancel-button handling to use validator-object for storing state instead of form element
* Fixed name selectors to handle "complex" names, eg. containing brackets ("list[]")
* Added button and disabled elements to exclude from validation
* Moved element event handlers to refresh to be able to add handlers to new elements
* Fixed email validation to allow long top level domains (eg. ".travel")
* Moved showErrors() from valid() to form()
* Added validator.size(): returns the number of current errors
* Call submitHandler with validator as scope for easier access of it's methods, eg. to find error labels using errorsFor(Element)
* Compatible with jQuery 1.1.x and 1.2.x

1.1
---

* Added validation on blur, keyup and click (for checkboxes and radiobutton). Replaces event-option.
* Fixed resetForm
* Fixed custom-methods-demo

1.0
---

* Improved number and numberDE methods to check for correct decimal numbers with delimiters
* Only elements that have rules are checked (otherwise success-option is applied to all elements)
* Added creditcard number method (thanks to Brian Klug)
* Added ignore-option, eg. ignore: "[@type=hidden]", using that expression to exclude elements to validate. Default: none, though submit and reset buttons are always ignored
* Heavily enhanced Functions-as-messages by providing a flexible String.format helper
* Accept Functions as messages, providing runtime-custom-messages
* Fixed exclusion of elements without rules from successList
* Fixed custom-method-demo, replaced the alert with message displaying the number of errors
* Fixed form-submit-prevention when using submitHandler
* Completely removed dependency on element IDs, though they are still used (when present) to link error labels to inputs. Achieved by using
  an array with {name, message, element} instead of an object with id:message pairs for the internal errorList.
* Added support for specifying simple rules as simple strings, eg. "required" is equivalent to {required: true}
* Added feature: Add errorClass to invalid field�s parent element, making it easy to style the label/field container or the label for the field.
* Added feature: focusCleanup - If enabled, removes the errorClass from the invalid elements and hides all errors messages whenever the element is focused.
* Added success option to show the a field was validated successfully
* Fixed Opera select-issue (avoiding a attribute-collision)
* Fixed problems with focussing hidden elements in IE
* Added feature to skip validation for submit buttons with class "cancel"
* Fixed potential issues with Google Toolbar by preferring plugin option messages over title attribute
* submitHandler is only called when an actual submit event was handled, validator.form() returns false only for invalid forms
* Invalid elements are now focused only on submit or via validator.focusInvalid(), avoiding all trouble with focus-on-blur
* IE6 error container layout issue is solved
* Customize error element via errorElement option
* Added validator.refresh() to find new inputs in the form
* Added accept validation method, checks file extensions
* Improved dependency feature by adding two custom expressions: ":blank" to select elements with an empty value and �:filled� to select elements with a value, both excluding whitespace
* Added a resetForm() method to the validator: Resets each form element (using the form plugin, if available), removes classes on invalid elements and hides all error messages
* Fixed docs for validator.showErrors()
* Fixed error label creation to always use html() instead of text(), allowing arbitrary HTML passed in as messages
* Fixed error label creation to use specified error class
* Added dependency feature: The requires method accepts both String (jQuery expressions) and Functions as the argument
* Heavily improved customizing of error message display: Use normal messages and show/hide an additional container; Completely replace message display with own mechanism (while being able to delegate to the default handler; Customize placing of generated labels (instead of default below-element)
* Fixed two major bugs in IE (error containers) and Opera (metadata)
* Modified validation methods to accept empty fields as valid (exception: of course �required� and also �equalTo� methods)
* Renamed "min" to "minLength", "max" to "maxLength", "length" to "rangeLength"
* Added "minValue", "maxValue" and "rangeValue"
* Streamlined API for support of different events. The default, submit, can be disabled. If any event is specified, that is applied to each element (instead of the entire form). Combining keyup-validation with submit-validation is now extremely easy to setup
* Added support for one-message-per-rule when defining messages via plugin settings
* Added support to wrap metadata in some parent element. Useful when metadata is used for other plugins, too.
* Refactored tests and demos: Less files, better demos
* Improved documentation: More examples for methods, more reference texts explaining some basics
