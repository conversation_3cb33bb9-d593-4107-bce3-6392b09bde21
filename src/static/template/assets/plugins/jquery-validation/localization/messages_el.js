/*
 * Translated default messages for the jQuery validation plugin.
 * Locale: EL (Greek; ελληνικά)
 */
(function ($) {
	$.extend($.validator.messages, {
		required: "Αυτό το πεδίο είναι υποχρεωτικό.",
		remote: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> διορθώστε αυτό το πεδίο.",
		email: "Παρα<PERSON><PERSON>λώ εισάγετε μια έγκυρη διεύθυνση email.",
		url: "Παρακαλώ εισάγετε ένα έγκυρο URL.",
		date: "Παρακ<PERSON>λώ εισάγετε μια έγκυρη ημερομηνία.",
		dateISO: "Πα<PERSON>ακ<PERSON>λώ εισάγετε μια έγκυρη ημερομηνία (ISO).",
		number: "Παρακαλώ εισάγετε έναν έγκυρο αριθμό.",
		digits: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> εισάγετε μόνο αριθμητικά ψηφία.",
		creditcard: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> εισάγετε έναν έγκυρο αριθμό πιστωτικής κάρτας.",
		equalTo: "Παρακαλώ εισάγετε την ίδια τιμή ξανά.",
		accept: "Παρακαλώ εισάγετε μια τιμή με έγκυρη επέκταση αρχείου.",
		maxlength: $.validator.format("Παρακαλώ εισάγετε μέχρι και {0} χαρακτήρες."),
		minlength: $.validator.format("Παρακαλώ εισάγετε τουλάχιστον {0} χαρακτήρες."),
		rangelength: $.validator.format("Παρακαλώ εισάγετε μια τιμή με μήκος μεταξύ {0} και {1} χαρακτήρων."),
		range: $.validator.format("Παρακαλώ εισάγετε μια τιμή μεταξύ {0} και {1}."),
		max: $.validator.format("Παρακαλώ εισάγετε μια τιμή μικρότερη ή ίση του {0}."),
		min: $.validator.format("Παρακαλώ εισάγετε μια τιμή μεγαλύτερη ή ίση του {0}.")
	});
}(jQuery));