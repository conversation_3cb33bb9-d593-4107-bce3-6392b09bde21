/*
 * Translated default messages for the jQuery validation plugin.
 * Locale: LV (Latvian; latviešu valoda)
 */
(function ($) {
	$.extend($.validator.messages, {
		required: "<PERSON>is lauks ir obligāts.",
		remote: "<PERSON><PERSON><PERSON><PERSON>, pārbaudiet šo lauku.",
		email: "<PERSON><PERSON><PERSON><PERSON>, ievadiet derīgu e-pasta adresi.",
		url: "<PERSON><PERSON><PERSON><PERSON>, ievadiet derīgu URL adresi.",
		date: "<PERSON><PERSON><PERSON><PERSON>, ievadiet derīgu datumu.",
		dateISO: "<PERSON><PERSON><PERSON><PERSON>, ievadiet derīgu datumu (ISO).",
		number: "<PERSON><PERSON><PERSON><PERSON>, ievadiet derīgu numuru.",
		digits: "<PERSON><PERSON><PERSON><PERSON>, ievadiet tikai ciparus.",
		creditcard: "<PERSON>ū<PERSON><PERSON>, ievadiet derīgu kredītkartes numuru.",
		equalTo: "<PERSON><PERSON><PERSON><PERSON>, ievadiet to pašu vēlreiz.",
		accept: "<PERSON><PERSON><PERSON><PERSON>, ievadiet vērtību ar derīgu paplašinājumu.",
		maxlength: $.validator.format("<PERSON><PERSON><PERSON><PERSON>, ievadiet ne vairāk kā {0} rakstzīmes."),
		minlength: $.validator.format("<PERSON>ū<PERSON><PERSON>, ievadiet vismaz {0} rakstzīmes."),
		rangelength: $.validator.format("Lūdzu ievadiet {0} līdz {1} rakstzīmes."),
		range: $.validator.format("Lūdzu, ievadiet skaitli no {0} līdz {1}."),
		max: $.validator.format("Lūdzu, ievadiet skaitli, kurš ir mazāks vai vienāds ar {0}."),
		min: $.validator.format("Lūdzu, ievadiet skaitli, kurš ir lielāks vai vienāds ar {0}.")
	});
}(jQuery));