/*
 * Translated default messages for the jQuery validation plugin.
 * Locale: VI (Vietnamese; Tiếng V<PERSON>)
 */
(function ($) {
	$.extend($.validator.messages, {
		required: "<PERSON><PERSON><PERSON> nhập.",
		remote: "<PERSON><PERSON><PERSON> sửa cho đúng.",
		email: "<PERSON><PERSON><PERSON> nhập email.",
		url: "<PERSON><PERSON><PERSON> nhập URL.",
		date: "<PERSON><PERSON><PERSON> nhập ngày.",
		dateISO: "<PERSON><PERSON><PERSON> nhập ngày (ISO).",
		number: "<PERSON><PERSON><PERSON> nhập số.",
		digits: "H<PERSON>y nhập chữ số.",
		creditcard: "<PERSON><PERSON><PERSON> nhập số thẻ tín dụng.",
		equalTo: "<PERSON><PERSON><PERSON> nhập thêm lần nữa.",
		accept: "Phần mở rộng không đúng.",
		maxlength: $.format("<PERSON><PERSON><PERSON> nhập từ {0} kí tự trở xuống."),
		minlength: $.format("<PERSON><PERSON><PERSON> nhập từ {0} kí tự trở lên."),
		rangelength: $.format("<PERSON><PERSON><PERSON> nhập từ {0} đến {1} kí tự."),
		range: $.format("<PERSON><PERSON><PERSON> nhập từ {0} đến {1}."),
		max: $.format("Hãy nhập từ {0} trở xuống."),
		min: $.format("Hãy nhập từ {1} trở lên.")
	});
}(jQuery));