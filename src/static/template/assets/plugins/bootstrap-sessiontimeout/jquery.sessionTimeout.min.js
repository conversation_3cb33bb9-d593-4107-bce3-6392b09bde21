!function(e){jQuery.sessionTimeout=function(t){function i(t){switch(t){case"start":s=setTimeout(function(){e("#sessionTimeout-dialog").modal("show"),n("start")},r.warnAfter);break;case"stop":clearTimeout(s)}}function n(e){switch(e){case"start":o=setTimeout(function(){window.location=r.redirUrl},r.redirAfter-r.warnAfter);break;case"stop":clearTimeout(o)}}var s,o,a={title:"Session Notification",message:"Your session is about to expire.",keepAliveUrl:"/keep-alive",redirUrl:"/timed-out",logoutUrl:"/log-out",warnAfter:9e5,redirAfter:12e5},r=a;t&&(r=e.extend(a,t)),e("body").append('<div class="modal fade" id="sessionTimeout-dialog"><div class="modal-dialog modal-small"><div class="modal-content"><div class="modal-header"><button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button><h4 class="modal-title">'+r.title+'</h4></div><div class="modal-body">'+r.message+'</div><div class="modal-footer"><button id="sessionTimeout-dialog-logout" type="button" class="btn btn-default">Logout</button><button id="sessionTimeout-dialog-keepalive" type="button" class="btn btn-primary" data-dismiss="modal">Stay Connected</button></div></div></div></div>'),e("#sessionTimeout-dialog-logout").on("click",function(){window.location=r.logoutUrl}),e("#sessionTimeout-dialog").on("hide.bs.modal",function(){e.ajax({type:"POST",url:r.keepAliveUrl}),n("stop"),i("start")}),i("start")}}(jQuery);