[{"tags": [], "description": {"full": "<p>lobals jQuery, define, exports, require, window, document</p>", "summary": "<p>lobals jQuery, define, exports, require, window, document</p>", "body": ""}, "ignore": false, "code": "(function (factory) {\n\t\"use strict\";\n\tif (typeof define === 'function' && define.amd) {\n\t\tdefine('jstree', ['jquery'], factory);\n\t}\n\telse if(typeof exports === 'object') {\n\t\tfactory(require('jquery'));\n\t}\n\telse {\n\t\tfactory(jQuery);\n\t}\n}(function ($, undefined) {\n\t\"use strict\";"}, {"tags": [], "description": {"full": "<p>jsTree 3.0.0<br /><a href='http://jstree.com/'>http://jstree.com/</a></p>\n\n<p>Copyright (c) 2013 <PERSON> (<a href='http://vakata.com'>http://vakata.com</a>)</p>\n\n<p>Licensed same as jquery - under the terms of the MIT License<br />  <a href='http://www.opensource.org/licenses/mit-license.php'>http://www.opensource.org/licenses/mit-license.php</a></p>", "summary": "<p>jsTree 3.0.0<br /><a href='http://jstree.com/'>http://jstree.com/</a></p>", "body": "<p>Copyright (c) 2013 <PERSON> (<a href='http://vakata.com'>http://vakata.com</a>)</p>\n\n<p>Licensed same as jquery - under the terms of the MIT License<br />  <a href='http://www.opensource.org/licenses/mit-license.php'>http://www.opensource.org/licenses/mit-license.php</a></p>"}, "ignore": true}, {"tags": [], "description": {"full": "<p>if using jslint please allow for the jQuery global and use following options: <br />jslint: browser: true, ass: true, bitwise: true, continue: true, nomen: true, plusplus: true, regexp: true, unparam: true, todo: true, white: true</p>", "summary": "<p>if using jslint please allow for the jQuery global and use following options: <br />jslint: browser: true, ass: true, bitwise: true, continue: true, nomen: true, plusplus: true, regexp: true, unparam: true, todo: true, white: true</p>", "body": ""}, "ignore": true, "code": "// prevent another load? maybe there is a better way?\n\tif($.jstree) {\n\t\treturn;\n\t}"}, {"tags": [], "description": {"full": "<h3>jsTree core functionality</h3>", "summary": "<h3>jsTree core functionality</h3>", "body": ""}, "ignore": false, "code": "// internal variables\n\tvar instance_counter = 0,\n\t\ttotal_nodes = 0,\n\t\tccp_node = false,\n\t\tccp_mode = false,\n\t\tccp_inst = false,\n\t\tthemes_loaded = [],\n\t\tsrc = $('script:last').attr('src'),\n\t\t_d = document, _node = _d.createElement('LI'), _temp1, _temp2;\n\n\t_node.setAttribute('role', 'treeitem');\n\t_temp1 = _d.createElement('I');\n\t_temp1.className = 'jstree-icon jstree-ocl';\n\t_node.appendChild(_temp1);\n\t_temp1 = _d.createElement('A');\n\t_temp1.className = 'jstree-anchor';\n\t_temp1.setAttribute('href','#');\n\t_temp2 = _d.createElement('I');\n\t_temp2.className = 'jstree-icon jstree-themeicon';\n\t_temp1.appendChild(_temp2);\n\t_node.appendChild(_temp1);\n\t_temp1 = _temp2 = null;"}, {"tags": [{"type": "name", "string": "$.jstree"}], "description": {"full": "<p>holds all jstree related functions and variables, including the actual class and methods to create, access and manipulate instances.</p>", "summary": "<p>holds all jstree related functions and variables, including the actual class and methods to create, access and manipulate instances.</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "$.jstree = {", "ctx": {"type": "property", "receiver": "$", "name": "jstree", "value": "{", "string": "$.jstree"}}, {"tags": [{"type": "name", "string": "$.jstree.version"}], "description": {"full": "<p>specifies the jstree version in use</p>", "summary": "<p>specifies the jstree version in use</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "version : '3.0.0-beta4',"}, {"tags": [{"type": "name", "string": "$.jstree.defaults"}], "description": {"full": "<p>holds all the default options used when creating new instances</p>", "summary": "<p>holds all the default options used when creating new instances</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "defaults : {"}, {"tags": [{"type": "name", "string": "$.jstree.defaults.plugins"}], "description": {"full": "<p>configure which plugins will be active on an instance. Should be an array of strings, where each element is a plugin name. The default is <code>[]</code></p>", "summary": "<p>configure which plugins will be active on an instance. Should be an array of strings, where each element is a plugin name. The default is <code>[]</code></p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "plugins : []\n\t\t},"}, {"tags": [{"type": "name", "string": "$.jstree.plugins"}], "description": {"full": "<p>stores all loaded jstree plugins (used internally)</p>", "summary": "<p>stores all loaded jstree plugins (used internally)</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "plugins : {},\n\t\tpath : src && src.indexOf('/') !== -1 ? src.replace(/\\/[^\\/]+$/,'') : ''\n\t};"}, {"tags": [{"type": "name", "string": "$.jstree.create(el [, options])"}, {"type": "param", "types": ["DOMElement", "j<PERSON><PERSON><PERSON>", "String"], "name": "el", "description": "the element to create the instance on, can be jQ<PERSON>y extended or a selector"}, {"type": "param", "types": ["Object"], "name": "options", "description": "options for this instance (extends `$.jstree.defaults`)"}, {"type": "return", "types": ["jsTree"], "description": "the new instance"}], "description": {"full": "<p>creates a jstree instance</p>", "summary": "<p>creates a jstree instance</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "$.jstree.create = function (el, options) {\n\t\tvar tmp = new $.jstree.core(++instance_counter),\n\t\t\topt = options;\n\t\toptions = $.extend(true, {}, $.jstree.defaults, options);\n\t\tif(opt && opt.plugins) {\n\t\t\toptions.plugins = opt.plugins;\n\t\t}\n\t\t$.each(options.plugins, function (i, k) {\n\t\t\tif(i !== 'core') {\n\t\t\t\ttmp = tmp.plugin(k, options[k]);\n\t\t\t}\n\t\t});\n\t\ttmp.init(el, options);\n\t\treturn tmp;\n\t};", "ctx": {"type": "method", "receiver": "$.jstree", "name": "create", "string": "$.jstree.create()"}}, {"tags": [{"type": "private", "string": ""}, {"type": "name", "string": "$.jstree.core(id)"}, {"type": "param", "types": ["Number"], "name": "id", "description": "this instance's index"}], "description": {"full": "<p>the jstree class constructor, used only internally</p>", "summary": "<p>the jstree class constructor, used only internally</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "$.jstree.core = function (id) {\n\t\tthis._id = id;\n\t\tthis._data = {\n\t\t\tcore : {\n\t\t\t\tthemes : {\n\t\t\t\t\tname : false,\n\t\t\t\t\tdots : false,\n\t\t\t\t\ticons : false\n\t\t\t\t},\n\t\t\t\tselected : []\n\t\t\t}\n\t\t};\n\t};", "ctx": {"type": "method", "receiver": "$.jstree", "name": "core", "string": "$.jstree.core()"}}, {"tags": [{"type": "name", "string": "$.jstree.reference(needle)"}, {"type": "param", "types": ["DOMElement", "j<PERSON><PERSON><PERSON>", "String"], "name": "needle", "description": ""}, {"type": "return", "types": ["jsTree", "null"], "description": "the instance or `null` if not found"}], "description": {"full": "<p>get a reference to an existing instance</p>\n\n<p><strong>Examples</strong></p>\n\n<pre><code>// provided a container with an ID of \"tree\", and a nested node with an ID of \"branch\"\n// all of there will return the same instance\n$.jstree.reference('tree');\n$.jstree.reference('#tree');\n$.jstree.reference($('#tree'));\n$.jstree.reference(document.getElementByID('tree'));\n$.jstree.reference('branch');\n$.jstree.reference('#branch');\n$.jstree.reference($('#branch'));\n$.jstree.reference(document.getElementByID('branch'));\n</code></pre>", "summary": "<p>get a reference to an existing instance</p>", "body": "<p><strong>Examples</strong></p>\n\n<pre><code>// provided a container with an ID of \"tree\", and a nested node with an ID of \"branch\"\n// all of there will return the same instance\n$.jstree.reference('tree');\n$.jstree.reference('#tree');\n$.jstree.reference($('#tree'));\n$.jstree.reference(document.getElementByID('tree'));\n$.jstree.reference('branch');\n$.jstree.reference('#branch');\n$.jstree.reference($('#branch'));\n$.jstree.reference(document.getElementByID('branch'));\n</code></pre>"}, "isPrivate": false, "ignore": false, "code": "$.jstree.reference = function (needle) {\n\t\tif(needle && !$(needle).length) {\n\t\t\tif(needle.id) {\n\t\t\t\tneedle = needle.id;\n\t\t\t}\n\t\t\tvar tmp = null;\n\t\t\t$('.jstree').each(function () {\n\t\t\t\tvar inst = $(this).data('jstree');\n\t\t\t\tif(inst && inst._model.data[needle]) {\n\t\t\t\t\ttmp = inst;\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t});\n\t\t\treturn tmp;\n\t\t}\n\t\treturn $(needle).closest('.jstree').data('jstree');\n\t};", "ctx": {"type": "method", "receiver": "$.jstree", "name": "reference", "string": "$.jstree.reference()"}}, {"tags": [{"type": "name", "string": "$().jstree([arg])"}, {"type": "param", "types": ["String", "Object"], "name": "arg", "description": ""}, {"type": "return", "types": ["Mixed"], "description": ""}], "description": {"full": "<p>Create an instance, get an instance or invoke a command on a instance. </p>\n\n<p>If there is no instance associated with the current node a new one is created and <code>arg</code> is used to extend <code>$.jstree.defaults</code> for this new instance. There would be no return value (chaining is not broken).</p>\n\n<p>If there is an existing instance and <code>arg</code> is a string the command specified by <code>arg</code> is executed on the instance, with any additional arguments passed to the function. If the function returns a value it will be returned (chaining could break depending on function).</p>\n\n<p>If there is an existing instance and <code>arg</code> is not a string the instance itself is returned (similar to <code>$.jstree.reference</code>).</p>\n\n<p>In any other case - nothing is returned and chaining is not broken.</p>\n\n<p><strong>Examples</strong></p>\n\n<pre><code>$('#tree1').jstree(); // creates an instance\n$('#tree2').jstree({ plugins : [] }); // create an instance with some options\n$('#tree1').jstree('open_node', '#branch_1'); // call a method on an existing instance, passing additional arguments\n$('#tree2').jstree(); // get an existing instance (or create an instance)\n$('#tree2').jstree(true); // get an existing instance (will not create new instance)\n$('#branch_1').jstree().select_node('#branch_1'); // get an instance (using a nested element and call a method)\n</code></pre>", "summary": "<p>Create an instance, get an instance or invoke a command on a instance. </p>", "body": "<p>If there is no instance associated with the current node a new one is created and <code>arg</code> is used to extend <code>$.jstree.defaults</code> for this new instance. There would be no return value (chaining is not broken).</p>\n\n<p>If there is an existing instance and <code>arg</code> is a string the command specified by <code>arg</code> is executed on the instance, with any additional arguments passed to the function. If the function returns a value it will be returned (chaining could break depending on function).</p>\n\n<p>If there is an existing instance and <code>arg</code> is not a string the instance itself is returned (similar to <code>$.jstree.reference</code>).</p>\n\n<p>In any other case - nothing is returned and chaining is not broken.</p>\n\n<p><strong>Examples</strong></p>\n\n<pre><code>$('#tree1').jstree(); // creates an instance\n$('#tree2').jstree({ plugins : [] }); // create an instance with some options\n$('#tree1').jstree('open_node', '#branch_1'); // call a method on an existing instance, passing additional arguments\n$('#tree2').jstree(); // get an existing instance (or create an instance)\n$('#tree2').jstree(true); // get an existing instance (will not create new instance)\n$('#branch_1').jstree().select_node('#branch_1'); // get an instance (using a nested element and call a method)\n</code></pre>"}, "isPrivate": false, "ignore": false, "code": "$.fn.jstree = function (arg) {\n\t\t// check for string argument\n\t\tvar is_method\t= (typeof arg === 'string'),\n\t\t\targs\t\t= Array.prototype.slice.call(arguments, 1),\n\t\t\tresult\t\t= null;\n\t\tthis.each(function () {\n\t\t\t// get the instance (if there is one) and method (if it exists)\n\t\t\tvar instance = $.jstree.reference(this),\n\t\t\t\tmethod = is_method && instance ? instance[arg] : null;\n\t\t\t// if calling a method, and method is available - execute on the instance\n\t\t\tresult = is_method && method ?\n\t\t\t\tmethod.apply(instance, args) :\n\t\t\t\tnull;\n\t\t\t// if there is no instance and no method is being called - create one\n\t\t\tif(!instance && !is_method && (arg === undefined || $.isPlainObject(arg))) {\n\t\t\t\t$(this).data('jstree', new $.jstree.create(this, arg));\n\t\t\t}\n\t\t\t// if there is an instance and no method is called - return the instance\n\t\t\tif(instance && !is_method) {\n\t\t\t\tresult = instance;\n\t\t\t}\n\t\t\t// if there was a method call which returned a result - break and return the value\n\t\t\tif(result !== null && result !== undefined) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t});\n\t\t// if there was a method call with a valid return value - return that, otherwise continue the chain\n\t\treturn result !== null && result !== undefined ?\n\t\t\tresult : this;\n\t};", "ctx": {"type": "method", "receiver": "$.fn", "name": "jstree", "string": "$.fn.jstree()"}}, {"tags": [{"type": "name", "string": "$(':jstree')"}, {"type": "return", "types": ["j<PERSON><PERSON><PERSON>"], "description": ""}], "description": {"full": "<p>used to find elements containing an instance</p>\n\n<p><strong>Examples</strong></p>\n\n<pre><code>$('div:jstree').each(function () {\n    $(this).jstree('destroy');\n});\n</code></pre>", "summary": "<p>used to find elements containing an instance</p>", "body": "<p><strong>Examples</strong></p>\n\n<pre><code>$('div:jstree').each(function () {\n    $(this).jstree('destroy');\n});\n</code></pre>"}, "isPrivate": false, "ignore": false, "code": "$.expr[':'].jstree = $.expr.createPseudo(function(search) {\n\t\treturn function(a) {\n\t\t\treturn $(a).hasClass('jstree') &&\n\t\t\t\t$(a).data('jstree') !== undefined;\n\t\t};\n\t});"}, {"tags": [{"type": "name", "string": "$.jstree.defaults.core"}], "description": {"full": "<p>stores all defaults for the core</p>", "summary": "<p>stores all defaults for the core</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "$.jstree.defaults.core = {"}, {"tags": [{"type": "name", "string": "$.jstree.defaults.core.data"}], "description": {"full": "<p>data configuration</p>\n\n<p>If left as <code>false</code> the HTML inside the jstree container element is used to populate the tree (that should be an unordered list with list items).</p>\n\n<p>You can also pass in a HTML string or a JSON array here.</p>\n\n<p>It is possible to pass in a standard jQuery-like AJAX config and jstree will automatically determine if the response is JSON or HTML and use that to populate the tree. <br />In addition to the standard jQuery ajax options here you can suppy functions for <code>data</code> and <code>url</code>, the functions will be run in the current instance's scope and a param will be passed indicating which node is being loaded, the return value of those functions will be used.</p>\n\n<p>The last option is to specify a function, that function will receive the node being loaded as argument and a second param which is a function which should be called with the result.</p>\n\n<p><strong>Examples</strong></p>\n\n<pre><code>// AJAX\n$('#tree').jstree({\n    'core' : {\n        'data' : {\n            'url' : '/get/children/',\n            'data' : function (node) {\n                return { 'id' : node.id };\n            }\n        }\n    });\n\n// direct data\n$('#tree').jstree({\n    'core' : {\n        'data' : [\n            'Simple root node',\n            {\n                'id' : 'node_2',\n                'text' : 'Root node with options',\n                'state' : { 'opened' : true, 'selected' : true },\n                'children' : [ { 'text' : 'Child 1' }, 'Child 2']\n            }\n        ]\n    });\n\n// function\n$('#tree').jstree({\n    'core' : {\n        'data' : function (obj, callback) {\n            callback.call(this, ['Root 1', 'Root 2']);\n        }\n    });\n</code></pre>", "summary": "<p>data configuration</p>", "body": "<p>If left as <code>false</code> the HTML inside the jstree container element is used to populate the tree (that should be an unordered list with list items).</p>\n\n<p>You can also pass in a HTML string or a JSON array here.</p>\n\n<p>It is possible to pass in a standard jQuery-like AJAX config and jstree will automatically determine if the response is JSON or HTML and use that to populate the tree. <br />In addition to the standard jQuery ajax options here you can suppy functions for <code>data</code> and <code>url</code>, the functions will be run in the current instance's scope and a param will be passed indicating which node is being loaded, the return value of those functions will be used.</p>\n\n<p>The last option is to specify a function, that function will receive the node being loaded as argument and a second param which is a function which should be called with the result.</p>\n\n<p><strong>Examples</strong></p>\n\n<pre><code>// AJAX\n$('#tree').jstree({\n    'core' : {\n        'data' : {\n            'url' : '/get/children/',\n            'data' : function (node) {\n                return { 'id' : node.id };\n            }\n        }\n    });\n\n// direct data\n$('#tree').jstree({\n    'core' : {\n        'data' : [\n            'Simple root node',\n            {\n                'id' : 'node_2',\n                'text' : 'Root node with options',\n                'state' : { 'opened' : true, 'selected' : true },\n                'children' : [ { 'text' : 'Child 1' }, 'Child 2']\n            }\n        ]\n    });\n\n// function\n$('#tree').jstree({\n    'core' : {\n        'data' : function (obj, callback) {\n            callback.call(this, ['Root 1', 'Root 2']);\n        }\n    });\n</code></pre>"}, "isPrivate": false, "ignore": false, "code": "data\t\t\t: false,"}, {"tags": [{"type": "name", "string": "$.jstree.defaults.core.strings"}], "description": {"full": "<p>configure the various strings used throughout the tree</p>\n\n<p>You can use an object where the key is the string you need to replace and the value is your replacement.<br />Another option is to specify a function which will be called with an argument of the needed string and should return the replacement.<br />If left as <code>false</code> no replacement is made.</p>\n\n<p><strong>Examples</strong></p>\n\n<pre><code>$('#tree').jstree({\n    'core' : {\n        'strings' : {\n            'Loading...' : 'Please wait ...'\n        }\n    }\n});\n</code></pre>", "summary": "<p>configure the various strings used throughout the tree</p>", "body": "<p>You can use an object where the key is the string you need to replace and the value is your replacement.<br />Another option is to specify a function which will be called with an argument of the needed string and should return the replacement.<br />If left as <code>false</code> no replacement is made.</p>\n\n<p><strong>Examples</strong></p>\n\n<pre><code>$('#tree').jstree({\n    'core' : {\n        'strings' : {\n            'Loading...' : 'Please wait ...'\n        }\n    }\n});\n</code></pre>"}, "isPrivate": false, "ignore": false, "code": "strings\t\t\t: false,"}, {"tags": [{"type": "name", "string": "$.jstree.defaults.core.check_callback"}], "description": {"full": "<p>determines what happens when a user tries to modify the structure of the tree<br />If left as <code>false</code> all operations like create, rename, delete, move or copy are prevented.<br />You can set this to <code>true</code> to allow all interactions or use a function to have better control.</p>\n\n<p><strong>Examples</strong></p>\n\n<pre><code>$('#tree').jstree({\n    'core' : {\n        'check_callback' : function (operation, node, node_parent, node_position) {\n            // operation can be 'create_node', 'rename_node', 'delete_node', 'move_node' or 'copy_node'\n            // in case of 'rename_node' node_position is filled with the new node name\n            return operation === 'rename_node' ? true : false;\n        }\n    }\n});\n</code></pre>", "summary": "<p>determines what happens when a user tries to modify the structure of the tree<br />If left as <code>false</code> all operations like create, rename, delete, move or copy are prevented.<br />You can set this to <code>true</code> to allow all interactions or use a function to have better control.</p>", "body": "<p><strong>Examples</strong></p>\n\n<pre><code>$('#tree').jstree({\n    'core' : {\n        'check_callback' : function (operation, node, node_parent, node_position) {\n            // operation can be 'create_node', 'rename_node', 'delete_node', 'move_node' or 'copy_node'\n            // in case of 'rename_node' node_position is filled with the new node name\n            return operation === 'rename_node' ? true : false;\n        }\n    }\n});\n</code></pre>"}, "isPrivate": false, "ignore": false, "code": "check_callback\t: false,"}, {"tags": [{"type": "name", "string": "$.jstree.defaults.core.animation"}], "description": {"full": "<p>the open / close animation duration in milliseconds - set this to <code>false</code> to disable the animation (default is <code>200</code>)</p>", "summary": "<p>the open / close animation duration in milliseconds - set this to <code>false</code> to disable the animation (default is <code>200</code>)</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "animation\t\t: 200,"}, {"tags": [{"type": "name", "string": "$.jstree.defaults.core.multiple"}], "description": {"full": "<p>a boolean indicating if multiple nodes can be selected</p>", "summary": "<p>a boolean indicating if multiple nodes can be selected</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "multiple\t\t: true,"}, {"tags": [{"type": "name", "string": "$.jstree.defaults.core.themes"}], "description": {"full": "<p>theme configuration object</p>", "summary": "<p>theme configuration object</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "themes\t\t\t: {"}, {"tags": [{"type": "name", "string": "$.jstree.defaults.core.themes.name"}], "description": {"full": "<p>the name of the theme to use (if left as <code>false</code> the default theme is used)</p>", "summary": "<p>the name of the theme to use (if left as <code>false</code> the default theme is used)</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "name\t\t\t: false,"}, {"tags": [{"type": "name", "string": "$.jstree.defaults.core.themes.url"}], "description": {"full": "<p>the URL of the theme's CSS file, leave this as <code>false</code> if you have manually included the theme CSS (recommended). You can set this to <code>true</code> too which will try to autoload the theme.</p>", "summary": "<p>the URL of the theme's CSS file, leave this as <code>false</code> if you have manually included the theme CSS (recommended). You can set this to <code>true</code> too which will try to autoload the theme.</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "url\t\t\t\t: false,"}, {"tags": [{"type": "name", "string": "$.jstree.defaults.core.themes.dir"}], "description": {"full": "<p>the location of all jstree themes - only used if <code>url</code> is set to <code>true</code></p>", "summary": "<p>the location of all jstree themes - only used if <code>url</code> is set to <code>true</code></p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "dir\t\t\t\t: false,"}, {"tags": [{"type": "name", "string": "$.jstree.defaults.core.themes.dots"}], "description": {"full": "<p>a boolean indicating if connecting dots are shown</p>", "summary": "<p>a boolean indicating if connecting dots are shown</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "dots\t\t\t: true,"}, {"tags": [{"type": "name", "string": "$.jstree.defaults.core.themes.icons"}], "description": {"full": "<p>a boolean indicating if node icons are shown</p>", "summary": "<p>a boolean indicating if node icons are shown</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "icons\t\t\t: true,"}, {"tags": [{"type": "name", "string": "$.jstree.defaults.core.themes.stripes"}], "description": {"full": "<p>a boolean indicating if the tree background is striped</p>", "summary": "<p>a boolean indicating if the tree background is striped</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "stripes\t\t\t: false,"}, {"tags": [{"type": "name", "string": "$.jstree.defaults.core.themes.variant"}], "description": {"full": "<p>a string (or boolean <code>false</code>) specifying the theme variant to use (if the theme supports variants)</p>", "summary": "<p>a string (or boolean <code>false</code>) specifying the theme variant to use (if the theme supports variants)</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "variant\t\t\t: false,"}, {"tags": [{"type": "name", "string": "$.jstree.defaults.core.themes.responsive"}], "description": {"full": "<p>a boolean specifying if a reponsive version of the theme should kick in on smaller screens (if the theme supports it). Defaults to <code>true</code>.</p>", "summary": "<p>a boolean specifying if a reponsive version of the theme should kick in on smaller screens (if the theme supports it). Defaults to <code>true</code>.</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "responsive\t\t: true\n\t\t},"}, {"tags": [{"type": "name", "string": "$.jstree.defaults.core.expand_selected_onload"}], "description": {"full": "<p>if left as <code>true</code> all parents of all selected nodes will be opened once the tree loads (so that all selected nodes are visible to the user)</p>", "summary": "<p>if left as <code>true</code> all parents of all selected nodes will be opened once the tree loads (so that all selected nodes are visible to the user)</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "expand_selected_onload : true\n\t};\n\t$.jstree.core.prototype = {"}, {"tags": [{"type": "private", "string": ""}, {"type": "name", "string": "plugin(deco [, opts])"}, {"type": "param", "types": ["String"], "name": "deco", "description": "the plugin to decorate with"}, {"type": "param", "types": ["Object"], "name": "opts", "description": "options for the plugin"}, {"type": "return", "types": ["jsTree"], "description": ""}], "description": {"full": "<p>used to decorate an instance with a plugin. Used internally.</p>", "summary": "<p>used to decorate an instance with a plugin. Used internally.</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "plugin : function (deco, opts) {\n\t\t\tvar Child = $.jstree.plugins[deco];\n\t\t\tif(Child) {\n\t\t\t\tthis._data[deco] = {};\n\t\t\t\tChild.prototype = this;\n\t\t\t\treturn new Child(opts, this);\n\t\t\t}\n\t\t\treturn this;\n\t\t},"}, {"tags": [{"type": "private", "string": ""}, {"type": "name", "string": "init(el, optons)"}, {"type": "param", "types": ["DOMElement", "j<PERSON><PERSON><PERSON>", "String"], "name": "el", "description": "the element we are transforming"}, {"type": "param", "types": ["Object"], "name": "options", "description": "options for this instance"}, {"type": "trigger", "string": "init.jstree, loading.jstree, loaded.jstree, ready.jstree, changed.jstree"}], "description": {"full": "<p>used to decorate an instance with a plugin. Used internally.</p>", "summary": "<p>used to decorate an instance with a plugin. Used internally.</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "init : function (el, options) {\n\t\t\tthis._model = {\n\t\t\t\tdata : {\n\t\t\t\t\t'#' : {\n\t\t\t\t\t\tid : '#',\n\t\t\t\t\t\tparent : null,\n\t\t\t\t\t\tparents : [],\n\t\t\t\t\t\tchildren : [],\n\t\t\t\t\t\tchildren_d : [],\n\t\t\t\t\t\tstate : { loaded : false }\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tchanged : [],\n\t\t\t\tforce_full_redraw : false,\n\t\t\t\tredraw_timeout : false,\n\t\t\t\tdefault_state : {\n\t\t\t\t\tloaded : true,\n\t\t\t\t\topened : false,\n\t\t\t\t\tselected : false,\n\t\t\t\t\tdisabled : false\n\t\t\t\t}\n\t\t\t};\n\n\t\t\tthis.element = $(el).addClass('jstree jstree-' + this._id);\n\t\t\tthis.settings = options;\n\t\t\tthis.element.bind(\"destroyed\", $.proxy(this.teardown, this));\n\n\t\t\tthis._data.core.ready = false;\n\t\t\tthis._data.core.loaded = false;\n\t\t\tthis._data.core.rtl = (this.element.css(\"direction\") === \"rtl\");\n\t\t\tthis.element[this._data.core.rtl ? 'addClass' : 'removeClass'](\"jstree-rtl\");\n\t\t\tthis.element.attr('role','tree');\n\n\t\t\tthis.bind();"}, {"tags": [{"type": "event", "string": ""}, {"type": "name", "string": "init.jstree"}], "description": {"full": "<p>triggered after all events are bound</p>", "summary": "<p>triggered after all events are bound</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "this.trigger(\"init\");\n\n\t\t\tthis._data.core.original_container_html = this.element.find(\" > ul > li\").clone(true);\n\t\t\tthis._data.core.original_container_html\n\t\t\t\t.find(\"li\").addBack()\n\t\t\t\t.contents().filter(function() {\n\t\t\t\t\treturn this.nodeType === 3 && (!this.nodeValue || /^\\s+$/.test(this.nodeValue));\n\t\t\t\t})\n\t\t\t\t.remove();\n\t\t\tthis.element.html(\"<\"+\"ul class='jstree-container-ul'><\"+\"li class='jstree-initial-node jstree-loading jstree-leaf jstree-last'><i class='jstree-icon jstree-ocl'></i><\"+\"a class='jstree-anchor' href='#'><i class='jstree-icon jstree-themeicon-hidden'></i>\" + this.get_string(\"Loading ...\") + \"</a></li></ul>\");\n\t\t\tthis._data.core.li_height = this.get_container_ul().children(\"li:eq(0)\").height() || 18;"}, {"tags": [{"type": "event", "string": ""}, {"type": "name", "string": "loading.jstree"}], "description": {"full": "<p>triggered after the loading text is shown and before loading starts</p>", "summary": "<p>triggered after the loading text is shown and before loading starts</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "this.trigger(\"loading\");\n\t\t\tthis.load_node('#');\n\t\t},"}, {"tags": [{"type": "name", "string": "destroy()"}], "description": {"full": "<p>destroy an instance</p>", "summary": "<p>destroy an instance</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "destroy : function () {\n\t\t\tthis.element.unbind(\"destroyed\", this.teardown);\n\t\t\tthis.teardown();\n\t\t},"}, {"tags": [{"type": "private", "string": ""}, {"type": "name", "string": "teardown()"}], "description": {"full": "<p>part of the destroying of an instance. Used internally.</p>", "summary": "<p>part of the destroying of an instance. Used internally.</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "teardown : function () {\n\t\t\tthis.unbind();\n\t\t\tthis.element\n\t\t\t\t.removeClass('jstree')\n\t\t\t\t.removeData('jstree')\n\t\t\t\t.find(\"[class^='jstree']\")\n\t\t\t\t\t.addBack()\n\t\t\t\t\t.attr(\"class\", function () { return this.className.replace(/jstree[^ ]*|$/ig,''); });\n\t\t\tthis.element = null;\n\t\t},"}, {"tags": [{"type": "private", "string": ""}, {"type": "name", "string": "bind()"}], "description": {"full": "<p>bind all events. Used internally.</p>", "summary": "<p>bind all events. Used internally.</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "bind : function () {\n\t\t\tif($.support.touch) {\n\t\t\t\tthis.element.addTouch();\n\t\t\t}\n\t\t\tthis.element\n\t\t\t\t.on(\"dblclick.jstree\", function () {\n\t\t\t\t\t\tif(document.selection && document.selection.empty) {\n\t\t\t\t\t\t\tdocument.selection.empty();\n\t\t\t\t\t\t}\n\t\t\t\t\t\telse {\n\t\t\t\t\t\t\tif(window.getSelection) {\n\t\t\t\t\t\t\t\tvar sel = window.getSelection();\n\t\t\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\t\t\tsel.removeAllRanges();\n\t\t\t\t\t\t\t\t\tsel.collapse();\n\t\t\t\t\t\t\t\t} catch (ignore) { }\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t.on(\"click.jstree\", \".jstree-ocl\", $.proxy(function (e) {\n\t\t\t\t\t\tthis.toggle_node(e.target);\n\t\t\t\t\t}, this))\n\t\t\t\t.on(\"click.jstree\", \".jstree-anchor\", $.proxy(function (e) {\n\t\t\t\t\t\te.preventDefault();\n\t\t\t\t\t\t$(e.currentTarget).focus();\n\t\t\t\t\t\tthis.activate_node(e.currentTarget, e);\n\t\t\t\t\t}, this))\n\t\t\t\t.on('keydown.jstree', '.jstree-anchor', $.proxy(function (e) {\n\t\t\t\t\t\tvar o = null;\n\t\t\t\t\t\tswitch(e.which) {\n\t\t\t\t\t\t\tcase 13:\n\t\t\t\t\t\t\tcase 32:\n\t\t\t\t\t\t\t\te.type = \"click\";\n\t\t\t\t\t\t\t\t$(e.currentTarget).trigger(e);\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\tcase 37:\n\t\t\t\t\t\t\t\te.preventDefault();\n\t\t\t\t\t\t\t\tif(this.is_open(e.currentTarget)) {\n\t\t\t\t\t\t\t\t\tthis.close_node(e.currentTarget);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\telse {\n\t\t\t\t\t\t\t\t\to = this.get_prev_dom(e.currentTarget);\n\t\t\t\t\t\t\t\t\tif(o && o.length) { o.children('.jstree-anchor').focus(); }\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\tcase 38:\n\t\t\t\t\t\t\t\te.preventDefault();\n\t\t\t\t\t\t\t\to = this.get_prev_dom(e.currentTarget);\n\t\t\t\t\t\t\t\tif(o && o.length) { o.children('.jstree-anchor').focus(); }\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\tcase 39:\n\t\t\t\t\t\t\t\te.preventDefault();\n\t\t\t\t\t\t\t\tif(this.is_closed(e.currentTarget)) {\n\t\t\t\t\t\t\t\t\tthis.open_node(e.currentTarget, function (o) { this.get_node(o, true).children('.jstree-anchor').focus(); });\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\telse {\n\t\t\t\t\t\t\t\t\to = this.get_next_dom(e.currentTarget);\n\t\t\t\t\t\t\t\t\tif(o && o.length) { o.children('.jstree-anchor').focus(); }\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\tcase 40:\n\t\t\t\t\t\t\t\te.preventDefault();\n\t\t\t\t\t\t\t\to = this.get_next_dom(e.currentTarget);\n\t\t\t\t\t\t\t\tif(o && o.length) { o.children('.jstree-anchor').focus(); }\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t// delete\n\t\t\t\t\t\t\tcase 46:\n\t\t\t\t\t\t\t\te.preventDefault();\n\t\t\t\t\t\t\t\to = this.get_node(e.currentTarget);\n\t\t\t\t\t\t\t\tif(o && o.id && o.id !== '#') {\n\t\t\t\t\t\t\t\t\to = this.is_selected(o) ? this.get_selected() : o;\n\t\t\t\t\t\t\t\t\t// this.delete_node(o);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t// f2\n\t\t\t\t\t\t\tcase 113:\n\t\t\t\t\t\t\t\te.preventDefault();\n\t\t\t\t\t\t\t\to = this.get_node(e.currentTarget);"}, {"tags": [], "description": {"full": "<p>if(o &amp;&amp; o.id &amp;&amp; o.id !== '#') {<br />                                    // this.edit(o);<br />                                }</p>", "summary": "<p>if(o &amp;&amp; o.id &amp;&amp; o.id !== '#') {<br />                                    // this.edit(o);<br />                                }</p>", "body": ""}, "ignore": true, "code": "break;\n\t\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\t\t// console.log(e.which);\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t}\n\t\t\t\t\t}, this))\n\t\t\t\t.on(\"load_node.jstree\", $.proxy(function (e, data) {\n\t\t\t\t\t\tif(data.status) {\n\t\t\t\t\t\t\tif(data.node.id === '#' && !this._data.core.loaded) {\n\t\t\t\t\t\t\t\tthis._data.core.loaded = true;"}, {"tags": [{"type": "event", "string": ""}, {"type": "name", "string": "loaded.jstree"}], "description": {"full": "<p>triggered after the root node is loaded for the first time</p>", "summary": "<p>triggered after the root node is loaded for the first time</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "this.trigger(\"loaded\");\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif(!this._data.core.ready && !this.get_container_ul().find('.jstree-loading:eq(0)').length) {\n\t\t\t\t\t\t\t\tthis._data.core.ready = true;\n\t\t\t\t\t\t\t\tif(this._data.core.selected.length) {\n\t\t\t\t\t\t\t\t\tif(this.settings.core.expand_selected_onload) {\n\t\t\t\t\t\t\t\t\t\tvar tmp = [], i, j;\n\t\t\t\t\t\t\t\t\t\tfor(i = 0, j = this._data.core.selected.length; i < j; i++) {\n\t\t\t\t\t\t\t\t\t\t\ttmp = tmp.concat(this._model.data[this._data.core.selected[i]].parents);\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\ttmp = $.vakata.array_unique(tmp);\n\t\t\t\t\t\t\t\t\t\tfor(i = 0, j = tmp.length; i < j; i++) {\n\t\t\t\t\t\t\t\t\t\t\tthis.open_node(tmp[i], false, 0);\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\tthis.trigger('changed', { 'action' : 'ready', 'selected' : this._data.core.selected });\n\t\t\t\t\t\t\t\t}"}, {"tags": [{"type": "event", "string": ""}, {"type": "name", "string": "ready.jstree"}], "description": {"full": "<p>triggered after all nodes are finished loading</p>", "summary": "<p>triggered after all nodes are finished loading</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "this.trigger(\"ready\");\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}, this))\n\t\t\t\t// THEME RELATED\n\t\t\t\t.on(\"init.jstree\", $.proxy(function () {\n\t\t\t\t\t\tvar s = this.settings.core.themes;\n\t\t\t\t\t\tthis._data.core.themes.dots\t\t\t= s.dots;\n\t\t\t\t\t\tthis._data.core.themes.stripes\t\t= s.stripes;\n\t\t\t\t\t\tthis._data.core.themes.icons\t\t= s.icons;\n\t\t\t\t\t\tthis.set_theme(s.name || \"default\", s.url);\n\t\t\t\t\t\tthis.set_theme_variant(s.variant);\n\t\t\t\t\t}, this))\n\t\t\t\t.on(\"loading.jstree\", $.proxy(function () {\n\t\t\t\t\t\tthis[ this._data.core.themes.dots ? \"show_dots\" : \"hide_dots\" ]();\n\t\t\t\t\t\tthis[ this._data.core.themes.icons ? \"show_icons\" : \"hide_icons\" ]();\n\t\t\t\t\t\tthis[ this._data.core.themes.stripes ? \"show_stripes\" : \"hide_stripes\" ]();\n\t\t\t\t\t}, this))\n\t\t\t\t.on('focus.jstree', '.jstree-anchor', $.proxy(function (e) {\n\t\t\t\t\t\t$(e.currentTarget).mouseenter();\n\t\t\t\t\t}, this))\n\t\t\t\t.on('blur.jstree', '.jstree-anchor', $.proxy(function (e) {\n\t\t\t\t\t\t$(e.currentTarget).mouseleave();\n\t\t\t\t\t}, this))\n\t\t\t\t.on('mouseenter.jstree', '.jstree-anchor', $.proxy(function (e) {\n\t\t\t\t\t\tvar o = this.element.find('.jstree-anchor:focus').not('.jstree-clicked');\n\t\t\t\t\t\tif(o && o.length && o[0] !== e.currentTarget) {\n\t\t\t\t\t\t\to.blur();\n\t\t\t\t\t\t}\n\t\t\t\t\t\tthis.hover_node(e.currentTarget);\n\t\t\t\t\t}, this))\n\t\t\t\t.on('mouseleave.jstree', '.jstree-anchor', $.proxy(function (e) {\n\t\t\t\t\t\tthis.dehover_node(e.currentTarget);\n\t\t\t\t\t}, this));\n\t\t},"}, {"tags": [{"type": "private", "string": ""}, {"type": "name", "string": "unbind()"}], "description": {"full": "<p>part of the destroying of an instance. Used internally.</p>", "summary": "<p>part of the destroying of an instance. Used internally.</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "unbind : function () {\n\t\t\tthis.element.off('.jstree');\n\t\t\t$(document).off('.jstree-' + this._id);\n\t\t},"}, {"tags": [{"type": "private", "string": ""}, {"type": "name", "string": "trigger(ev [, data])"}, {"type": "param", "types": ["String"], "name": "ev", "description": "the name of the event to trigger"}, {"type": "param", "types": ["Object"], "name": "data", "description": "additional data to pass with the event"}], "description": {"full": "<p>trigger an event. Used internally.</p>", "summary": "<p>trigger an event. Used internally.</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "trigger : function (ev, data) {\n\t\t\tif(!data) {\n\t\t\t\tdata = {};\n\t\t\t}\n\t\t\tdata.instance = this;\n\t\t\tthis.element.triggerHandler(ev.replace('.jstree','') + '.jstree', data);\n\t\t},"}, {"tags": [{"type": "name", "string": "get_container()"}, {"type": "return", "types": ["j<PERSON><PERSON><PERSON>"], "description": ""}], "description": {"full": "<p>returns the jQuery extended instance container</p>", "summary": "<p>returns the jQuery extended instance container</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "get_container : function () {\n\t\t\treturn this.element;\n\t\t},"}, {"tags": [{"type": "private", "string": ""}, {"type": "name", "string": "get_container_ul()"}, {"type": "return", "types": ["j<PERSON><PERSON><PERSON>"], "description": ""}], "description": {"full": "<p>returns the jQuery extended main UL node inside the instance container. Used internally.</p>", "summary": "<p>returns the jQuery extended main UL node inside the instance container. Used internally.</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "get_container_ul : function () {\n\t\t\treturn this.element.children(\"ul:eq(0)\");\n\t\t},"}, {"tags": [{"type": "private", "string": ""}, {"type": "name", "string": "get_string(key)"}, {"type": "param", "types": ["String"], "name": "key", "description": ""}, {"type": "return", "types": ["String"], "description": ""}], "description": {"full": "<p>gets string replacements (localization). Used internally.</p>", "summary": "<p>gets string replacements (localization). Used internally.</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "get_string : function (key) {\n\t\t\tvar a = this.settings.core.strings;\n\t\t\tif($.isFunction(a)) { return a.call(this, key); }\n\t\t\tif(a && a[key]) { return a[key]; }\n\t\t\treturn key;\n\t\t},"}, {"tags": [{"type": "private", "string": ""}, {"type": "name", "string": "_firstChild(dom)"}, {"type": "param", "types": ["DOMElement"], "name": "dom", "description": ""}, {"type": "return", "types": ["DOMElement"], "description": ""}], "description": {"full": "<p>gets the first child of a DOM node. Used internally.</p>", "summary": "<p>gets the first child of a DOM node. Used internally.</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "_firstChild : function (dom) {\n\t\t\tdom = dom ? dom.firstChild : null;\n\t\t\twhile(dom !== null && dom.nodeType !== 1) {\n\t\t\t\tdom = dom.nextSibling;\n\t\t\t}\n\t\t\treturn dom;\n\t\t},"}, {"tags": [{"type": "private", "string": ""}, {"type": "name", "string": "_nextSibling(dom)"}, {"type": "param", "types": ["DOMElement"], "name": "dom", "description": ""}, {"type": "return", "types": ["DOMElement"], "description": ""}], "description": {"full": "<p>gets the next sibling of a DOM node. Used internally.</p>", "summary": "<p>gets the next sibling of a DOM node. Used internally.</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "_nextSibling : function (dom) {\n\t\t\tdom = dom ? dom.nextSibling : null;\n\t\t\twhile(dom !== null && dom.nodeType !== 1) {\n\t\t\t\tdom = dom.nextSibling;\n\t\t\t}\n\t\t\treturn dom;\n\t\t},"}, {"tags": [{"type": "private", "string": ""}, {"type": "name", "string": "_previousSibling(dom)"}, {"type": "param", "types": ["DOMElement"], "name": "dom", "description": ""}, {"type": "return", "types": ["DOMElement"], "description": ""}], "description": {"full": "<p>gets the previous sibling of a DOM node. Used internally.</p>", "summary": "<p>gets the previous sibling of a DOM node. Used internally.</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "_previousSibling : function (dom) {\n\t\t\tdom = dom ? dom.previousSibling : null;\n\t\t\twhile(dom !== null && dom.nodeType !== 1) {\n\t\t\t\tdom = dom.previousSibling;\n\t\t\t}\n\t\t\treturn dom;\n\t\t},"}, {"tags": [{"type": "name", "string": "get_node(obj [, as_dom])"}, {"type": "param", "types": ["mixed"], "name": "obj", "description": ""}, {"type": "param", "types": ["Boolean"], "name": "as_dom", "description": ""}, {"type": "return", "types": ["Object", "j<PERSON><PERSON><PERSON>"], "description": ""}], "description": {"full": "<p>get the JSON representation of a node (or the actual jQuery extended DOM node) by using any input (child DOM element, ID string, selector, etc)</p>", "summary": "<p>get the JSON representation of a node (or the actual jQuery extended DOM node) by using any input (child DOM element, ID string, selector, etc)</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "get_node : function (obj, as_dom) {\n\t\t\tif(obj && obj.id) {\n\t\t\t\tobj = obj.id;\n\t\t\t}\n\t\t\tvar dom;\n\t\t\ttry {\n\t\t\t\tif(this._model.data[obj]) {\n\t\t\t\t\tobj = this._model.data[obj];\n\t\t\t\t}\n\t\t\t\telse if(((dom = $(obj, this.element)).length || (dom = $('#' + obj, this.element)).length) && this._model.data[dom.closest('li').attr('id')]) {\n\t\t\t\t\tobj = this._model.data[dom.closest('li').attr('id')];\n\t\t\t\t}\n\t\t\t\telse if((dom = $(obj, this.element)).length && dom.hasClass('jstree')) {\n\t\t\t\t\tobj = this._model.data['#'];\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\n\t\t\t\tif(as_dom) {\n\t\t\t\t\tobj = obj.id === '#' ? this.element : $(document.getElementById(obj.id));\n\t\t\t\t}\n\t\t\t\treturn obj;\n\t\t\t} catch (ex) { return false; }\n\t\t},"}, {"tags": [{"type": "name", "string": "get_next_dom(obj [, strict])"}, {"type": "param", "types": ["mixed"], "name": "obj", "description": ""}, {"type": "param", "types": ["Boolean"], "name": "strict", "description": ""}, {"type": "return", "types": ["j<PERSON><PERSON><PERSON>"], "description": ""}], "description": {"full": "<p>get the next visible node that is below the <code>obj</code> node. If <code>strict</code> is set to <code>true</code> only sibling nodes are returned.</p>", "summary": "<p>get the next visible node that is below the <code>obj</code> node. If <code>strict</code> is set to <code>true</code> only sibling nodes are returned.</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "get_next_dom : function (obj, strict) {\n\t\t\tvar tmp;\n\t\t\tobj = this.get_node(obj, true);\n\t\t\tif(obj[0] === this.element[0]) {\n\t\t\t\ttmp = this._firstChild(this.get_container_ul()[0]);\n\t\t\t\treturn tmp ? $(tmp) : false;\n\t\t\t}\n\t\t\tif(!obj || !obj.length) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tif(strict) {\n\t\t\t\ttmp = this._nextSibling(obj[0]);\n\t\t\t\treturn tmp ? $(tmp) : false;\n\t\t\t}\n\t\t\tif(obj.hasClass(\"jstree-open\")) {\n\t\t\t\ttmp = this._firstChild(obj.children('ul')[0]);\n\t\t\t\treturn tmp ? $(tmp) : false;\n\t\t\t}\n\t\t\tif((tmp = this._nextSibling(obj[0])) !== null) {\n\t\t\t\treturn $(tmp);\n\t\t\t}\n\t\t\treturn obj.parentsUntil(\".jstree\",\"li\").next(\"li\").eq(0);\n\t\t},"}, {"tags": [{"type": "name", "string": "get_prev_dom(obj [, strict])"}, {"type": "param", "types": ["mixed"], "name": "obj", "description": ""}, {"type": "param", "types": ["Boolean"], "name": "strict", "description": ""}, {"type": "return", "types": ["j<PERSON><PERSON><PERSON>"], "description": ""}], "description": {"full": "<p>get the previous visible node that is above the <code>obj</code> node. If <code>strict</code> is set to <code>true</code> only sibling nodes are returned.</p>", "summary": "<p>get the previous visible node that is above the <code>obj</code> node. If <code>strict</code> is set to <code>true</code> only sibling nodes are returned.</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "get_prev_dom : function (obj, strict) {\n\t\t\tvar tmp;\n\t\t\tobj = this.get_node(obj, true);\n\t\t\tif(obj[0] === this.element[0]) {\n\t\t\t\ttmp = this.get_container_ul()[0].lastChild;\n\t\t\t\treturn tmp ? $(tmp) : false;\n\t\t\t}\n\t\t\tif(!obj || !obj.length) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tif(strict) {\n\t\t\t\ttmp = this._previousSibling(obj[0]);\n\t\t\t\treturn tmp ? $(tmp) : false;\n\t\t\t}\n\t\t\tif((tmp = this._previousSibling(obj[0])) !== null) {\n\t\t\t\tobj = $(tmp);\n\t\t\t\twhile(obj.hasClass(\"jstree-open\")) {\n\t\t\t\t\tobj = obj.children(\"ul:eq(0)\").children(\"li:last\");\n\t\t\t\t}\n\t\t\t\treturn obj;\n\t\t\t}\n\t\t\ttmp = obj[0].parentNode.parentNode;\n\t\t\treturn tmp && tmp.tagName === 'LI' ? $(tmp) : false;\n\t\t},"}, {"tags": [{"type": "name", "string": "get_parent(obj)"}, {"type": "param", "types": ["mixed"], "name": "obj", "description": ""}, {"type": "return", "types": ["String"], "description": ""}], "description": {"full": "<p>get the parent ID of a node</p>", "summary": "<p>get the parent ID of a node</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "get_parent : function (obj) {\n\t\t\tobj = this.get_node(obj);\n\t\t\tif(!obj || obj.id === '#') {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\treturn obj.parent;\n\t\t},"}, {"tags": [{"type": "name", "string": "get_children_dom(obj)"}, {"type": "param", "types": ["mixed"], "name": "obj", "description": ""}, {"type": "return", "types": ["j<PERSON><PERSON><PERSON>"], "description": ""}], "description": {"full": "<p>get a jQuery collection of all the children of a node (node must be rendered)</p>", "summary": "<p>get a jQuery collection of all the children of a node (node must be rendered)</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "get_children_dom : function (obj) {\n\t\t\tobj = this.get_node(obj, true);\n\t\t\tif(obj[0] === this.element[0]) {\n\t\t\t\treturn this.get_container_ul().children(\"li\");\n\t\t\t}\n\t\t\tif(!obj || !obj.length) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\treturn obj.children(\"ul\").children(\"li\");\n\t\t},"}, {"tags": [{"type": "name", "string": "is_parent(obj)"}, {"type": "param", "types": ["mixed"], "name": "obj", "description": ""}, {"type": "return", "types": ["Boolean"], "description": ""}], "description": {"full": "<p>checks if a node has children</p>", "summary": "<p>checks if a node has children</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "is_parent : function (obj) {\n\t\t\tobj = this.get_node(obj);\n\t\t\treturn obj && (obj.state.loaded === false || obj.children.length);\n\t\t},"}, {"tags": [{"type": "name", "string": "is_loaded(obj)"}, {"type": "param", "types": ["mixed"], "name": "obj", "description": ""}, {"type": "return", "types": ["Boolean"], "description": ""}], "description": {"full": "<p>checks if a node is loaded (its children are available)</p>", "summary": "<p>checks if a node is loaded (its children are available)</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "is_loaded : function (obj) {\n\t\t\tobj = this.get_node(obj);\n\t\t\treturn obj && obj.state.loaded;\n\t\t},"}, {"tags": [{"type": "name", "string": "is_loading(obj)"}, {"type": "param", "types": ["mixed"], "name": "obj", "description": ""}, {"type": "return", "types": ["Boolean"], "description": ""}], "description": {"full": "<p>check if a node is currently loading (fetching children)</p>", "summary": "<p>check if a node is currently loading (fetching children)</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "is_loading : function (obj) {\n\t\t\tobj = this.get_node(obj, true);\n\t\t\treturn obj && obj.hasClass(\"jstree-loading\");\n\t\t},"}, {"tags": [{"type": "name", "string": "is_open(obj)"}, {"type": "param", "types": ["mixed"], "name": "obj", "description": ""}, {"type": "return", "types": ["Boolean"], "description": ""}], "description": {"full": "<p>check if a node is opened</p>", "summary": "<p>check if a node is opened</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "is_open : function (obj) {\n\t\t\tobj = this.get_node(obj);\n\t\t\treturn obj && obj.state.opened;\n\t\t},"}, {"tags": [{"type": "name", "string": "is_closed(obj)"}, {"type": "param", "types": ["mixed"], "name": "obj", "description": ""}, {"type": "return", "types": ["Boolean"], "description": ""}], "description": {"full": "<p>check if a node is in a closed state</p>", "summary": "<p>check if a node is in a closed state</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "is_closed : function (obj) {\n\t\t\tobj = this.get_node(obj);\n\t\t\treturn obj && this.is_parent(obj) && !obj.state.opened;\n\t\t},"}, {"tags": [{"type": "name", "string": "is_leaf(obj)"}, {"type": "param", "types": ["mixed"], "name": "obj", "description": ""}, {"type": "return", "types": ["Boolean"], "description": ""}], "description": {"full": "<p>check if a node has no children</p>", "summary": "<p>check if a node has no children</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "is_leaf : function (obj) {\n\t\t\treturn !this.is_parent(obj);\n\t\t},"}, {"tags": [{"type": "name", "string": "load_node(obj [, callback])"}, {"type": "param", "types": ["mixed"], "name": "obj", "description": ""}, {"type": "param", "types": ["function"], "name": "callback", "description": "a function to be executed once loading is conplete, the function is executed in the instance's scope and receives two arguments - the node and a boolean status"}, {"type": "return", "types": ["Boolean"], "description": ""}, {"type": "trigger", "string": "load_node.jstree"}], "description": {"full": "<p>loads a node (fetches its children using the <code>core.data</code> setting). Multiple nodes can be passed to by using an array.</p>", "summary": "<p>loads a node (fetches its children using the <code>core.data</code> setting). Multiple nodes can be passed to by using an array.</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "load_node : function (obj, callback) {\n\t\t\tvar t1, t2;\n\t\t\tif($.isArray(obj)) {\n\t\t\t\tobj = obj.slice();\n\t\t\t\tfor(t1 = 0, t2 = obj.length; t1 < t2; t1++) {\n\t\t\t\t\tthis.load_node(obj[t1], callback);\n\t\t\t\t}\n\t\t\t\treturn true;\n\t\t\t}\n\t\t\tobj = this.get_node(obj);\n\t\t\tif(!obj) {\n\t\t\t\tcallback.call(this, obj, false);\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tthis.get_node(obj, true).addClass(\"jstree-loading\");\n\t\t\tthis._load_node(obj, $.proxy(function (status) {\n\t\t\t\tobj.state.loaded = status;\n\t\t\t\tthis.get_node(obj, true).removeClass(\"jstree-loading\");"}, {"tags": [{"type": "event", "string": ""}, {"type": "name", "string": "load_node.jstree"}, {"type": "param", "types": ["Object"], "name": "node", "description": "the node that was loading"}, {"type": "param", "types": ["Boolean"], "name": "status", "description": "was the node loaded successfully"}], "description": {"full": "<p>triggered after a node is loaded</p>", "summary": "<p>triggered after a node is loaded</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "this.trigger('load_node', { \"node\" : obj, \"status\" : status });\n\t\t\t\tif(callback) {\n\t\t\t\t\tcallback.call(this, obj, status);\n\t\t\t\t}\n\t\t\t}, this));\n\t\t\treturn true;\n\t\t},"}, {"tags": [{"type": "private", "string": ""}, {"type": "name", "string": "_load_node(obj [, callback])"}, {"type": "param", "types": ["mixed"], "name": "obj", "description": ""}, {"type": "param", "types": ["function"], "name": "callback", "description": "a function to be executed once loading is conplete, the function is executed in the instance's scope and receives one argument - a boolean status"}, {"type": "return", "types": ["Boolean"], "description": ""}], "description": {"full": "<p>handles the actual loading of a node. Used only internally.</p>", "summary": "<p>handles the actual loading of a node. Used only internally.</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "_load_node : function (obj, callback) {\n\t\t\tvar s = this.settings.core.data;\n\t\t\t// use original HTML\n\t\t\tif(!s) {\n\t\t\t\treturn callback.call(this, obj.id === '#' ? this._append_html_data(obj, this._data.core.original_container_html.clone(true)) : false);\n\t\t\t}\n\t\t\tif($.isFunction(s)) {\n\t\t\t\treturn s.call(this, obj, $.proxy(function (d) {\n\t\t\t\t\treturn callback.call(this, this[typeof d === 'string' ? '_append_html_data' : '_append_json_data'](obj, typeof d === 'string' ? $(d) : d));\n\t\t\t\t}, this));\n\t\t\t}\n\t\t\tif(typeof s === 'object') {\n\t\t\t\tif(s.url) {\n\t\t\t\t\ts = $.extend(true, {}, s);\n\t\t\t\t\tif($.isFunction(s.url)) {\n\t\t\t\t\t\ts.url = s.url.call(this, obj);\n\t\t\t\t\t}\n\t\t\t\t\tif($.isFunction(s.data)) {\n\t\t\t\t\t\ts.data = s.data.call(this, obj);\n\t\t\t\t\t}\n\t\t\t\t\treturn $.ajax(s)\n\t\t\t\t\t\t.done($.proxy(function (d,t,x) {\n\t\t\t\t\t\t\t\tvar type = x.getResponseHeader('Content-Type');\n\t\t\t\t\t\t\t\tif(type.indexOf('json') !== -1) {\n\t\t\t\t\t\t\t\t\treturn callback.call(this, this._append_json_data(obj, d));\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tif(type.indexOf('html') !== -1) {\n\t\t\t\t\t\t\t\t\treturn callback.call(this, this._append_html_data(obj, $(d)));\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}, this))\n\t\t\t\t\t\t.fail($.proxy(function () {\n\t\t\t\t\t\t\t\tcallback.call(this, false);\n\t\t\t\t\t\t\t}, this));\n\t\t\t\t}\n\t\t\t\treturn callback.call(this, this._append_json_data(obj, s));\n\t\t\t}\n\t\t\tif(typeof s === 'string') {\n\t\t\t\treturn callback.call(this, this._append_html_data(obj, s));\n\t\t\t}\n\t\t\treturn callback.call(this, false);\n\t\t},"}, {"tags": [{"type": "private", "string": ""}, {"type": "name", "string": "_node_changed(obj [, callback])"}, {"type": "param", "types": ["mixed"], "name": "obj", "description": ""}], "description": {"full": "<p>adds a node to the list of nodes to redraw. Used only internally.</p>", "summary": "<p>adds a node to the list of nodes to redraw. Used only internally.</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "_node_changed : function (obj) {\n\t\t\tobj = this.get_node(obj);\n\t\t\tif(obj) {\n\t\t\t\tthis._model.changed.push(obj.id);\n\t\t\t}\n\t\t},"}, {"tags": [{"type": "private", "string": ""}, {"type": "name", "string": "_append_html_data(obj, data)"}, {"type": "param", "types": ["mixed"], "name": "obj", "description": "the node to append to"}, {"type": "param", "types": ["String"], "name": "data", "description": "the HTML string to parse and append"}, {"type": "return", "types": ["Boolean"], "description": ""}, {"type": "trigger", "string": "model.jstree, changed.jstree"}], "description": {"full": "<p>appends HTML content to the tree. Used internally.</p>", "summary": "<p>appends HTML content to the tree. Used internally.</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "_append_html_data : function (dom, data) {\n\t\t\tdom = this.get_node(dom);\n\t\t\tvar dat = data.is('ul') ? data.children() : data,\n\t\t\t\tpar = dom.id,\n\t\t\t\tchd = [],\n\t\t\t\tdpc = [],\n\t\t\t\tm = this._model.data,\n\t\t\t\tp = m[par],\n\t\t\t\ts = this._data.core.selected.length,\n\t\t\t\ttmp, i, j;\n\t\t\tdat.each($.proxy(function (i, v) {\n\t\t\t\ttmp = this._parse_model_from_html($(v), par, p.parents.concat());\n\t\t\t\tif(tmp) {\n\t\t\t\t\tchd.push(tmp);\n\t\t\t\t\tdpc.push(tmp);\n\t\t\t\t\tif(m[tmp].children_d.length) {\n\t\t\t\t\t\tdpc = dpc.concat(m[tmp].children_d);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}, this));\n\t\t\tp.children = chd;\n\t\t\tp.children_d = dpc;\n\t\t\tfor(i = 0, j = p.parents.length; i < j; i++) {\n\t\t\t\tm[p.parents[i]].children_d = m[p.parents[i]].children_d.concat(dpc);\n\t\t\t}"}, {"tags": [{"type": "event", "string": ""}, {"type": "name", "string": "model.jstree"}, {"type": "param", "types": ["Array"], "name": "nodes", "description": "an array of node IDs"}, {"type": "param", "types": ["String"], "name": "parent", "description": "the parent ID of the nodes"}], "description": {"full": "<p>triggered when new data is inserted to the tree model</p>", "summary": "<p>triggered when new data is inserted to the tree model</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "this.trigger('model', { \"nodes\" : dpc, 'parent' : par });\n\t\t\tif(par !== '#') {\n\t\t\t\tthis._node_changed(par);\n\t\t\t\tthis.redraw();\n\t\t\t}\n\t\t\telse {\n\t\t\t\tthis.get_container_ul().children('.jstree-initial-node').remove();\n\t\t\t\tthis.redraw(true);\n\t\t\t}\n\t\t\tif(this._data.core.selected.length !== s) {\n\t\t\t\tthis.trigger('changed', { 'action' : 'model', 'selected' : this._data.core.selected });\n\t\t\t}\n\t\t\treturn true;\n\t\t},"}, {"tags": [{"type": "private", "string": ""}, {"type": "name", "string": "_append_json_data(obj, data)"}, {"type": "param", "types": ["mixed"], "name": "obj", "description": "the node to append to"}, {"type": "param", "types": ["String"], "name": "data", "description": "the JSON object to parse and append"}, {"type": "return", "types": ["Boolean"], "description": ""}], "description": {"full": "<p>appends JSON content to the tree. Used internally.</p>", "summary": "<p>appends JSON content to the tree. Used internally.</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "_append_json_data : function (dom, data) {\n\t\t\tdom = this.get_node(dom);\n\t\t\tvar dat = data,\n\t\t\t\tpar = dom.id,\n\t\t\t\tchd = [],\n\t\t\t\tdpc = [],\n\t\t\t\tm = this._model.data,\n\t\t\t\tp = m[par],\n\t\t\t\ts = this._data.core.selected.length,\n\t\t\t\ttmp, i, j;\n\t\t\t// *%$@!!!\n\t\t\tif(dat.d) {\n\t\t\t\tdat = dat.d;\n\t\t\t\tif(typeof dat === \"string\") {\n\t\t\t\t\tdat = $.vakata.json.decode(dat);\n\t\t\t\t}\n\t\t\t}\n\t\t\tif(!$.isArray(dat)) { dat = [dat]; }\n\t\t\tif(dat.length && dat[0].id !== undefined && dat[0].parent !== undefined) {\n\t\t\t\t// Flat JSON support (for easy import from DB):\n\t\t\t\t// 1) convert to object (foreach)\n\t\t\t\tfor(i = 0, j = dat.length; i < j; i++) {\n\t\t\t\t\tif(!dat[i].children) {\n\t\t\t\t\t\tdat[i].children = [];\n\t\t\t\t\t}\n\t\t\t\t\tm[dat[i].id] = dat[i];\n\t\t\t\t}\n\t\t\t\t// 2) populate children (foreach)\n\t\t\t\tfor(i = 0, j = dat.length; i < j; i++) {\n\t\t\t\t\tm[dat[i].parent].children.push(dat[i].id);\n\t\t\t\t\t// populate parent.children_d\n\t\t\t\t\tp.children_d.push(dat[i].id);\n\t\t\t\t}\n\t\t\t\t// 3) normalize && populate parents and children_d with recursion\n\t\t\t\tfor(i = 0, j = p.children.length; i < j; i++) {\n\t\t\t\t\ttmp = this._parse_model_from_flat_json(m[p.children[i]], par, p.parents.concat());\n\t\t\t\t\tdpc.push(tmp);\n\t\t\t\t\tif(m[tmp].children_d.length) {\n\t\t\t\t\t\tdpc = dpc.concat(m[tmp].children_d);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t// ?) three_state selection - p.state.selected && t - (if three_state foreach(dat => ch) -> foreach(parents) if(parent.selected) child.selected = true;\n\t\t\t}\n\t\t\telse {\n\t\t\t\tfor(i = 0, j = dat.length; i < j; i++) {\n\t\t\t\t\ttmp = this._parse_model_from_json(dat[i], par, p.parents.concat());\n\t\t\t\t\tif(tmp) {\n\t\t\t\t\t\tchd.push(tmp);\n\t\t\t\t\t\tdpc.push(tmp);\n\t\t\t\t\t\tif(m[tmp].children_d.length) {\n\t\t\t\t\t\t\tdpc = dpc.concat(m[tmp].children_d);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tp.children = chd;\n\t\t\t\tp.children_d = dpc;\n\t\t\t\tfor(i = 0, j = p.parents.length; i < j; i++) {\n\t\t\t\t\tm[p.parents[i]].children_d = m[p.parents[i]].children_d.concat(dpc);\n\t\t\t\t}\n\t\t\t}\n\t\t\tthis.trigger('model', { \"nodes\" : dpc, 'parent' : par });\n\n\t\t\tif(par !== '#') {\n\t\t\t\tthis._node_changed(par);\n\t\t\t\tthis.redraw();\n\t\t\t}\n\t\t\telse {\n\t\t\t\t// this.get_container_ul().children('.jstree-initial-node').remove();\n\t\t\t\tthis.redraw(true);\n\t\t\t}\n\t\t\tif(this._data.core.selected.length !== s) {\n\t\t\t\tthis.trigger('changed', { 'action' : 'model', 'selected' : this._data.core.selected });\n\t\t\t}\n\t\t\treturn true;\n\t\t},"}, {"tags": [{"type": "private", "string": ""}, {"type": "name", "string": "_parse_model_from_html(d [, p, ps])"}, {"type": "param", "types": ["j<PERSON><PERSON><PERSON>"], "name": "d", "description": "the jQuery object to parse"}, {"type": "param", "types": ["String"], "name": "p", "description": "the parent ID"}, {"type": "param", "types": ["Array"], "name": "ps", "description": "list of all parents"}, {"type": "return", "types": ["String"], "description": "the ID of the object added to the model"}], "description": {"full": "<p>parses a node from a jQuery object and appends them to the in memory tree model. Used internally.</p>", "summary": "<p>parses a node from a jQuery object and appends them to the in memory tree model. Used internally.</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "_parse_model_from_html : function (d, p, ps) {\n\t\t\tif(!ps) { ps = []; }\n\t\t\telse { ps = [].concat(ps); }\n\t\t\tif(p) { ps.unshift(p); }\n\t\t\tvar c, e, m = this._model.data,\n\t\t\t\tdata = {\n\t\t\t\t\tid\t\t\t: false,\n\t\t\t\t\ttext\t\t: false,\n\t\t\t\t\ticon\t\t: true,\n\t\t\t\t\tparent\t\t: p,\n\t\t\t\t\tparents\t\t: ps,\n\t\t\t\t\tchildren\t: [],\n\t\t\t\t\tchildren_d\t: [],\n\t\t\t\t\tdata\t\t: null,\n\t\t\t\t\tstate\t\t: { },\n\t\t\t\t\tli_attr\t\t: { id : false },\n\t\t\t\t\ta_attr\t\t: { href : '#' },\n\t\t\t\t\toriginal\t: false\n\t\t\t\t}, i, tmp, tid;\n\t\t\tfor(i in this._model.default_state) {\n\t\t\t\tif(this._model.default_state.hasOwnProperty(i)) {\n\t\t\t\t\tdata.state[i] = this._model.default_state[i];\n\t\t\t\t}\n\t\t\t}\n\t\t\ttmp = $.vakata.attributes(d, true);\n\t\t\t$.each(tmp, function (i, v) {\n\t\t\t\tv = $.trim(v);\n\t\t\t\tif(!v.length) { return true; }\n\t\t\t\tdata.li_attr[i] = v;\n\t\t\t\tif(i === 'id') {\n\t\t\t\t\tdata.id = v;\n\t\t\t\t}\n\t\t\t});\n\t\t\ttmp = d.children('a').eq(0);\n\t\t\tif(tmp.length) {\n\t\t\t\ttmp = $.vakata.attributes(tmp, true);\n\t\t\t\t$.each(tmp, function (i, v) {\n\t\t\t\t\tv = $.trim(v);\n\t\t\t\t\tif(v.length) {\n\t\t\t\t\t\tdata.a_attr[i] = v;\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t}\n\t\t\ttmp = d.children(\"a:eq(0)\").length ? d.children(\"a:eq(0)\").clone() : d.clone();\n\t\t\ttmp.children(\"ins, i, ul\").remove();\n\t\t\ttmp = tmp.html();\n\t\t\ttmp = $('<div />').html(tmp);\n\t\t\tdata.text = tmp.html();\n\t\t\ttmp = d.data();\n\t\t\tdata.data = tmp ? $.extend(true, {}, tmp) : null;\n\t\t\tdata.state.opened = d.hasClass('jstree-open');\n\t\t\tdata.state.selected = d.children('a').hasClass('jstree-clicked');\n\t\t\tdata.state.disabled = d.children('a').hasClass('jstree-disabled');\n\t\t\tif(data.data && data.data.jstree) {\n\t\t\t\tfor(i in data.data.jstree) {\n\t\t\t\t\tif(data.data.jstree.hasOwnProperty(i)) {\n\t\t\t\t\t\tdata.state[i] = data.data.jstree[i];\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\ttmp = d.children(\"a\").children(\".jstree-themeicon\");\n\t\t\tif(tmp.length) {\n\t\t\t\tdata.icon = tmp.hasClass('jstree-themeicon-hidden') ? false : tmp.attr('rel');\n\t\t\t}\n\t\t\tif(data.state.icon) {\n\t\t\t\tdata.icon = data.state.icon;\n\t\t\t}\n\t\t\ttmp = d.children(\"ul\").children(\"li\");\n\t\t\tdo {\n\t\t\t\ttid = 'j' + this._id + '_' + (++total_nodes);\n\t\t\t} while(m[tid]);\n\t\t\tdata.id = data.li_attr.id || tid;\n\t\t\tif(tmp.length) {\n\t\t\t\ttmp.each($.proxy(function (i, v) {\n\t\t\t\t\tc = this._parse_model_from_html($(v), data.id, ps);\n\t\t\t\t\te = this._model.data[c];\n\t\t\t\t\tdata.children.push(c);\n\t\t\t\t\tif(e.children_d.length) {\n\t\t\t\t\t\tdata.children_d = data.children_d.concat(e.children_d);\n\t\t\t\t\t}\n\t\t\t\t}, this));\n\t\t\t\tdata.children_d = data.children_d.concat(data.children);\n\t\t\t}\n\t\t\telse {\n\t\t\t\tif(d.hasClass('jstree-closed')) {\n\t\t\t\t\tdata.state.loaded = false;\n\t\t\t\t}\n\t\t\t}\n\t\t\tif(data.li_attr.class) {\n\t\t\t\tdata.li_attr.class = data.li_attr.class.replace('jstree-closed','').replace('jstree-open','');\n\t\t\t}\n\t\t\tif(data.a_attr.class) {\n\t\t\t\tdata.a_attr.class = data.a_attr.class.replace('jstree-clicked','').replace('jstree-disabled','');\n\t\t\t}\n\t\t\tm[data.id] = data;\n\t\t\tif(data.state.selected) {\n\t\t\t\tthis._data.core.selected.push(data.id);\n\t\t\t}\n\t\t\treturn data.id;\n\t\t},"}, {"tags": [{"type": "private", "string": ""}, {"type": "name", "string": "_parse_model_from_flat_json(d [, p, ps])"}, {"type": "param", "types": ["Object"], "name": "d", "description": "the JSON object to parse"}, {"type": "param", "types": ["String"], "name": "p", "description": "the parent ID"}, {"type": "param", "types": ["Array"], "name": "ps", "description": "list of all parents"}, {"type": "return", "types": ["String"], "description": "the ID of the object added to the model"}], "description": {"full": "<p>parses a node from a JSON object (used when dealing with flat data, which has no nesting of children, but has id and parent properties) and appends it to the in memory tree model. Used internally.</p>", "summary": "<p>parses a node from a JSON object (used when dealing with flat data, which has no nesting of children, but has id and parent properties) and appends it to the in memory tree model. Used internally.</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "_parse_model_from_flat_json : function (d, p, ps) {\n\t\t\tif(!ps) { ps = []; }\n\t\t\telse { ps = ps.concat(); }\n\t\t\tif(p) { ps.unshift(p); }\n\t\t\tvar tid = d.id,\n\t\t\t\tm = this._model.data,\n\t\t\t\tdf = this._model.default_state,\n\t\t\t\ti, j, c, e,\n\t\t\t\ttmp = {\n\t\t\t\t\tid\t\t\t: tid,\n\t\t\t\t\ttext\t\t: d.text || '',\n\t\t\t\t\ticon\t\t: d.icon !== undefined ? d.icon : true,\n\t\t\t\t\tparent\t\t: p,\n\t\t\t\t\tparents\t\t: ps,\n\t\t\t\t\tchildren\t: d.children || [],\n\t\t\t\t\tchildren_d\t: d.children_d || [],\n\t\t\t\t\tdata\t\t: d.data,\n\t\t\t\t\tstate\t\t: { },\n\t\t\t\t\tli_attr\t\t: { id : false },\n\t\t\t\t\ta_attr\t\t: { href : '#' },\n\t\t\t\t\toriginal\t: false\n\t\t\t\t};\n\t\t\tfor(i in df) {\n\t\t\t\tif(df.hasOwnProperty(i)) {\n\t\t\t\t\ttmp.state[i] = df[i];\n\t\t\t\t}\n\t\t\t}\n\t\t\tif(d && d.data && d.data.jstree && d.data.jstree.icon) {\n\t\t\t\ttmp.icon = d.data.jstree.icon;\n\t\t\t}\n\t\t\tif(d && d.data) {\n\t\t\t\ttmp.data = d.data;\n\t\t\t\tif(d.data.jstree) {\n\t\t\t\t\tfor(i in d.data.jstree) {\n\t\t\t\t\t\tif(d.data.jstree.hasOwnProperty(i)) {\n\t\t\t\t\t\t\ttmp.state[i] = d.data.jstree[i];\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tif(d && typeof d.state === 'object') {\n\t\t\t\tfor (i in d.state) {\n\t\t\t\t\tif(d.state.hasOwnProperty(i)) {\n\t\t\t\t\t\ttmp.state[i] = d.state[i];\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tif(d && typeof d.li_attr === 'object') {\n\t\t\t\tfor (i in d.li_attr) {\n\t\t\t\t\tif(d.li_attr.hasOwnProperty(i)) {\n\t\t\t\t\t\ttmp.li_attr[i] = d.li_attr[i];\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tif(!tmp.li_attr.id) {\n\t\t\t\ttmp.li_attr.id = tid;\n\t\t\t}\n\t\t\tif(d && typeof d.a_attr === 'object') {\n\t\t\t\tfor (i in d.a_attr) {\n\t\t\t\t\tif(d.a_attr.hasOwnProperty(i)) {\n\t\t\t\t\t\ttmp.a_attr[i] = d.a_attr[i];\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tif(d && d.children && d.children === true) {\n\t\t\t\ttmp.state.loaded = false;\n\t\t\t\ttmp.children = [];\n\t\t\t\ttmp.children_d = [];\n\t\t\t}\n\t\t\tm[tmp.id] = tmp;\n\t\t\tfor(i = 0, j = tmp.children.length; i < j; i++) {\n\t\t\t\tc = this._parse_model_from_flat_json(m[tmp.children[i]], tmp.id, ps);\n\t\t\t\te = m[c];\n\t\t\t\ttmp.children_d.push(c);\n\t\t\t\tif(e.children_d.length) {\n\t\t\t\t\ttmp.children_d = tmp.children_d.concat(e.children_d);\n\t\t\t\t}\n\t\t\t}\n\t\t\tdelete d.data;\n\t\t\tdelete d.children;\n\t\t\tm[tmp.id].original = d;\n\t\t\tif(tmp.state.selected) {\n\t\t\t\tthis._data.core.selected.push(tmp.id);\n\t\t\t}\n\t\t\treturn tmp.id;\n\t\t},"}, {"tags": [{"type": "private", "string": ""}, {"type": "name", "string": "_parse_model_from_json(d [, p, ps])"}, {"type": "param", "types": ["Object"], "name": "d", "description": "the JSON object to parse"}, {"type": "param", "types": ["String"], "name": "p", "description": "the parent ID"}, {"type": "param", "types": ["Array"], "name": "ps", "description": "list of all parents"}, {"type": "return", "types": ["String"], "description": "the ID of the object added to the model"}], "description": {"full": "<p>parses a node from a JSON object and appends it to the in memory tree model. Used internally.</p>", "summary": "<p>parses a node from a JSON object and appends it to the in memory tree model. Used internally.</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "_parse_model_from_json : function (d, p, ps) {\n\t\t\tif(!ps) { ps = []; }\n\t\t\telse { ps = ps.concat(); }\n\t\t\tif(p) { ps.unshift(p); }\n\t\t\tvar tid = false, i, j, c, e, m = this._model.data, df = this._model.default_state, tmp;\n\t\t\tdo {\n\t\t\t\ttid = 'j' + this._id + '_' + (++total_nodes);\n\t\t\t} while(m[tid]);\n\n\t\t\ttmp = {\n\t\t\t\tid\t\t\t: false,\n\t\t\t\ttext\t\t: typeof d === 'string' ? d : '',\n\t\t\t\ticon\t\t: typeof d === 'object' && d.icon !== undefined ? d.icon : true,\n\t\t\t\tparent\t\t: p,\n\t\t\t\tparents\t\t: ps,\n\t\t\t\tchildren\t: [],\n\t\t\t\tchildren_d\t: [],\n\t\t\t\tdata\t\t: null,\n\t\t\t\tstate\t\t: { },\n\t\t\t\tli_attr\t\t: { id : false },\n\t\t\t\ta_attr\t\t: { href : '#' },\n\t\t\t\toriginal\t: false\n\t\t\t};\n\t\t\tfor(i in df) {\n\t\t\t\tif(df.hasOwnProperty(i)) {\n\t\t\t\t\ttmp.state[i] = df[i];\n\t\t\t\t}\n\t\t\t}\n\t\t\tif(d && d.id) { tmp.id = d.id; }\n\t\t\tif(d && d.text) { tmp.text = d.text; }\n\t\t\tif(d && d.data && d.data.jstree && d.data.jstree.icon) {\n\t\t\t\ttmp.icon = d.data.jstree.icon;\n\t\t\t}\n\t\t\tif(d && d.data) {\n\t\t\t\ttmp.data = d.data;\n\t\t\t\tif(d.data.jstree) {\n\t\t\t\t\tfor(i in d.data.jstree) {\n\t\t\t\t\t\tif(d.data.jstree.hasOwnProperty(i)) {\n\t\t\t\t\t\t\ttmp.state[i] = d.data.jstree[i];\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tif(d && typeof d.state === 'object') {\n\t\t\t\tfor (i in d.state) {\n\t\t\t\t\tif(d.state.hasOwnProperty(i)) {\n\t\t\t\t\t\ttmp.state[i] = d.state[i];\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tif(d && typeof d.li_attr === 'object') {\n\t\t\t\tfor (i in d.li_attr) {\n\t\t\t\t\tif(d.li_attr.hasOwnProperty(i)) {\n\t\t\t\t\t\ttmp.li_attr[i] = d.li_attr[i];\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tif(tmp.li_attr.id && !tmp.id) {\n\t\t\t\ttmp.id = tmp.li_attr.id;\n\t\t\t}\n\t\t\tif(!tmp.id) {\n\t\t\t\ttmp.id = tid;\n\t\t\t}\n\t\t\tif(!tmp.li_attr.id) {\n\t\t\t\ttmp.li_attr.id = tmp.id;\n\t\t\t}\n\t\t\tif(d && typeof d.a_attr === 'object') {\n\t\t\t\tfor (i in d.a_attr) {\n\t\t\t\t\tif(d.a_attr.hasOwnProperty(i)) {\n\t\t\t\t\t\ttmp.a_attr[i] = d.a_attr[i];\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tif(d && d.children && d.children.length) {\n\t\t\t\tfor(i = 0, j = d.children.length; i < j; i++) {\n\t\t\t\t\tc = this._parse_model_from_json(d.children[i], tmp.id, ps);\n\t\t\t\t\te = m[c];\n\t\t\t\t\ttmp.children.push(c);\n\t\t\t\t\tif(e.children_d.length) {\n\t\t\t\t\t\ttmp.children_d = tmp.children_d.concat(e.children_d);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\ttmp.children_d = tmp.children_d.concat(tmp.children);\n\t\t\t}\n\t\t\tif(d && d.children && d.children === true) {\n\t\t\t\ttmp.state.loaded = false;\n\t\t\t\ttmp.children = [];\n\t\t\t\ttmp.children_d = [];\n\t\t\t}\n\t\t\tdelete d.data;\n\t\t\tdelete d.children;\n\t\t\ttmp.original = d;\n\t\t\tm[tmp.id] = tmp;\n\t\t\tif(tmp.state.selected) {\n\t\t\t\tthis._data.core.selected.push(tmp.id);\n\t\t\t}\n\t\t\treturn tmp.id;\n\t\t},"}, {"tags": [{"type": "private", "string": ""}, {"type": "name", "string": "_redraw()"}, {"type": "trigger", "string": "redraw.jstree"}], "description": {"full": "<p>redraws all nodes that need to be redrawn. Used internally.</p>", "summary": "<p>redraws all nodes that need to be redrawn. Used internally.</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "_redraw : function () {\n\t\t\tvar nodes = this._model.force_full_redraw ? this._model.data['#'].children.concat([]) : this._model.changed.concat([]),\n\t\t\t\tf = document.createElement('UL'), tmp, i, j;\n\t\t\tfor(i = 0, j = nodes.length; i < j; i++) {\n\t\t\t\ttmp = this.redraw_node(nodes[i], true, this._model.force_full_redraw);\n\t\t\t\tif(tmp && this._model.force_full_redraw) {\n\t\t\t\t\tf.appendChild(tmp);\n\t\t\t\t}\n\t\t\t}\n\t\t\tif(this._model.force_full_redraw) {\n\t\t\t\tf.className = this.get_container_ul()[0].className;\n\t\t\t\tthis.element.empty().append(f);\n\t\t\t\t//this.get_container_ul()[0].appendChild(f);\n\t\t\t}\n\t\t\tthis._model.force_full_redraw = false;\n\t\t\tthis._model.changed = [];"}, {"tags": [{"type": "event", "string": ""}, {"type": "name", "string": "redraw.jstree"}, {"type": "param", "types": ["array"], "name": "nodes", "description": "the redrawn nodes"}], "description": {"full": "<p>triggered after nodes are redrawn</p>", "summary": "<p>triggered after nodes are redrawn</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "this.trigger('redraw', { \"nodes\" : nodes });\n\t\t},"}, {"tags": [{"type": "name", "string": "redraw([full])"}, {"type": "param", "types": ["Boolean"], "name": "full", "description": "if set to `true` all nodes are redrawn."}], "description": {"full": "<p>redraws all nodes that need to be redrawn or optionally - the whole tree</p>", "summary": "<p>redraws all nodes that need to be redrawn or optionally - the whole tree</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "redraw : function (full) {\n\t\t\tif(full) {\n\t\t\t\tthis._model.force_full_redraw = true;\n\t\t\t}\n\t\t\t//if(this._model.redraw_timeout) {\n\t\t\t//\tclearTimeout(this._model.redraw_timeout);\n\t\t\t//}\n\t\t\t//this._model.redraw_timeout = setTimeout($.proxy(this._redraw, this),0);\n\t\t\tthis._redraw();\n\t\t},"}, {"tags": [{"type": "private", "string": ""}, {"type": "name", "string": "redraw_node(node, deep, is_callback)"}, {"type": "param", "types": ["mixed"], "name": "node", "description": "the node to redraw"}, {"type": "param", "types": ["Boolean"], "name": "deep", "description": "should child nodes be redrawn too"}, {"type": "param", "types": ["Boolean"], "name": "is_callback", "description": "is this a recursion call"}], "description": {"full": "<p>redraws a single node. Used internally.</p>", "summary": "<p>redraws a single node. Used internally.</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "redraw_node : function (node, deep, is_callback) {\n\t\t\tvar obj = this.get_node(node),\n\t\t\t\tpar = false,\n\t\t\t\tind = false,\n\t\t\t\told = false,\n\t\t\t\ti = false,\n\t\t\t\tj = false,\n\t\t\t\tk = false,\n\t\t\t\tc = '',\n\t\t\t\td = document,\n\t\t\t\tm = this._model.data;\n\t\t\tif(!obj) { return false; }\n\t\t\tif(obj.id === '#') {  return this.redraw(true); }\n\t\t\tdeep = deep || obj.children.length === 0;\n\t\t\tnode = d.getElementById(obj.id); //, this.element);\n\t\t\tif(!node) {\n\t\t\t\tdeep = true;\n\t\t\t\t//node = d.createElement('LI');\n\t\t\t\tif(!is_callback) {\n\t\t\t\t\tpar = obj.parent !== '#' ? $('#' + obj.parent, this.element)[0] : null;\n\t\t\t\t\tif(par !== null && (!par || !m[obj.parent].state.opened)) {\n\t\t\t\t\t\treturn false;\n\t\t\t\t\t}\n\t\t\t\t\tind = $.inArray(obj.id, par === null ? m['#'].children : m[obj.parent].children);\n\t\t\t\t}\n\t\t\t}\n\t\t\telse {\n\t\t\t\tnode = $(node);\n\t\t\t\tif(!is_callback) {\n\t\t\t\t\tpar = node.parent().parent()[0];\n\t\t\t\t\tif(par === this.element[0]) {\n\t\t\t\t\t\tpar = null;\n\t\t\t\t\t}\n\t\t\t\t\tind = node.index();\n\t\t\t\t}\n\t\t\t\tm[obj.id].data = node.data();\n\t\t\t\tif(!deep && obj.children.length && !node.children('ul').length) {\n\t\t\t\t\tdeep = true;\n\t\t\t\t}\n\t\t\t\tif(!deep) {\n\t\t\t\t\told = node.children('UL')[0];\n\t\t\t\t}\n\t\t\t\tnode.remove();\n\t\t\t\t//node = d.createElement('LI');\n\t\t\t\t//node = node[0];\n\t\t\t}\n\t\t\tnode = _node.cloneNode(true);\n\t\t\t// node is DOM, deep is boolean\n\n\t\t\tc = 'jstree-node ';\n\t\t\tfor(i in obj.li_attr) {\n\t\t\t\tif(obj.li_attr.hasOwnProperty(i)) {\n\t\t\t\t\tif(i === 'id') { continue; }\n\t\t\t\t\tif(i !== 'class') {\n\t\t\t\t\t\tnode.setAttribute(i, obj.li_attr[i]);\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\tc += obj.li_attr[i];\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tif(!obj.children.length && obj.state.loaded) {\n\t\t\t\tc += ' jstree-leaf';\n\t\t\t}\n\t\t\telse {\n\t\t\t\tc += obj.state.opened ? ' jstree-open' : ' jstree-closed';\n\t\t\t}\n\t\t\tif(obj.parent !== null && m[obj.parent].children[m[obj.parent].children.length - 1] === obj.id) {\n\t\t\t\tc += ' jstree-last';\n\t\t\t}\n\t\t\tnode.id = obj.id;\n\t\t\tnode.className = c;\n\t\t\tc = ( obj.state.selected ? ' jstree-clicked' : '') + ( obj.state.disabled ? ' jstree-disabled' : '');\n\t\t\tfor(j in obj.a_attr) {\n\t\t\t\tif(obj.a_attr.hasOwnProperty(j)) {\n\t\t\t\t\tif(j === 'href' && obj.a_attr[j] === '#') { continue; }\n\t\t\t\t\tif(j !== 'class') {\n\t\t\t\t\t\tnode.childNodes[1].setAttribute(j, obj.a_attr[j]);\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\tc += ' ' + obj.a_attr[j];\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tif(c.length) {\n\t\t\t\tnode.childNodes[1].className = 'jstree-anchor ' + c;\n\t\t\t}\n\t\t\tif((obj.icon && obj.icon !== true) || obj.icon === false) {\n\t\t\t\tif(obj.icon === false) {\n\t\t\t\t\tnode.childNodes[1].childNodes[0].className += ' jstree-themeicon-hidden';\n\t\t\t\t}\n\t\t\t\telse if(obj.icon.indexOf('/') === -1 && obj.icon.indexOf('.') === -1) {\n\t\t\t\t\tnode.childNodes[1].childNodes[0].className += ' ' + obj.icon + ' jstree-themeicon-custom';\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\tnode.childNodes[1].childNodes[0].style.backgroundImage = 'url('+obj.icon+')';\n\t\t\t\t\tnode.childNodes[1].childNodes[0].style.backgroundPosition = 'center center';\n\t\t\t\t\tnode.childNodes[1].childNodes[0].style.backgroundSize = 'auto';\n\t\t\t\t\tnode.childNodes[1].childNodes[0].className += ' jstree-themeicon-custom';\n\t\t\t\t}\n\t\t\t}\n\t\t\t//node.childNodes[1].appendChild(d.createTextNode(obj.text));\n\t\t\tnode.childNodes[1].innerHTML += obj.text;\n\t\t\tif(obj.data) { $.data(node, obj.data); }\n\n\t\t\tif(deep && obj.children.length && obj.state.opened) {\n\t\t\t\tk = d.createElement('UL');\n\t\t\t\tk.setAttribute('role', 'group');\n\t\t\t\tk.className = 'jstree-children';\n\t\t\t\tfor(i = 0, j = obj.children.length; i < j; i++) {\n\t\t\t\t\tk.appendChild(this.redraw_node(obj.children[i], deep, true));\n\t\t\t\t}\n\t\t\t\tnode.appendChild(k);\n\t\t\t}\n\t\t\tif(old) {\n\t\t\t\tnode.appendChild(old);\n\t\t\t}\n\t\t\tif(!is_callback) {\n\t\t\t\t// append back using par / ind\n\t\t\t\tif(!par) {\n\t\t\t\t\tpar = this.element[0];\n\t\t\t\t}\n\t\t\t\tif(!par.getElementsByTagName('UL').length) {\n\t\t\t\t\ti = d.createElement('UL');\n\t\t\t\t\ti.setAttribute('role', 'group');\n\t\t\t\t\ti.className = 'jstree-children';\n\t\t\t\t\tpar.appendChild(i);\n\t\t\t\t\tpar = i;\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\tpar = par.getElementsByTagName('UL')[0];\n\t\t\t\t}\n\n\t\t\t\tif(ind < par.childNodes.length) {\n\t\t\t\t\tpar.insertBefore(node, par.childNodes[ind]);\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\tpar.appendChild(node);\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn node;\n\t\t},"}, {"tags": [{"type": "name", "string": "open_node(obj [, callback, animation])"}, {"type": "param", "types": ["mixed"], "name": "obj", "description": "the node to open"}, {"type": "param", "types": ["Function"], "name": "callback", "description": "a function to execute once the node is opened"}, {"type": "param", "types": ["Number"], "name": "animation", "description": "the animation duration in milliseconds when opening the node (overrides the `core.animation` setting). Use `false` for no animation."}, {"type": "trigger", "string": "open_node.jstree, after_open.jstree"}], "description": {"full": "<p>opens a node, revaling its children. If the node is not loaded it will be loaded and opened once ready.</p>", "summary": "<p>opens a node, revaling its children. If the node is not loaded it will be loaded and opened once ready.</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "open_node : function (obj, callback, animation) {\n\t\t\tvar t1, t2, d, t;\n\t\t\tif($.isArray(obj)) {\n\t\t\t\tobj = obj.slice();\n\t\t\t\tfor(t1 = 0, t2 = obj.length; t1 < t2; t1++) {\n\t\t\t\t\tthis.open_node(obj[t1], callback, animation);\n\t\t\t\t}\n\t\t\t\treturn true;\n\t\t\t}\n\t\t\tobj = this.get_node(obj);\n\t\t\tif(!obj || obj.id === '#') {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tanimation = animation === undefined ? this.settings.core.animation : animation;\n\t\t\tif(!this.is_closed(obj)) {\n\t\t\t\tif(callback) {\n\t\t\t\t\tcallback.call(this, obj, false);\n\t\t\t\t}\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tif(!this.is_loaded(obj)) {\n\t\t\t\tif(this.is_loading(obj)) {\n\t\t\t\t\treturn setTimeout($.proxy(function () {\n\t\t\t\t\t\tthis.open_node(obj, callback, animation);\n\t\t\t\t\t}, this), 500);\n\t\t\t\t}\n\t\t\t\tthis.load_node(obj, function (o, ok) {\n\t\t\t\t\treturn ok ? this.open_node(o, callback, animation) : (callback ? callback.call(this, o, false) : false);\n\t\t\t\t});\n\t\t\t}\n\t\t\telse {\n\t\t\t\td = this.get_node(obj, true);\n\t\t\t\tt = this;\n\t\t\t\tif(d.length) {\n\t\t\t\t\tif(obj.children.length && !this._firstChild(d.children('ul')[0])) {\n\t\t\t\t\t\tobj.state.opened = true;\n\t\t\t\t\t\tthis.redraw_node(obj, true);\n\t\t\t\t\t\td = this.get_node(obj, true);\n\t\t\t\t\t}\n\t\t\t\t\tif(!animation) {\n\t\t\t\t\t\td[0].className = d[0].className.replace('jstree-closed', 'jstree-open');\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\td\n\t\t\t\t\t\t\t.children(\"ul\").css(\"display\",\"none\").end()\n\t\t\t\t\t\t\t.removeClass(\"jstree-closed\").addClass(\"jstree-open\")\n\t\t\t\t\t\t\t.children(\"ul\").stop(true, true)\n\t\t\t\t\t\t\t\t.slideDown(animation, function () {\n\t\t\t\t\t\t\t\t\tthis.style.display = \"\";\n\t\t\t\t\t\t\t\t\tt.trigger(\"after_open\", { \"node\" : obj });\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tobj.state.opened = true;\n\t\t\t\tif(callback) {\n\t\t\t\t\tcallback.call(this, obj, true);\n\t\t\t\t}"}, {"tags": [{"type": "event", "string": ""}, {"type": "name", "string": "open_node.jstree"}, {"type": "param", "types": ["Object"], "name": "node", "description": "the opened node"}], "description": {"full": "<p>triggered when a node is opened (if there is an animation it will not be completed yet)</p>", "summary": "<p>triggered when a node is opened (if there is an animation it will not be completed yet)</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "this.trigger('open_node', { \"node\" : obj });\n\t\t\t\tif(!animation || !d.length) {"}, {"tags": [{"type": "event", "string": ""}, {"type": "name", "string": "after_open.jstree"}, {"type": "param", "types": ["Object"], "name": "node", "description": "the opened node"}], "description": {"full": "<p>triggered when a node is opened and the animation is complete</p>", "summary": "<p>triggered when a node is opened and the animation is complete</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "this.trigger(\"after_open\", { \"node\" : obj });\n\t\t\t\t}\n\t\t\t}\n\t\t},"}, {"tags": [{"type": "name", "string": "_open_to(obj)"}, {"type": "param", "types": ["mixed"], "name": "obj", "description": "the node to reveal"}, {"type": "private", "string": ""}], "description": {"full": "<p>opens every parent of a node (node should be loaded)</p>", "summary": "<p>opens every parent of a node (node should be loaded)</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "_open_to : function (obj) {\n\t\t\tobj = this.get_node(obj);\n\t\t\tif(!obj || obj.id === '#') {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tvar i, j, p = obj.parents;\n\t\t\tfor(i = 0, j = p.length; i < j; i+=1) {\n\t\t\t\tif(i !== '#') {\n\t\t\t\t\tthis.open_node(p[i], false, 0);\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn $(document.getElementById(obj.id));\n\t\t},"}, {"tags": [{"type": "name", "string": "close_node(obj [, animation])"}, {"type": "param", "types": ["mixed"], "name": "obj", "description": "the node to close"}, {"type": "param", "types": ["Number"], "name": "animation", "description": "the animation duration in milliseconds when closing the node (overrides the `core.animation` setting). Use `false` for no animation."}, {"type": "trigger", "string": "close_node.jstree, after_close.jstree"}], "description": {"full": "<p>closes a node, hiding its children</p>", "summary": "<p>closes a node, hiding its children</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "close_node : function (obj, animation) {\n\t\t\tvar t1, t2, t, d;\n\t\t\tif($.isArray(obj)) {\n\t\t\t\tobj = obj.slice();\n\t\t\t\tfor(t1 = 0, t2 = obj.length; t1 < t2; t1++) {\n\t\t\t\t\tthis.close_node(obj[t1], animation);\n\t\t\t\t}\n\t\t\t\treturn true;\n\t\t\t}\n\t\t\tobj = this.get_node(obj);\n\t\t\tif(!obj || obj.id === '#') {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tanimation = animation === undefined ? this.settings.core.animation : animation;\n\t\t\tt = this;\n\t\t\td = this.get_node(obj, true);\n\t\t\tif(d.length) {\n\t\t\t\tif(!animation) {\n\t\t\t\t\td[0].className = d[0].className.replace('jstree-open', 'jstree-closed');\n\t\t\t\t\td.children('ul').remove();\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\td\n\t\t\t\t\t\t.children(\"ul\").attr(\"style\",\"display:block !important\").end()\n\t\t\t\t\t\t.removeClass(\"jstree-open\").addClass(\"jstree-closed\")\n\t\t\t\t\t\t.children(\"ul\").stop(true, true).slideUp(animation, function () {\n\t\t\t\t\t\t\tthis.style.display = \"\";\n\t\t\t\t\t\t\td.children('ul').remove();\n\t\t\t\t\t\t\tt.trigger(\"after_close\", { \"node\" : obj });\n\t\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t}\n\t\t\tobj.state.opened = false;"}, {"tags": [{"type": "event", "string": ""}, {"type": "name", "string": "close_node.jstree"}, {"type": "param", "types": ["Object"], "name": "node", "description": "the closed node"}], "description": {"full": "<p>triggered when a node is closed (if there is an animation it will not be complete yet)</p>", "summary": "<p>triggered when a node is closed (if there is an animation it will not be complete yet)</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "this.trigger('close_node',{ \"node\" : obj });\n\t\t\tif(!animation || !d.length) {"}, {"tags": [{"type": "event", "string": ""}, {"type": "name", "string": "after_close.jstree"}, {"type": "param", "types": ["Object"], "name": "node", "description": "the closed node"}], "description": {"full": "<p>triggered when a node is closed and the animation is complete</p>", "summary": "<p>triggered when a node is closed and the animation is complete</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "this.trigger(\"after_close\", { \"node\" : obj });\n\t\t\t}\n\t\t},"}, {"tags": [{"type": "name", "string": "toggle_node(obj)"}, {"type": "param", "types": ["mixed"], "name": "obj", "description": "the node to toggle"}], "description": {"full": "<p>toggles a node - closing it if it is open, opening it if it is closed</p>", "summary": "<p>toggles a node - closing it if it is open, opening it if it is closed</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "toggle_node : function (obj) {\n\t\t\tvar t1, t2;\n\t\t\tif($.isArray(obj)) {\n\t\t\t\tobj = obj.slice();\n\t\t\t\tfor(t1 = 0, t2 = obj.length; t1 < t2; t1++) {\n\t\t\t\t\tthis.toggle_node(obj[t1]);\n\t\t\t\t}\n\t\t\t\treturn true;\n\t\t\t}\n\t\t\tif(this.is_closed(obj)) {\n\t\t\t\treturn this.open_node(obj);\n\t\t\t}\n\t\t\tif(this.is_open(obj)) {\n\t\t\t\treturn this.close_node(obj);\n\t\t\t}\n\t\t},"}, {"tags": [{"type": "name", "string": "open_all([obj, animation, original_obj])"}, {"type": "param", "types": ["mixed"], "name": "obj", "description": "the node to open recursively, omit to open all nodes in the tree"}, {"type": "param", "types": ["Number"], "name": "animation", "description": "the animation duration in milliseconds when opening the nodes, the default is no animation"}, {"type": "param", "types": ["j<PERSON><PERSON><PERSON>"], "name": "reference", "description": "to the node that started the process (internal use)"}, {"type": "trigger", "string": "open_all.jstree"}], "description": {"full": "<p>opens all nodes within a node (or the tree), revaling their children. If the node is not loaded it will be loaded and opened once ready.</p>", "summary": "<p>opens all nodes within a node (or the tree), revaling their children. If the node is not loaded it will be loaded and opened once ready.</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "open_all : function (obj, animation, original_obj) {\n\t\t\tif(!obj) { obj = '#'; }\n\t\t\tobj = this.get_node(obj);\n\t\t\tif(!obj) { return false; }\n\t\t\tvar dom = obj.id === '#' ? this.get_container_ul() : this.get_node(obj, true), i, j, _this;\n\t\t\tif(!dom.length) {\n\t\t\t\tfor(i = 0, j = obj.children_d.length; i < j; i++) {\n\t\t\t\t\tif(this.is_closed(this._mode.data[obj.children_d[i]])) {\n\t\t\t\t\t\tthis._mode.data[obj.children_d[i]].state.opened = true;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn this.trigger('open_all', { \"node\" : obj });\n\t\t\t}\n\t\t\toriginal_obj = original_obj || dom;\n\t\t\t_this = this;\n\t\t\tdom = this.is_closed(obj) ? dom.find('li.jstree-closed').addBack() : dom.find('li.jstree-closed');\n\t\t\tdom.each(function () {\n\t\t\t\t_this.open_node(\n\t\t\t\t\tthis,\n\t\t\t\t\tfunction(node, status) { if(status && this.is_parent(node)) { this.open_all(node, animation, original_obj); } },\n\t\t\t\t\tanimation || 0\n\t\t\t\t);\n\t\t\t});\n\t\t\tif(original_obj.find('li.jstree-closed').length === 0) {"}, {"tags": [{"type": "event", "string": ""}, {"type": "name", "string": "open_all.jstree"}, {"type": "param", "types": ["Object"], "name": "node", "description": "the opened node"}], "description": {"full": "<p>triggered when an <code>open_all</code> call completes</p>", "summary": "<p>triggered when an <code>open_all</code> call completes</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "this.trigger('open_all', { \"node\" : this.get_node(original_obj) });\n\t\t\t}\n\t\t},"}, {"tags": [{"type": "name", "string": "close_all([obj, animation])"}, {"type": "param", "types": ["mixed"], "name": "obj", "description": "the node to close recursively, omit to close all nodes in the tree"}, {"type": "param", "types": ["Number"], "name": "animation", "description": "the animation duration in milliseconds when closing the nodes, the default is no animation"}, {"type": "trigger", "string": "close_all.jstree"}], "description": {"full": "<p>closes all nodes within a node (or the tree), revaling their children</p>", "summary": "<p>closes all nodes within a node (or the tree), revaling their children</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "close_all : function (obj, animation) {\n\t\t\tif(!obj) { obj = '#'; }\n\t\t\tobj = this.get_node(obj);\n\t\t\tif(!obj) { return false; }\n\t\t\tvar dom = obj.id === '#' ? this.get_container_ul() : this.get_node(obj, true),\n\t\t\t\t_this = this, i, j;\n\t\t\tif(!dom.length) {\n\t\t\t\tfor(i = 0, j = obj.children_d.length; i < j; i++) {\n\t\t\t\t\tthis._mode.data[obj.children_d[i]].state.opened = false;\n\t\t\t\t}\n\t\t\t\treturn this.trigger('close_all', { \"node\" : obj });\n\t\t\t}\n\t\t\tdom = this.is_open(obj) ? dom.find('li.jstree-open').addBack() : dom.find('li.jstree-open');\n\t\t\tdom.vakata_reverse().each(function () { _this.close_node(this, animation || 0); });"}, {"tags": [{"type": "event", "string": ""}, {"type": "name", "string": "close_all.jstree"}, {"type": "param", "types": ["Object"], "name": "node", "description": "the closed node"}], "description": {"full": "<p>triggered when an <code>close_all</code> call completes</p>", "summary": "<p>triggered when an <code>close_all</code> call completes</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "this.trigger('close_all', { \"node\" : obj });\n\t\t},"}, {"tags": [{"type": "name", "string": "is_disabled(obj)"}, {"type": "param", "types": ["mixed"], "name": "obj", "description": ""}, {"type": "return", "types": ["Boolean"], "description": ""}], "description": {"full": "<p>checks if a node is disabled (not selectable)</p>", "summary": "<p>checks if a node is disabled (not selectable)</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "is_disabled : function (obj) {\n\t\t\tobj = this.get_node(obj);\n\t\t\treturn obj && obj.state && obj.state.disabled;\n\t\t},"}, {"tags": [{"type": "name", "string": "enable_node(obj)"}, {"type": "param", "types": ["mixed"], "name": "obj", "description": "the node to enable"}, {"type": "trigger", "string": "enable_node.jstree"}], "description": {"full": "<p>enables a node - so that it can be selected</p>", "summary": "<p>enables a node - so that it can be selected</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "enable_node : function (obj) {\n\t\t\tvar t1, t2;\n\t\t\tif($.isArray(obj)) {\n\t\t\t\tobj = obj.slice();\n\t\t\t\tfor(t1 = 0, t2 = obj.length; t1 < t2; t1++) {\n\t\t\t\t\tthis.enable_node(obj[t1]);\n\t\t\t\t}\n\t\t\t\treturn true;\n\t\t\t}\n\t\t\tobj = this.get_node(obj);\n\t\t\tif(!obj || obj.id === '#') {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tobj.state.disabled = false;\n\t\t\tthis.get_node(obj,true).children('.jstree-anchor').removeClass('jstree-disabled');"}, {"tags": [{"type": "event", "string": ""}, {"type": "name", "string": "enable_node.jstree"}, {"type": "param", "types": ["Object"], "name": "node", "description": "the enabled node"}], "description": {"full": "<p>triggered when an node is enabled</p>", "summary": "<p>triggered when an node is enabled</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "this.trigger('enable_node', { 'node' : obj });\n\t\t},"}, {"tags": [{"type": "name", "string": "disable_node(obj)"}, {"type": "param", "types": ["mixed"], "name": "obj", "description": "the node to disable"}, {"type": "trigger", "string": "disable_node.jstree"}], "description": {"full": "<p>disables a node - so that it can not be selected</p>", "summary": "<p>disables a node - so that it can not be selected</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "disable_node : function (obj) {\n\t\t\tvar t1, t2;\n\t\t\tif($.isArray(obj)) {\n\t\t\t\tobj = obj.slice();\n\t\t\t\tfor(t1 = 0, t2 = obj.length; t1 < t2; t1++) {\n\t\t\t\t\tthis.disable_node(obj[t1]);\n\t\t\t\t}\n\t\t\t\treturn true;\n\t\t\t}\n\t\t\tobj = this.get_node(obj);\n\t\t\tif(!obj || obj.id === '#') {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tobj.state.disabled = true;\n\t\t\tthis.get_node(obj,true).children('.jstree-anchor').addClass('jstree-disabled');"}, {"tags": [{"type": "event", "string": ""}, {"type": "name", "string": "disable_node.jstree"}, {"type": "param", "types": ["Object"], "name": "node", "description": "the disabled node"}], "description": {"full": "<p>triggered when an node is disabled</p>", "summary": "<p>triggered when an node is disabled</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "this.trigger('disable_node', { 'node' : obj });\n\t\t},"}, {"tags": [{"type": "private", "string": ""}, {"type": "name", "string": "activate_node(obj, e)"}, {"type": "param", "types": ["mixed"], "name": "obj", "description": "the node"}, {"type": "param", "types": ["Object"], "name": "e", "description": "the related event"}, {"type": "trigger", "string": "activate_node.jstree"}], "description": {"full": "<p>called when a node is selected by the user. Used internally.</p>", "summary": "<p>called when a node is selected by the user. Used internally.</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "activate_node : function (obj, e) {\n\t\t\tif(this.is_disabled(obj)) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tif(!this.settings.core.multiple || (!e.metaKey && !e.ctrlKey && !e.shiftKey) || (e.shiftKey && (!this._data.core.last_clicked || !this.get_parent(obj) || this.get_parent(obj) !== this._data.core.last_clicked.parent ) )) {\n\t\t\t\tthis.deselect_all(true);\n\t\t\t\tthis.select_node(obj);\n\t\t\t\tthis._data.core.last_clicked = this.get_node(obj);\n\t\t\t}\n\t\t\telse {\n\t\t\t\tif(e.shiftKey) {\n\t\t\t\t\tvar o = this.get_node(obj).id,\n\t\t\t\t\t\tl = this._data.core.last_clicked.id,\n\t\t\t\t\t\tp = this.get_node(this._data.core.last_clicked.parent).children,\n\t\t\t\t\t\tc = false,\n\t\t\t\t\t\ti, j;\n\t\t\t\t\tfor(i = 0, j = p.length; i < j; i += 1) {\n\t\t\t\t\t\t// separate IFs work whem o and l are the same\n\t\t\t\t\t\tif(p[i] === o) {\n\t\t\t\t\t\t\tc = !c;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif(p[i] === l) {\n\t\t\t\t\t\t\tc = !c;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif(c || p[i] === o || p[i] === l) {\n\t\t\t\t\t\t\tthis.select_node(p[i]);\n\t\t\t\t\t\t}\n\t\t\t\t\t\telse {\n\t\t\t\t\t\t\tthis.deselect_node(p[i]);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\tif(!this.is_selected(obj)) {\n\t\t\t\t\t\tthis.select_node(obj);\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\tthis.deselect_node(obj);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}"}, {"tags": [{"type": "event", "string": ""}, {"type": "name", "string": "activate_node.jstree"}, {"type": "param", "types": ["Object"], "name": "node", "description": ""}], "description": {"full": "<p>triggered when an node is clicked or intercated with by the user</p>", "summary": "<p>triggered when an node is clicked or intercated with by the user</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "this.trigger('activate_node', { 'node' : this.get_node(obj) });\n\t\t},"}, {"tags": [{"type": "private", "string": ""}, {"type": "name", "string": "hover_node(obj)"}, {"type": "param", "types": ["mixed"], "name": "obj", "description": ""}, {"type": "trigger", "string": "hover_node.jstree"}], "description": {"full": "<p>applies the hover state on a node, called when a node is hovered by the user. Used internally.</p>", "summary": "<p>applies the hover state on a node, called when a node is hovered by the user. Used internally.</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "hover_node : function (obj) {\n\t\t\tobj = this.get_node(obj, true);\n\t\t\tif(!obj || !obj.length) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tobj.children('.jstree-anchor').addClass('jstree-hovered');"}, {"tags": [{"type": "event", "string": ""}, {"type": "name", "string": "hover_node.jstree"}, {"type": "param", "types": ["Object"], "name": "node", "description": ""}], "description": {"full": "<p>triggered when an node is hovered</p>", "summary": "<p>triggered when an node is hovered</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "this.trigger('hover_node', { 'node' : this.get_node(obj) });\n\t\t},"}, {"tags": [{"type": "private", "string": ""}, {"type": "name", "string": "dehover_node(obj)"}, {"type": "param", "types": ["mixed"], "name": "obj", "description": ""}, {"type": "trigger", "string": "dehover_node.jstree"}], "description": {"full": "<p>removes the hover state from a nodecalled when a node is no longer hovered by the user. Used internally.</p>", "summary": "<p>removes the hover state from a nodecalled when a node is no longer hovered by the user. Used internally.</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "dehover_node : function (obj) {\n\t\t\tobj = this.get_node(obj, true);\n\t\t\tif(!obj || !obj.length) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tobj.children('.jstree-anchor').removeClass('jstree-hovered');"}, {"tags": [{"type": "event", "string": ""}, {"type": "name", "string": "dehover_node.jstree"}, {"type": "param", "types": ["Object"], "name": "node", "description": ""}], "description": {"full": "<p>triggered when an node is no longer hovered</p>", "summary": "<p>triggered when an node is no longer hovered</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "this.trigger('dehover_node', { 'node' : this.get_node(obj) });\n\t\t},"}, {"tags": [{"type": "name", "string": "select_node(obj [, supress_event, prevent_open])"}, {"type": "param", "types": ["mixed"], "name": "obj", "description": "an array can be used to select multiple nodes"}, {"type": "param", "types": ["Boolean"], "name": "supress_event", "description": "if set to `true` the `changed.jstree` event won't be triggered"}, {"type": "param", "types": ["Boolean"], "name": "prevent_open", "description": "if set to `true` parents of the selected node won't be opened"}, {"type": "trigger", "string": "select_node.jstree, changed.jstree"}], "description": {"full": "<p>select a node</p>", "summary": "<p>select a node</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "select_node : function (obj, supress_event, prevent_open) {\n\t\t\tvar dom, t1, t2, th;\n\t\t\tif($.isArray(obj)) {\n\t\t\t\tobj = obj.slice();\n\t\t\t\tfor(t1 = 0, t2 = obj.length; t1 < t2; t1++) {\n\t\t\t\t\tthis.select_node(obj[t1], supress_event, prevent_open);\n\t\t\t\t}\n\t\t\t\treturn true;\n\t\t\t}\n\t\t\tobj = this.get_node(obj);\n\t\t\tif(!obj || obj.id === '#') {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tdom = this.get_node(obj, true);\n\t\t\tif(!obj.state.selected) {\n\t\t\t\tobj.state.selected = true;\n\t\t\t\tthis._data.core.selected.push(obj.id);\n\t\t\t\tif(!prevent_open) {\n\t\t\t\t\tdom = this._open_to(obj);\n\t\t\t\t}\n\t\t\t\tif(dom && dom.length) {\n\t\t\t\t\tdom.children('.jstree-anchor').addClass('jstree-clicked');\n\t\t\t\t}"}, {"tags": [{"type": "event", "string": ""}, {"type": "name", "string": "select_node.jstree"}, {"type": "param", "types": ["Object"], "name": "node", "description": ""}, {"type": "param", "types": ["Array"], "name": "selected", "description": "the current selection"}], "description": {"full": "<p>triggered when an node is selected</p>", "summary": "<p>triggered when an node is selected</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "this.trigger('select_node', { 'node' : obj, 'selected' : this._data.core.selected });\n\t\t\t\tif(!supress_event) {"}, {"tags": [{"type": "event", "string": ""}, {"type": "name", "string": "changed.jstree"}, {"type": "param", "types": ["Object"], "name": "node", "description": ""}, {"type": "param", "types": ["Object"], "name": "action", "description": "the action that caused the selection to change"}, {"type": "param", "types": ["Array"], "name": "selected", "description": "the current selection"}], "description": {"full": "<p>triggered when selection changes</p>", "summary": "<p>triggered when selection changes</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "this.trigger('changed', { 'action' : 'select_node', 'node' : obj, 'selected' : this._data.core.selected });\n\t\t\t\t}\n\t\t\t}\n\t\t},"}, {"tags": [{"type": "name", "string": "deselect_node(obj [, supress_event])"}, {"type": "param", "types": ["mixed"], "name": "obj", "description": "an array can be used to deselect multiple nodes"}, {"type": "param", "types": ["Boolean"], "name": "supress_event", "description": "if set to `true` the `changed.jstree` event won't be triggered"}, {"type": "trigger", "string": "deselect_node.jstree, changed.jstree"}], "description": {"full": "<p>deselect a node</p>", "summary": "<p>deselect a node</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "deselect_node : function (obj, supress_event) {\n\t\t\tvar t1, t2, dom;\n\t\t\tif($.isArray(obj)) {\n\t\t\t\tobj = obj.slice();\n\t\t\t\tfor(t1 = 0, t2 = obj.length; t1 < t2; t1++) {\n\t\t\t\t\tthis.deselect_node(obj[t1], supress_event);\n\t\t\t\t}\n\t\t\t\treturn true;\n\t\t\t}\n\t\t\tobj = this.get_node(obj);\n\t\t\tif(!obj || obj.id === '#') {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tdom = this.get_node(obj, true);\n\t\t\tif(obj.state.selected) {\n\t\t\t\tobj.state.selected = false;\n\t\t\t\tthis._data.core.selected = $.vakata.array_remove_item(this._data.core.selected, obj.id);\n\t\t\t\tif(dom.length) {\n\t\t\t\t\tdom.children('.jstree-anchor').removeClass('jstree-clicked');\n\t\t\t\t}"}, {"tags": [{"type": "event", "string": ""}, {"type": "name", "string": "deselect_node.jstree"}, {"type": "param", "types": ["Object"], "name": "node", "description": ""}, {"type": "param", "types": ["Array"], "name": "selected", "description": "the current selection"}], "description": {"full": "<p>triggered when an node is deselected</p>", "summary": "<p>triggered when an node is deselected</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "this.trigger('deselect_node', { 'node' : obj, 'selected' : this._data.core.selected });\n\t\t\t\tif(!supress_event) {\n\t\t\t\t\tthis.trigger('changed', { 'action' : 'deselect_node', 'node' : obj, 'selected' : this._data.core.selected });\n\t\t\t\t}\n\t\t\t}\n\t\t},"}, {"tags": [{"type": "name", "string": "select_all([supress_event])"}, {"type": "param", "types": ["Boolean"], "name": "supress_event", "description": "if set to `true` the `changed.jstree` event won't be triggered"}, {"type": "trigger", "string": "select_all.jstree, changed.jstree"}], "description": {"full": "<p>select all nodes in the tree</p>", "summary": "<p>select all nodes in the tree</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "select_all : function (supress_event) {\n\t\t\tvar tmp = this._data.core.selected.concat([]), i, j;\n\t\t\tthis._data.core.selected = this._model.data['#'].children_d.concat();\n\t\t\tfor(i = 0, j = this._data.core.selected.length; i < j; i++) {\n\t\t\t\tif(this._model.data[this._data.core.selected[i]]) {\n\t\t\t\t\tthis._model.data[this._data.core.selected[i]].state.selected = true;\n\t\t\t\t}\n\t\t\t}\n\t\t\tthis.redraw(true);"}, {"tags": [{"type": "event", "string": ""}, {"type": "name", "string": "select_all.jstree"}, {"type": "param", "types": ["Array"], "name": "selected", "description": "the current selection"}], "description": {"full": "<p>triggered when all nodes are selected</p>", "summary": "<p>triggered when all nodes are selected</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "this.trigger('select_all', { 'selected' : this._data.core.selected });\n\t\t\tif(!supress_event) {\n\t\t\t\tthis.trigger('changed', { 'action' : 'select_all', 'selected' : this._data.core.selected, 'old_selection' : tmp });\n\t\t\t}\n\t\t},"}, {"tags": [{"type": "name", "string": "deselect_all([supress_event])"}, {"type": "param", "types": ["Boolean"], "name": "supress_event", "description": "if set to `true` the `changed.jstree` event won't be triggered"}, {"type": "trigger", "string": "deselect_all.jstree, changed.jstree"}], "description": {"full": "<p>deselect all selected nodes</p>", "summary": "<p>deselect all selected nodes</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "deselect_all : function (supress_event) {\n\t\t\tvar tmp = this._data.core.selected.concat([]), i, j;\n\t\t\tfor(i = 0, j = this._data.core.selected.length; i < j; i++) {\n\t\t\t\tif(this._model.data[this._data.core.selected[i]]) {\n\t\t\t\t\tthis._model.data[this._data.core.selected[i]].state.selected = false;\n\t\t\t\t}\n\t\t\t}\n\t\t\tthis._data.core.selected = [];\n\t\t\tthis.element.find('.jstree-clicked').removeClass('jstree-clicked');"}, {"tags": [{"type": "event", "string": ""}, {"type": "name", "string": "deselect_all.jstree"}, {"type": "param", "types": ["Object"], "name": "node", "description": "the previous selection"}, {"type": "param", "types": ["Array"], "name": "selected", "description": "the current selection"}], "description": {"full": "<p>triggered when all nodes are deselected</p>", "summary": "<p>triggered when all nodes are deselected</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "this.trigger('deselect_all', { 'selected' : this._data.core.selected, 'node' : tmp });\n\t\t\tif(!supress_event) {\n\t\t\t\tthis.trigger('changed', { 'action' : 'deselect_all', 'selected' : this._data.core.selected, 'old_selection' : tmp });\n\t\t\t}\n\t\t},"}, {"tags": [{"type": "name", "string": "is_selected(obj)"}, {"type": "param", "types": ["mixed"], "name": "obj", "description": ""}, {"type": "return", "types": ["Boolean"], "description": ""}], "description": {"full": "<p>checks if a node is selected</p>", "summary": "<p>checks if a node is selected</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "is_selected : function (obj) {\n\t\t\tobj = this.get_node(obj);\n\t\t\tif(!obj || obj.id === '#') {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\treturn obj.state.selected;\n\t\t},"}, {"tags": [{"type": "name", "string": "get_selected([full])"}, {"type": "param", "types": ["mixed"], "name": "full", "description": "if set to `true` the returned array will consist of the full node objects, otherwise - only IDs will be returned"}, {"type": "return", "types": ["Array"], "description": ""}], "description": {"full": "<p>get an array of all selected node IDs</p>", "summary": "<p>get an array of all selected node IDs</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "get_selected : function (full) {\n\t\t\treturn full ? $.map(this._data.core.selected, $.proxy(function (i) { return this.get_node(i); }, this)) : this._data.core.selected;\n\t\t},"}, {"tags": [{"type": "name", "string": "get_state()"}, {"type": "private", "string": ""}, {"type": "return", "types": ["Object"], "description": ""}], "description": {"full": "<p>gets the current state of the tree so that it can be restored later with <code>set_state(state)</code>. Used internally.</p>", "summary": "<p>gets the current state of the tree so that it can be restored later with <code>set_state(state)</code>. Used internally.</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "get_state : function () {\n\t\t\tvar state\t= {\n\t\t\t\t'core' : {\n\t\t\t\t\t'open' : [],\n\t\t\t\t\t'scroll' : {\n\t\t\t\t\t\t'left' : this.element.scrollLeft(),\n\t\t\t\t\t\t'top' : this.element.scrollTop()\n\t\t\t\t\t},"}, {"tags": [], "description": {"full": "<p>'themes' : {<br />                        'name' : this.get_theme(),<br />                        'icons' : this.<em>data.core.themes.icons,<br />                        'dots' : this.</em>data.core.themes.dots<br />                    },</p>", "summary": "<p>'themes' : {<br />                        'name' : this.get_theme(),<br />                        'icons' : this.<em>data.core.themes.icons,<br />                        'dots' : this.</em>data.core.themes.dots<br />                    },</p>", "body": ""}, "ignore": true, "code": "'selected' : []\n\t\t\t\t}\n\t\t\t}, i;\n\t\t\tfor(i in this._model.data) {\n\t\t\t\tif(this._model.data.hasOwnProperty(i)) {\n\t\t\t\t\tif(i !== '#') {\n\t\t\t\t\t\tif(this._model.data[i].state.opened) {\n\t\t\t\t\t\t\tstate.core.open.push(i);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif(this._model.data[i].state.selected) {\n\t\t\t\t\t\t\tstate.core.selected.push(i);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn state;\n\t\t},"}, {"tags": [{"type": "name", "string": "set_state(state [, callback])"}, {"type": "private", "string": ""}, {"type": "param", "types": ["Object"], "name": "state", "description": "the state to restore"}, {"type": "param", "types": ["Function"], "name": "callback", "description": "an optional function to execute once the state is restored."}, {"type": "trigger", "string": "set_state.jstree"}], "description": {"full": "<p>sets the state of the tree. Used internally.</p>", "summary": "<p>sets the state of the tree. Used internally.</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "set_state : function (state, callback) {\n\t\t\tif(state) {\n\t\t\t\tif(state.core) {\n\t\t\t\t\tvar res, n, t, _this;\n\t\t\t\t\tif($.isArray(state.core.open)) {\n\t\t\t\t\t\tres = true;\n\t\t\t\t\t\tn = false;\n\t\t\t\t\t\tt = this;\n\t\t\t\t\t\t$.each(state.core.open.concat([]), function (i, v) {\n\t\t\t\t\t\t\tn = document.getElementById(v);\n\t\t\t\t\t\t\tif(n) {\n\t\t\t\t\t\t\t\tif(t.is_loaded(v)) {\n\t\t\t\t\t\t\t\t\tif(t.is_closed(v)) {\n\t\t\t\t\t\t\t\t\t\tt.open_node(v, false, 0);\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t$.vakata.array_remove_item(state.core.open, v);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\telse {\n\t\t\t\t\t\t\t\t\tif(!t.is_loading(v)) {\n\t\t\t\t\t\t\t\t\t\tt.open_node(v, $.proxy(function () { this.set_state(state); }, t), 0);\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t// there will be some async activity - so wait for it\n\t\t\t\t\t\t\t\t\tres = false;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t\tif(res) {\n\t\t\t\t\t\t\tdelete state.core.open;\n\t\t\t\t\t\t\tthis.set_state(state, callback);\n\t\t\t\t\t\t}\n\t\t\t\t\t\treturn false;\n\t\t\t\t\t}\n\t\t\t\t\tif(state.core.scroll) {\n\t\t\t\t\t\tif(state.core.scroll && state.core.scroll.left !== undefined) {\n\t\t\t\t\t\t\tthis.element.scrollLeft(state.core.scroll.left);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif(state.core.scroll && state.core.scroll.top !== undefined) {\n\t\t\t\t\t\t\tthis.element.scrollTop(state.core.scroll.top);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tdelete state.core.scroll;\n\t\t\t\t\t\tdelete state.core.open;\n\t\t\t\t\t\tthis.set_state(state, callback);\n\t\t\t\t\t\treturn false;\n\t\t\t\t\t}"}, {"tags": [], "description": {"full": "<p>if(state.core.themes) {<br />                        if(state.core.themes.name) {<br />                            this.set_theme(state.core.themes.name);<br />                        }<br />                        if(typeof state.core.themes.dots !== 'undefined') {<br />                            this<a href=\"\"> state.core.themes.dots ? \"show_dots\" : \"hide_dots\" </a>;<br />                        }<br />                        if(typeof state.core.themes.icons !== 'undefined') {<br />                            this<a href=\"\"> state.core.themes.icons ? \"show_icons\" : \"hide_icons\" </a>;<br />                        }<br />                        delete state.core.themes;<br />                        delete state.core.open;<br />                        this.set_state(state, callback);<br />                        return false;<br />                    }</p>", "summary": "<p>if(state.core.themes) {<br />                        if(state.core.themes.name) {<br />                            this.set_theme(state.core.themes.name);<br />                        }<br />                        if(typeof state.core.themes.dots !== 'undefined') {<br />                            this<a href=\"\"> state.core.themes.dots ? \"show_dots\" : \"hide_dots\" </a>;<br />                        }<br />                        if(typeof state.core.themes.icons !== 'undefined') {<br />                            this<a href=\"\"> state.core.themes.icons ? \"show_icons\" : \"hide_icons\" </a>;<br />                        }<br />                        delete state.core.themes;<br />                        delete state.core.open;<br />                        this.set_state(state, callback);<br />                        return false;<br />                    }</p>", "body": ""}, "ignore": true, "code": "if(state.core.selected) {\n\t\t\t\t\t\t_this = this;\n\t\t\t\t\t\tthis.deselect_all();\n\t\t\t\t\t\t$.each(state.core.selected, function (i, v) {\n\t\t\t\t\t\t\t_this.select_node(v);\n\t\t\t\t\t\t});\n\t\t\t\t\t\tdelete state.core.selected;\n\t\t\t\t\t\tthis.set_state(state, callback);\n\t\t\t\t\t\treturn false;\n\t\t\t\t\t}\n\t\t\t\t\tif($.isEmptyObject(state)) {\n\t\t\t\t\t\tif(callback) { callback.call(this); }"}, {"tags": [{"type": "event", "string": ""}, {"type": "name", "string": "set_state.jstree"}], "description": {"full": "<p>triggered when a <code>set_state</code> call completes</p>", "summary": "<p>triggered when a <code>set_state</code> call completes</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "this.trigger('set_state');\n\t\t\t\t\t\treturn false;\n\t\t\t\t\t}\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\t\t\t\treturn true;\n\t\t\t}\n\t\t\treturn false;\n\t\t},"}, {"tags": [{"type": "name", "string": "refresh()"}, {"type": "trigger", "string": "refresh.jstree"}], "description": {"full": "<p>refreshes the tree - all nodes are reloaded with calls to <code>load_node</code>.</p>", "summary": "<p>refreshes the tree - all nodes are reloaded with calls to <code>load_node</code>.</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "refresh : function () {\n\t\t\tthis._data.core.state = this.get_state();\n\t\t\tthis.load_node('#', function (o, s) {\n\t\t\t\tif(s) {\n\t\t\t\t\tthis.set_state($.extend(true, {}, this._data.core.state), function () {"}, {"tags": [{"type": "event", "string": ""}, {"type": "name", "string": "refresh.jstree"}], "description": {"full": "<p>triggered when a <code>refresh</code> call completes</p>", "summary": "<p>triggered when a <code>refresh</code> call completes</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "this.trigger('refresh');\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\tthis._data.core.state = null;\n\t\t\t});\n\t\t},"}, {"tags": [{"type": "name", "string": "set_id(obj, id)"}, {"type": "param", "types": ["mixed"], "name": "obj", "description": "the node"}, {"type": "param", "types": ["String"], "name": "id", "description": "the new ID"}, {"type": "return", "types": ["Boolean"], "description": ""}], "description": {"full": "<p>set (change) the ID of a node</p>", "summary": "<p>set (change) the ID of a node</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "set_id : function (obj, id) {\n\t\t\tobj = this.get_node(obj);\n\t\t\tif(!obj || obj.id === '#') { return false; }\n\t\t\tvar i, j, m = this._model.data;\n\t\t\t// update parents (replace current ID with new one in children and children_d)\n\t\t\tm[obj.parent].children[$.inArray(obj.id, m[obj.parent].children)] = id;\n\t\t\tfor(i = 0, j = obj.parents.length; i < j; i++) {\n\t\t\t\tm[obj.parents[i]].children_d[$.inArray(obj.id, m[obj.parents[i]].children_d)] = id;\n\t\t\t}\n\t\t\t// update children (replace current ID with new one in parent and parents)\n\t\t\tfor(i = 0, j = obj.children.length; i < j; i++) {\n\t\t\t\tm[obj.children[i]].parent = id;\n\t\t\t}\n\t\t\tfor(i = 0, j = obj.children_d.length; i < j; i++) {\n\t\t\t\tm[obj.children_d[i]].parents[$.inArray(obj.id, m[obj.children_d[i]].parents)] = id;\n\t\t\t}\n\t\t\t// update model and obj itself (obj.id, this._model.data[KEY])\n\t\t\ti = this.get_node(obj.id, true);\n\t\t\tif(i) {\n\t\t\t\ti.attr('id', id);\n\t\t\t}\n\t\t\tdelete m[obj.id];\n\t\t\tobj.id = id;\n\t\t\tm[id] = obj;\n\t\t\treturn true;\n\t\t},"}, {"tags": [{"type": "name", "string": "get_text(obj)"}, {"type": "param", "types": ["mixed"], "name": "obj", "description": "the node"}, {"type": "return", "types": ["String"], "description": ""}], "description": {"full": "<p>get the text value of a node</p>", "summary": "<p>get the text value of a node</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "get_text : function (obj) {\n\t\t\tobj = this.get_node(obj);\n\t\t\treturn (!obj || obj.id === '#') ? false : obj.text;\n\t\t},"}, {"tags": [{"type": "private", "string": ""}, {"type": "name", "string": "set_text(obj, val)"}, {"type": "param", "types": ["mixed"], "name": "obj", "description": "the node, you can pass an array to set the text on multiple nodes"}, {"type": "param", "types": ["String"], "name": "val", "description": "the new text value"}, {"type": "return", "types": ["Boolean"], "description": ""}, {"type": "trigger", "string": "set_text.jstree"}], "description": {"full": "<p>set the text value of a node. Used internally, please use <code>rename_node(obj, val)</code>.</p>", "summary": "<p>set the text value of a node. Used internally, please use <code>rename_node(obj, val)</code>.</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "set_text : function (obj, val) {\n\t\t\tvar t1, t2, dom, tmp;\n\t\t\tif($.isArray(obj)) {\n\t\t\t\tobj = obj.slice();\n\t\t\t\tfor(t1 = 0, t2 = obj.length; t1 < t2; t1++) {\n\t\t\t\t\tthis.set_text(obj[t1], val);\n\t\t\t\t}\n\t\t\t\treturn true;\n\t\t\t}\n\t\t\tobj = this.get_node(obj);\n\t\t\tif(!obj || obj.id === '#') { return false; }\n\t\t\tobj.text = val;\n\t\t\tdom = this.get_node(obj, true);\n\t\t\tif(dom.length) {\n\t\t\t\tdom = dom.children(\".jstree-anchor:eq(0)\");\n\t\t\t\ttmp = dom.children(\"I\").clone();\n\t\t\t\tdom.html(val).prepend(tmp);"}, {"tags": [{"type": "event", "string": ""}, {"type": "name", "string": "set_text.jstree"}, {"type": "param", "types": ["Object"], "name": "obj", "description": ""}, {"type": "param", "types": ["String"], "name": "text", "description": "the new value"}], "description": {"full": "<p>triggered when a node text value is changed</p>", "summary": "<p>triggered when a node text value is changed</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "this.trigger('set_text',{ \"obj\" : obj, \"text\" : val });\n\t\t\t}\n\t\t\treturn true;\n\t\t},"}, {"tags": [{"type": "name", "string": "get_json([obj, options])"}, {"type": "param", "types": ["mixed"], "name": "obj", "description": ""}, {"type": "param", "types": ["Object"], "name": "options", "description": ""}, {"type": "param", "types": ["Boolean"], "name": "options.no_state", "description": "do not return state information"}, {"type": "param", "types": ["Boolean"], "name": "options.no_id", "description": "do not return ID"}, {"type": "param", "types": ["Boolean"], "name": "options.no_children", "description": "do not include children"}, {"type": "return", "types": ["Object"], "description": ""}], "description": {"full": "<p>gets a JSON representation of a node (or the whole tree)</p>", "summary": "<p>gets a JSON representation of a node (or the whole tree)</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "get_json : function (obj, options) {\n\t\t\tobj = this.get_node(obj || '#');\n\t\t\tif(!obj) { return false; }\n\t\t\tvar tmp = {\n\t\t\t\t'id' : obj.id,\n\t\t\t\t'text' : obj.text,\n\t\t\t\t'icon' : this.get_icon(obj),\n\t\t\t\t'li_attr' : obj.li_attr,\n\t\t\t\t'a_attr' : obj.a_attr,\n\t\t\t\t'state' : {},\n\t\t\t\t'data' : options && options.no_data ? false : ( this.get_node(obj, true).length ? this.get_node(obj, true).data() : obj.data ),\n\t\t\t\t'children' : []\n\t\t\t}, i, j;\n\t\t\tif(!options || !options.no_state) {\n\t\t\t\tfor(i in obj.state) {\n\t\t\t\t\tif(obj.state.hasOwnProperty(i)) {\n\t\t\t\t\t\ttmp.state[i] = obj.state[i];\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tif(options && options.no_id && tmp.li_attr && tmp.li_attr.id) {\n\t\t\t\tdelete tmp.li_attr.id;\n\t\t\t\tdelete tmp.id;\n\t\t\t}\n\t\t\tif(!options || !options.no_children) {\n\t\t\t\tfor(i = 0, j = obj.children.length; i < j; i++) {\n\t\t\t\t\ttmp.children.push(this.get_json(obj.children[i], options));\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn obj.id === '#' ? tmp.children : tmp;\n\t\t},"}, {"tags": [{"type": "name", "string": "create_node([obj, node, pos, callback, is_loaded])"}, {"type": "param", "types": ["mixed"], "name": "par", "description": "the parent node"}, {"type": "param", "types": ["mixed"], "name": "node", "description": "the data for the new node (a valid JSON object, or a simple string with the name)"}, {"type": "param", "types": ["mixed"], "name": "pos", "description": "the index at which to insert the node, \"first\" and \"last\" are also supported, default is \"last\""}, {"type": "param", "types": ["Function"], "name": "callback", "description": "a function to be called once the node is created"}, {"type": "param", "types": ["Boolean"], "name": "is_loaded", "description": "internal argument indicating if the parent node was succesfully loaded"}, {"type": "return", "types": ["String"], "description": "the ID of the newly create node"}, {"type": "trigger", "string": "model.jstree, create_node.jstree"}], "description": {"full": "<p>create a new node (do not confuse with load_node)</p>", "summary": "<p>create a new node (do not confuse with load_node)</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "create_node : function (par, node, pos, callback, is_loaded) {\n\t\t\tpar = this.get_node(par);\n\t\t\tif(!par) { return false; }\n\t\t\tpos = pos === undefined ? \"last\" : pos;\n\t\t\tif(!pos.match(/^(before|after)$/) && !is_loaded && !this.is_loaded(par)) {\n\t\t\t\treturn this.load_node(par, function () { this.create_node(par, node, pos, callback, true); });\n\t\t\t}\n\t\t\tif(!node) { node = { \"text\" : this.get_string('New node') }; }\n\t\t\tif(node.text === undefined) { node.text = this.get_string('New node'); }\n\t\t\tvar tmp, dpc, i, j;\n\n\t\t\tif(par.id === '#') {\n\t\t\t\tif(pos === \"before\") { pos = \"first\"; }\n\t\t\t\tif(pos === \"after\") { pos = \"last\"; }\n\t\t\t}\n\t\t\tswitch(pos) {\n\t\t\t\tcase \"before\":\n\t\t\t\t\ttmp = this.get_node(par.parent);\n\t\t\t\t\tpos = $.inArray(par.id, tmp.children);\n\t\t\t\t\tpar = tmp;\n\t\t\t\t\tbreak;\n\t\t\t\tcase \"after\" :\n\t\t\t\t\ttmp = this.get_node(par.parent);\n\t\t\t\t\tpos = $.inArray(par.id, tmp.children) + 1;\n\t\t\t\t\tpar = tmp;\n\t\t\t\t\tbreak;\n\t\t\t\tcase \"inside\":\n\t\t\t\tcase \"first\":\n\t\t\t\t\tpos = 0;\n\t\t\t\t\tbreak;\n\t\t\t\tcase \"last\":\n\t\t\t\t\tpos = par.children.length;\n\t\t\t\t\tbreak;\n\t\t\t\tdefault:\n\t\t\t\t\tif(!pos) { pos = 0; }\n\t\t\t\t\tbreak;\n\t\t\t}\n\t\t\tif(pos > par.children.length) { pos = par.children.length; }\n\t\t\tif(!node.id) { node.id = true; }\n\t\t\tif(!this.check(\"create_node\", node, par, pos)) { return false; }\n\t\t\tif(node.id === true) { delete node.id; }\n\t\t\tnode = this._parse_model_from_json(node, par.id, par.parents.concat());\n\t\t\tif(!node) { return false; }\n\t\t\ttmp = this.get_node(node);\n\t\t\tdpc = [];\n\t\t\tdpc.push(node);\n\t\t\tdpc = dpc.concat(tmp.children_d);\n\t\t\tthis.trigger('model', { \"nodes\" : dpc, \"parent\" : par.id });\n\n\t\t\tpar.children_d = par.children_d.concat(dpc);\n\t\t\tfor(i = 0, j = par.parents.length; i < j; i++) {\n\t\t\t\tthis._model.data[par.parents[i]].children_d = this._model.data[par.parents[i]].children_d.concat(dpc);\n\t\t\t}\n\t\t\tnode = tmp;\n\t\t\ttmp = [];\n\t\t\tfor(i = 0, j = par.children.length; i < j; i++) {\n\t\t\t\ttmp[i >= pos ? i+1 : i] = par.children[i];\n\t\t\t}\n\t\t\ttmp[pos] = node.id;\n\t\t\tpar.children = tmp;\n\n\t\t\tthis.redraw_node(par, true);\n\t\t\tif(callback) { callback.call(this, this.get_node(node)); }"}, {"tags": [{"type": "event", "string": ""}, {"type": "name", "string": "create_node.jstree"}, {"type": "param", "types": ["Object"], "name": "node", "description": ""}, {"type": "param", "types": ["String"], "name": "parent", "description": "the parent's ID"}, {"type": "param", "types": ["Number"], "name": "position", "description": "the position of the new node among the parent's children"}], "description": {"full": "<p>triggered when a node is created</p>", "summary": "<p>triggered when a node is created</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "this.trigger('create_node', { \"node\" : this.get_node(node), \"parent\" : par.id, \"position\" : pos });\n\t\t\treturn node.id;\n\t\t},"}, {"tags": [{"type": "name", "string": "rename_node(obj, val)"}, {"type": "param", "types": ["mixed"], "name": "obj", "description": "the node, you can pass an array to rename multiple nodes to the same name"}, {"type": "param", "types": ["String"], "name": "val", "description": "the new text value"}, {"type": "return", "types": ["Boolean"], "description": ""}, {"type": "trigger", "string": "rename_node.jstree"}], "description": {"full": "<p>set the text value of a node</p>", "summary": "<p>set the text value of a node</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "rename_node : function (obj, val) {\n\t\t\tvar t1, t2, old;\n\t\t\tif($.isArray(obj)) {\n\t\t\t\tobj = obj.slice();\n\t\t\t\tfor(t1 = 0, t2 = obj.length; t1 < t2; t1++) {\n\t\t\t\t\tthis.rename_node(obj[t1], val);\n\t\t\t\t}\n\t\t\t\treturn true;\n\t\t\t}\n\t\t\tobj = this.get_node(obj);\n\t\t\tif(!obj || obj.id === '#') { return false; }\n\t\t\told = obj.text;\n\t\t\tif(!this.check(\"rename_node\", obj, this.get_parent(obj), val)) { return false; }\n\t\t\tthis.set_text(obj, val); // .apply(this, Array.prototype.slice.call(arguments))"}, {"tags": [{"type": "event", "string": ""}, {"type": "name", "string": "rename_node.jstree"}, {"type": "param", "types": ["Object"], "name": "node", "description": ""}, {"type": "param", "types": ["String"], "name": "text", "description": "the new value"}, {"type": "param", "types": ["String"], "name": "old", "description": "the old value"}], "description": {"full": "<p>triggered when a node is renamed</p>", "summary": "<p>triggered when a node is renamed</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "this.trigger('rename_node', { \"node\" : obj, \"text\" : val, \"old\" : old });\n\t\t\treturn true;\n\t\t},"}, {"tags": [{"type": "name", "string": "delete_node(obj)"}, {"type": "param", "types": ["mixed"], "name": "obj", "description": "the node, you can pass an array to delete multiple nodes"}, {"type": "return", "types": ["Boolean"], "description": ""}, {"type": "trigger", "string": "delete_node.jstree, changed.jstree"}], "description": {"full": "<p>remove a node</p>", "summary": "<p>remove a node</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "delete_node : function (obj) {\n\t\t\tvar t1, t2, par, pos, tmp, i, j, k, l, c;\n\t\t\tif($.isArray(obj)) {\n\t\t\t\tobj = obj.slice();\n\t\t\t\tfor(t1 = 0, t2 = obj.length; t1 < t2; t1++) {\n\t\t\t\t\tthis.delete_node(obj[t1]);\n\t\t\t\t}\n\t\t\t\treturn true;\n\t\t\t}\n\t\t\tobj = this.get_node(obj);\n\t\t\tif(!obj || obj.id === '#') { return false; }\n\t\t\tpar = this.get_node(obj.parent);\n\t\t\tpos = $.inArray(obj.id, par.children);\n\t\t\tc = false;\n\t\t\tif(!this.check(\"delete_node\", obj, par, pos)) { return false; }\n\t\t\tif(pos !== -1) {\n\t\t\t\tpar.children = $.vakata.array_remove(par.children, pos);\n\t\t\t}\n\t\t\ttmp = obj.children_d.concat([]);\n\t\t\ttmp.push(obj.id);\n\t\t\tfor(k = 0, l = tmp.length; k < l; k++) {\n\t\t\t\tfor(i = 0, j = obj.parents.length; i < j; i++) {\n\t\t\t\t\tpos = $.inArray(tmp[k], this._model.data[obj.parents[i]].children_d);\n\t\t\t\t\tif(pos !== -1) {\n\t\t\t\t\t\tthis._model.data[obj.parents[i]].children_d = $.vakata.array_remove(this._model.data[obj.parents[i]].children_d, pos);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif(this._model.data[tmp[k]].state.selected) {\n\t\t\t\t\tc = true;\n\t\t\t\t\tpos = $.inArray(tmp[k], this._data.core.selected);\n\t\t\t\t\tif(pos !== -1) {\n\t\t\t\t\t\tthis._data.core.selected = $.vakata.array_remove(this._data.core.selected, pos);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}"}, {"tags": [{"type": "event", "string": ""}, {"type": "name", "string": "delete_node.jstree"}, {"type": "param", "types": ["Object"], "name": "node", "description": ""}, {"type": "param", "types": ["String"], "name": "parent", "description": "the parent's ID"}], "description": {"full": "<p>triggered when a node is deleted</p>", "summary": "<p>triggered when a node is deleted</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "this.trigger('delete_node', { \"node\" : obj, \"parent\" : par.id });\n\t\t\tif(c) {\n\t\t\t\tthis.trigger('changed', { 'action' : 'delete_node', 'node' : obj, 'selected' : this._data.core.selected, 'parent' : par.id });\n\t\t\t}\n\t\t\tfor(k = 0, l = tmp.length; k < l; k++) {\n\t\t\t\tdelete this._model.data[tmp[k]];\n\t\t\t}\n\t\t\tthis.redraw_node(par, true);\n\t\t\treturn true;\n\t\t},"}, {"tags": [{"type": "private", "string": ""}, {"type": "name", "string": "check(chk, obj, par, pos)"}, {"type": "param", "types": ["String"], "name": "chk", "description": "the operation to check, can be \"create_node\", \"rename_node\", \"delete_node\", \"copy_node\" or \"move_node\""}, {"type": "param", "types": ["mixed"], "name": "obj", "description": "the node"}, {"type": "param", "types": ["mixed"], "name": "par", "description": "the parent"}, {"type": "param", "types": ["mixed"], "name": "pos", "description": "the position to insert at, or if \"rename_node\" - the new name"}, {"type": "return", "types": ["Boolean"], "description": ""}], "description": {"full": "<p>check if an operation is premitted on the tree. Used internally.</p>", "summary": "<p>check if an operation is premitted on the tree. Used internally.</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "check : function (chk, obj, par, pos) {\n\t\t\tobj = obj && obj.id ? obj : this.get_node(obj);\n\t\t\tpar = par && par.id ? par : this.get_node(par);\n\t\t\tvar tmp = chk.match(/^move_node|copy_node|create_node$/i) ? par : obj,\n\t\t\t\tchc = this.settings.core.check_callback;\n\t\t\tif(chk === \"move_node\") {\n\t\t\t\tif(obj.id === par.id || $.inArray(obj.id, par.children) === pos || $.inArray(par.id, obj.children_d) !== -1) {\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t}\n\t\t\ttmp = this.get_node(tmp, true);\n\t\t\tif(tmp.length) { tmp = tmp.data('jstree'); }\n\t\t\tif(tmp && tmp.functions && (tmp.functions[chk] === false || tmp.functions[chk] === true)) {\n\t\t\t\treturn tmp.functions[chk];\n\t\t\t}\n\t\t\tif(chc === false || ($.isFunction(chc) && chc.call(this, chk, obj, par, pos) === false) || (chc && chc[chk] === false)) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\treturn true;\n\t\t},"}, {"tags": [{"type": "name", "string": "move_node(obj, par [, pos, callback, is_loaded])"}, {"type": "param", "types": ["mixed"], "name": "obj", "description": "the node to move, pass an array to move multiple nodes"}, {"type": "param", "types": ["mixed"], "name": "par", "description": "the new parent"}, {"type": "param", "types": ["mixed"], "name": "pos", "description": "the position to insert at (\"first\" and \"last\" are supported, as well as \"before\" and \"after\"), defaults to `0`"}, {"type": "param", "types": ["function"], "name": "callback", "description": "a function to call once the move is completed, receives 3 arguments - the node, the new parent and the position"}, {"type": "param", "types": ["Boolean"], "name": "internal", "description": "parameter indicating if the parent node has been loaded"}, {"type": "trigger", "string": "move_node.jstree"}], "description": {"full": "<p>move a node to a new parent</p>", "summary": "<p>move a node to a new parent</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "move_node : function (obj, par, pos, callback, is_loaded) {\n\t\t\tvar t1, t2, old_par, new_par, old_ins, is_multi, dpc, tmp, i, j, k, l, p;\n\t\t\tif($.isArray(obj)) {\n\t\t\t\tobj = obj.reverse().slice();\n\t\t\t\tfor(t1 = 0, t2 = obj.length; t1 < t2; t1++) {\n\t\t\t\t\tthis.move_node(obj[t1], par, pos, callback, is_loaded);\n\t\t\t\t}\n\t\t\t\treturn true;\n\t\t\t}\n\t\t\tobj = obj && obj.id ? obj : this.get_node(obj);\n\t\t\tpar = this.get_node(par);\n\t\t\tpos = pos === undefined ? 0 : pos;\n\n\t\t\tif(!par || !obj || obj.id === '#') { return false; }\n\t\t\tif(!pos.toString().match(/^(before|after)$/) && !is_loaded && !this.is_loaded(par)) {\n\t\t\t\treturn this.load_node(par, function () { this.move_node(obj, par, pos, callback, true); });\n\t\t\t}\n\n\t\t\told_par = (obj.parent || '#').toString();\n\t\t\tnew_par = (!pos.toString().match(/^(before|after)$/) || par.id === '#') ? par : this.get_node(par.parent);\n\t\t\told_ins = this._model.data[obj.id] ? this : $.jstree.reference(obj.id);\n\t\t\tis_multi = !old_ins || !old_ins._id || (this._id !== old_ins._id);\n\t\t\tif(is_multi) {\n\t\t\t\tif(this.copy_node(obj, par, pos, callback, is_loaded)) {\n\t\t\t\t\tif(old_ins) { old_ins.delete_node(obj); }\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\t//var m = this._model.data;\n\t\t\tif(new_par.id === '#') {\n\t\t\t\tif(pos === \"before\") { pos = \"first\"; }\n\t\t\t\tif(pos === \"after\") { pos = \"last\"; }\n\t\t\t}\n\t\t\tswitch(pos) {\n\t\t\t\tcase \"before\":\n\t\t\t\t\tpos = $.inArray(par.id, new_par.children);\n\t\t\t\t\tbreak;\n\t\t\t\tcase \"after\" :\n\t\t\t\t\tpos = $.inArray(par.id, new_par.children) + 1;\n\t\t\t\t\tbreak;\n\t\t\t\tcase \"inside\":\n\t\t\t\tcase \"first\":\n\t\t\t\t\tpos = 0;\n\t\t\t\t\tbreak;\n\t\t\t\tcase \"last\":\n\t\t\t\t\tpos = new_par.children.length;\n\t\t\t\t\tbreak;\n\t\t\t\tdefault:\n\t\t\t\t\tif(!pos) { pos = 0; }\n\t\t\t\t\tbreak;\n\t\t\t}\n\t\t\tif(pos > new_par.children.length) { pos = new_par.children.length; }\n\t\t\tif(!this.check(\"move_node\", obj, new_par, pos)) { return false; }\n\t\t\tif(obj.parent === new_par.id) {\n\t\t\t\tdpc = new_par.children.concat();\n\t\t\t\ttmp = $.inArray(obj.id, dpc);\n\t\t\t\tif(tmp !== -1) {\n\t\t\t\t\tdpc = $.vakata.array_remove(dpc, tmp);\n\t\t\t\t\tif(pos > tmp) { pos--; }\n\t\t\t\t}\n\t\t\t\ttmp = [];\n\t\t\t\tfor(i = 0, j = dpc.length; i < j; i++) {\n\t\t\t\t\ttmp[i >= pos ? i+1 : i] = dpc[i];\n\t\t\t\t}\n\t\t\t\ttmp[pos] = obj.id;\n\t\t\t\tnew_par.children = tmp;\n\t\t\t\tthis._node_changed(new_par.id);\n\t\t\t\tthis.redraw(new_par.id === '#');\n\t\t\t}\n\t\t\telse {\n\t\t\t\t// clean old parent and up\n\t\t\t\ttmp = obj.children_d.concat();\n\t\t\t\ttmp.push(obj.id);\n\t\t\t\tfor(i = 0, j = obj.parents.length; i < j; i++) {\n\t\t\t\t\tdpc = [];\n\t\t\t\t\tp = old_ins._model.data[obj.parents[i]].children_d;\n\t\t\t\t\tfor(k = 0, l = p.length; k < l; k++) {\n\t\t\t\t\t\tif($.inArray(p[k], tmp) === -1) {\n\t\t\t\t\t\t\tdpc.push(p[k]);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\told_ins._model.data[obj.parents[i]].children_d = dpc;\n\t\t\t\t}\n\t\t\t\told_ins._model.data[old_par].children = $.vakata.array_remove_item(old_ins._model.data[old_par].children, obj.id);\n\n\t\t\t\t// insert into new parent and up\n\t\t\t\tfor(i = 0, j = new_par.parents.length; i < j; i++) {\n\t\t\t\t\tthis._model.data[new_par.parents[i]].children_d = this._model.data[new_par.parents[i]].children_d.concat(tmp);\n\t\t\t\t}\n\t\t\t\tdpc = [];\n\t\t\t\tfor(i = 0, j = new_par.children.length; i < j; i++) {\n\t\t\t\t\tdpc[i >= pos ? i+1 : i] = new_par.children[i];\n\t\t\t\t}\n\t\t\t\tdpc[pos] = obj.id;\n\t\t\t\tnew_par.children = dpc;\n\t\t\t\tnew_par.children_d.push(obj.id);\n\t\t\t\tnew_par.children_d = new_par.children_d.concat(obj.children_d);\n\n\t\t\t\t// update object\n\t\t\t\tobj.parent = new_par.id;\n\t\t\t\ttmp = new_par.parents.concat();\n\t\t\t\ttmp.unshift(new_par.id);\n\t\t\t\tp = obj.parents.length;\n\t\t\t\tobj.parents = tmp;\n\n\t\t\t\t// update object children\n\t\t\t\ttmp = tmp.concat();\n\t\t\t\tfor(i = 0, j = obj.children_d.length; i < j; i++) {\n\t\t\t\t\tthis._model.data[obj.children_d[i]].parents = this._model.data[obj.children_d[i]].parents.slice(0,p*-1);\n\t\t\t\t\tArray.prototype.push.apply(this._model.data[obj.children_d[i]].parents, tmp);\n\t\t\t\t}\n\n\t\t\t\tthis._node_changed(old_par);\n\t\t\t\tthis._node_changed(new_par.id);\n\t\t\t\tthis.redraw(old_par === '#' || new_par.id === '#');\n\t\t\t}\n\t\t\tif(callback) { callback.call(this, obj, new_par, pos); }"}, {"tags": [{"type": "event", "string": ""}, {"type": "name", "string": "move_node.jstree"}, {"type": "param", "types": ["Object"], "name": "node", "description": ""}, {"type": "param", "types": ["String"], "name": "parent", "description": "the parent's ID"}, {"type": "param", "types": ["Number"], "name": "position", "description": "the position of the node among the parent's children"}, {"type": "param", "types": ["String"], "name": "old_parent", "description": "the old parent of the node"}, {"type": "param", "types": ["Boolean"], "name": "is_multi", "description": "do the node and new parent belong to different instances"}, {"type": "param", "types": ["jsTree"], "name": "old_instance", "description": "the instance the node came from"}, {"type": "param", "types": ["jsTree"], "name": "new_instance", "description": "the instance of the new parent"}], "description": {"full": "<p>triggered when a node is moved</p>", "summary": "<p>triggered when a node is moved</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "this.trigger('move_node', { \"node\" : obj, \"parent\" : new_par.id, \"position\" : pos, \"old_parent\" : old_par, \"is_multi\" : is_multi, 'old_instance' : old_ins, 'new_instance' : this });\n\t\t\treturn true;\n\t\t},"}, {"tags": [{"type": "name", "string": "copy_node(obj, par [, pos, callback, is_loaded])"}, {"type": "param", "types": ["mixed"], "name": "obj", "description": "the node to copy, pass an array to copy multiple nodes"}, {"type": "param", "types": ["mixed"], "name": "par", "description": "the new parent"}, {"type": "param", "types": ["mixed"], "name": "pos", "description": "the position to insert at (\"first\" and \"last\" are supported, as well as \"before\" and \"after\"), defaults to `0`"}, {"type": "param", "types": ["function"], "name": "callback", "description": "a function to call once the move is completed, receives 3 arguments - the node, the new parent and the position"}, {"type": "param", "types": ["Boolean"], "name": "internal", "description": "parameter indicating if the parent node has been loaded"}, {"type": "trigger", "string": "model.jstree copy_node.jstree"}], "description": {"full": "<p>copy a node to a new parent</p>", "summary": "<p>copy a node to a new parent</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "copy_node : function (obj, par, pos, callback, is_loaded) {\n\t\t\tvar t1, t2, dpc, tmp, i, j, node, old_par, new_par, old_ins, is_multi;\n\t\t\tif($.isArray(obj)) {\n\t\t\t\tobj = obj.reverse().slice();\n\t\t\t\tfor(t1 = 0, t2 = obj.length; t1 < t2; t1++) {\n\t\t\t\t\tthis.copy_node(obj[t1], par, pos, callback, is_loaded);\n\t\t\t\t}\n\t\t\t\treturn true;\n\t\t\t}\n\t\t\tobj = obj && obj.id ? obj : this.get_node(obj);\n\t\t\tpar = this.get_node(par);\n\t\t\tpos = pos === undefined ? 0 : pos;\n\n\t\t\tif(!par || !obj || obj.id === '#') { return false; }\n\t\t\tif(!pos.toString().match(/^(before|after)$/) && !is_loaded && !this.is_loaded(par)) {\n\t\t\t\treturn this.load_node(par, function () { this.copy_node(obj, par, pos, callback, true); });\n\t\t\t}\n\n\t\t\told_par = (obj.parent || '#').toString();\n\t\t\tnew_par = (!pos.toString().match(/^(before|after)$/) || par.id === '#') ? par : this.get_node(par.parent);\n\t\t\told_ins = this._model.data[obj.id] ? this : $.jstree.reference(obj.id);\n\t\t\tis_multi = !old_ins || !old_ins._id || (this._id !== old_ins._id);\n\t\t\tif(new_par.id === '#') {\n\t\t\t\tif(pos === \"before\") { pos = \"first\"; }\n\t\t\t\tif(pos === \"after\") { pos = \"last\"; }\n\t\t\t}\n\t\t\tswitch(pos) {\n\t\t\t\tcase \"before\":\n\t\t\t\t\tpos = $.inArray(par.id, new_par.children);\n\t\t\t\t\tbreak;\n\t\t\t\tcase \"after\" :\n\t\t\t\t\tpos = $.inArray(par.id, new_par.children) + 1;\n\t\t\t\t\tbreak;\n\t\t\t\tcase \"inside\":\n\t\t\t\tcase \"first\":\n\t\t\t\t\tpos = 0;\n\t\t\t\t\tbreak;\n\t\t\t\tcase \"last\":\n\t\t\t\t\tpos = new_par.children.length;\n\t\t\t\t\tbreak;\n\t\t\t\tdefault:\n\t\t\t\t\tif(!pos) { pos = 0; }\n\t\t\t\t\tbreak;\n\t\t\t}\n\t\t\tif(pos > new_par.children.length) { pos = new_par.children.length; }\n\t\t\tif(!this.check(\"copy_node\", obj, new_par, pos)) { return false; }\n\t\t\tnode = old_ins ? old_ins.get_json(obj, { no_id : true, no_data : true, no_state : true }) : obj;\n\t\t\tif(!node) { return false; }\n\t\t\tif(node.id === true) { delete node.id; }\n\t\t\tnode = this._parse_model_from_json(node, new_par.id, new_par.parents.concat());\n\t\t\tif(!node) { return false; }\n\t\t\ttmp = this.get_node(node);\n\t\t\tdpc = [];\n\t\t\tdpc.push(node);\n\t\t\tdpc = dpc.concat(tmp.children_d);\n\t\t\tthis.trigger('model', { \"nodes\" : dpc, \"parent\" : new_par.id });\n\n\t\t\t// insert into new parent and up\n\t\t\tfor(i = 0, j = new_par.parents.length; i < j; i++) {\n\t\t\t\tthis._model.data[new_par.parents[i]].children_d = this._model.data[new_par.parents[i]].children_d.concat(dpc);\n\t\t\t}\n\t\t\tdpc = [];\n\t\t\tfor(i = 0, j = new_par.children.length; i < j; i++) {\n\t\t\t\tdpc[i >= pos ? i+1 : i] = new_par.children[i];\n\t\t\t}\n\t\t\tdpc[pos] = tmp.id;\n\t\t\tnew_par.children = dpc;\n\t\t\tnew_par.children_d.push(tmp.id);\n\t\t\tnew_par.children_d = new_par.children_d.concat(tmp.children_d);\n\n\t\t\tthis._node_changed(new_par.id);\n\t\t\tthis.redraw(new_par.id === '#');\n\t\t\tif(callback) { callback.call(this, tmp, new_par, pos); }"}, {"tags": [{"type": "event", "string": ""}, {"type": "name", "string": "copy_node.jstree"}, {"type": "param", "types": ["Object"], "name": "node", "description": "the copied node"}, {"type": "param", "types": ["Object"], "name": "original", "description": "the original node"}, {"type": "param", "types": ["String"], "name": "parent", "description": "the parent's ID"}, {"type": "param", "types": ["Number"], "name": "position", "description": "the position of the node among the parent's children"}, {"type": "param", "types": ["String"], "name": "old_parent", "description": "the old parent of the node"}, {"type": "param", "types": ["Boolean"], "name": "is_multi", "description": "do the node and new parent belong to different instances"}, {"type": "param", "types": ["jsTree"], "name": "old_instance", "description": "the instance the node came from"}, {"type": "param", "types": ["jsTree"], "name": "new_instance", "description": "the instance of the new parent"}], "description": {"full": "<p>triggered when a node is copied</p>", "summary": "<p>triggered when a node is copied</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "this.trigger('copy_node', { \"node\" : tmp, \"original\" : obj, \"parent\" : new_par.id, \"position\" : pos, \"old_parent\" : old_par, \"is_multi\" : is_multi, 'old_instance' : old_ins, 'new_instance' : this });\n\t\t\treturn tmp.id;\n\t\t},"}, {"tags": [{"type": "name", "string": "cut(obj)"}, {"type": "param", "types": ["mixed"], "name": "obj", "description": "multiple objects can be passed using an array"}, {"type": "trigger", "string": "cut.jstree"}], "description": {"full": "<p>cut a node (a later call to <code>paste(obj)</code> would move the node)</p>", "summary": "<p>cut a node (a later call to <code>paste(obj)</code> would move the node)</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "cut : function (obj) {\n\t\t\tif(!obj) { obj = this._data.core.selected.concat(); }\n\t\t\tif(!$.isArray(obj)) { obj = [obj]; }\n\t\t\tif(!obj.length) { return false; }\n\t\t\tvar tmp = [], o, t1, t2;\n\t\t\tfor(t1 = 0, t2 = obj.length; t1 < t2; t1++) {\n\t\t\t\to = this.get_node(obj[t1]);\n\t\t\t\tif(o && o.id && o.id !== '#') { tmp.push(o); }\n\t\t\t}\n\t\t\tif(!tmp.length) { return false; }\n\t\t\tccp_node = tmp;\n\t\t\tccp_inst = this;\n\t\t\tccp_mode = 'move_node';"}, {"tags": [{"type": "event", "string": ""}, {"type": "name", "string": "cut.jstree"}, {"type": "param", "types": ["Array"], "name": "node", "description": ""}], "description": {"full": "<p>triggered when nodes are added to the buffer for moving</p>", "summary": "<p>triggered when nodes are added to the buffer for moving</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "this.trigger('cut', { \"node\" : obj });\n\t\t},"}, {"tags": [{"type": "name", "string": "copy(obj)"}, {"type": "param", "types": ["mixed"], "name": "obj", "description": "multiple objects can be passed using an array"}, {"type": "trigger", "string": "copy.jstre"}], "description": {"full": "<p>copy a node (a later call to <code>paste(obj)</code> would copy the node)</p>", "summary": "<p>copy a node (a later call to <code>paste(obj)</code> would copy the node)</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "copy : function (obj) {\n\t\t\tif(!obj) { obj = this._data.core.selected.concat(); }\n\t\t\tif(!$.isArray(obj)) { obj = [obj]; }\n\t\t\tif(!obj.length) { return false; }\n\t\t\tvar tmp = [], o, t1, t2;\n\t\t\tfor(t1 = 0, t2 = obj.length; t1 < t2; t1++) {\n\t\t\t\to = this.get_node(obj[t1]);\n\t\t\t\tif(o && o.id && o.id !== '#') { tmp.push(o); }\n\t\t\t}\n\t\t\tif(!tmp.length) { return false; }\n\t\t\tccp_node = tmp;\n\t\t\tccp_inst = this;\n\t\t\tccp_mode = 'copy_node';"}, {"tags": [{"type": "event", "string": ""}, {"type": "name", "string": "copy.jstree"}, {"type": "param", "types": ["Array"], "name": "node", "description": ""}], "description": {"full": "<p>triggered when nodes are added to the buffer for copying</p>", "summary": "<p>triggered when nodes are added to the buffer for copying</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "this.trigger('copy', { \"node\" : obj });\n\t\t},"}, {"tags": [{"type": "name", "string": "get_buffer()"}, {"type": "return", "types": ["Object"], "description": "an object consisting of `mode` (\"copy_node\" or \"move_node\"), `node` (an array of objects) and `inst` (the instance)"}], "description": {"full": "<p>get the current buffer (any nodes that are waiting for a paste operation)</p>", "summary": "<p>get the current buffer (any nodes that are waiting for a paste operation)</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "get_buffer : function () {\n\t\t\treturn { 'mode' : ccp_mode, 'node' : ccp_node, 'inst' : ccp_inst };\n\t\t},"}, {"tags": [{"type": "name", "string": "can_paste()"}, {"type": "return", "types": ["Boolean"], "description": ""}], "description": {"full": "<p>check if there is something in the buffer to paste</p>", "summary": "<p>check if there is something in the buffer to paste</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "can_paste : function () {\n\t\t\treturn ccp_mode !== false && ccp_node !== false; // && ccp_inst._model.data[ccp_node];\n\t\t},"}, {"tags": [{"type": "name", "string": "paste(obj)"}, {"type": "param", "types": ["mixed"], "name": "obj", "description": "the new parent"}, {"type": "trigger", "string": "paste.jstree"}], "description": {"full": "<p>copy or move the previously cut or copied nodes to a new parent</p>", "summary": "<p>copy or move the previously cut or copied nodes to a new parent</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "paste : function (obj) {\n\t\t\tobj = this.get_node(obj);\n\t\t\tif(!obj || !ccp_mode || !ccp_mode.match(/^(copy_node|move_node)$/) || !ccp_node) { return false; }\n\t\t\tif(this[ccp_mode](ccp_node, obj)) {"}, {"tags": [{"type": "event", "string": ""}, {"type": "name", "string": "paste.jstree"}, {"type": "param", "types": ["String"], "name": "parent", "description": "the ID of the receiving node"}, {"type": "param", "types": ["Array"], "name": "node", "description": "the nodes in the buffer"}, {"type": "param", "types": ["String"], "name": "mode", "description": "the performed operation - \"copy_node\" or \"move_node\""}], "description": {"full": "<p>triggered when paste is invoked</p>", "summary": "<p>triggered when paste is invoked</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "this.trigger('paste', { \"parent\" : obj.id, \"node\" : ccp_node, \"mode\" : ccp_mode });\n\t\t\t}\n\t\t\tccp_node = false;\n\t\t\tccp_mode = false;\n\t\t\tccp_inst = false;\n\t\t},"}, {"tags": [{"type": "name", "string": "edit(obj [, default_text])"}, {"type": "param", "types": ["mixed"], "name": "obj", "description": ""}, {"type": "param", "types": ["String"], "name": "default_text", "description": "the text to populate the input with (if omitted the node text value is used)"}], "description": {"full": "<p>put a node in edit mode (input field to rename the node)</p>", "summary": "<p>put a node in edit mode (input field to rename the node)</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "edit : function (obj, default_text) {\n\t\t\tobj = this._open_to(obj);\n\t\t\tif(!obj || !obj.length) { return false; }\n\t\t\tvar rtl = this._data.core.rtl,\n\t\t\t\tw  = this.element.width(),\n\t\t\t\ta  = obj.children('.jstree-anchor'),\n\t\t\t\ts  = $('<span>'),"}, {"tags": [], "description": {"full": "<p>oi = obj.children(\"i:visible\"),<br />                ai = a.children(\"i:visible\"),<br />                w1 = oi.width() * oi.length,<br />                w2 = ai.width() * ai.length,</p>", "summary": "<p>oi = obj.children(\"i:visible\"),<br />                ai = a.children(\"i:visible\"),<br />                w1 = oi.width() * oi.length,<br />                w2 = ai.width() * ai.length,</p>", "body": ""}, "ignore": true, "code": "t  = typeof default_text === 'string' ? default_text : this.get_text(obj),\n\t\t\t\th1 = $(\"<\"+\"div />\", { css : { \"position\" : \"absolute\", \"top\" : \"-200px\", \"left\" : (rtl ? \"0px\" : \"-1000px\"), \"visibility\" : \"hidden\" } }).appendTo(\"body\"),\n\t\t\t\th2 = $(\"<\"+\"input />\", {\n\t\t\t\t\t\t\"value\" : t,\n\t\t\t\t\t\t\"class\" : \"jstree-rename-input\",\n\t\t\t\t\t\t// \"size\" : t.length,\n\t\t\t\t\t\t\"css\" : {\n\t\t\t\t\t\t\t\"padding\" : \"0\",\n\t\t\t\t\t\t\t\"border\" : \"1px solid silver\",\n\t\t\t\t\t\t\t\"box-sizing\" : \"border-box\",\n\t\t\t\t\t\t\t\"display\" : \"inline-block\",\n\t\t\t\t\t\t\t\"height\" : (this._data.core.li_height) + \"px\",\n\t\t\t\t\t\t\t\"lineHeight\" : (this._data.core.li_height) + \"px\",\n\t\t\t\t\t\t\t\"width\" : \"150px\" // will be set a bit further down\n\t\t\t\t\t\t},\n\t\t\t\t\t\t\"blur\" : $.proxy(function () {\n\t\t\t\t\t\t\tvar i = s.children(\".jstree-rename-input\"),\n\t\t\t\t\t\t\t\tv = i.val();\n\t\t\t\t\t\t\tif(v === \"\") { v = t; }\n\t\t\t\t\t\t\th1.remove();\n\t\t\t\t\t\t\ts.replaceWith(a);\n\t\t\t\t\t\t\ts.remove();\n\t\t\t\t\t\t\tif(this.rename_node(obj, v) === false) {\n\t\t\t\t\t\t\t\tthis.set_text(obj, t);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}, this),\n\t\t\t\t\t\t\"keydown\" : function (event) {\n\t\t\t\t\t\t\tvar key = event.which;\n\t\t\t\t\t\t\tif(key === 27) {\n\t\t\t\t\t\t\t\tthis.value = t;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif(key === 27 || key === 13 || key === 37 || key === 38 || key === 39 || key === 40 || key === 32) {\n\t\t\t\t\t\t\t\tevent.stopImmediatePropagation();\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif(key === 27 || key === 13) {\n\t\t\t\t\t\t\t\tevent.preventDefault();\n\t\t\t\t\t\t\t\tthis.blur();\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t},\n\t\t\t\t\t\t\"click\" : function (e) { e.stopImmediatePropagation(); },\n\t\t\t\t\t\t\"mousedown\" : function (e) { e.stopImmediatePropagation(); },\n\t\t\t\t\t\t\"keyup\" : function (event) {\n\t\t\t\t\t\t\th2.width(Math.min(h1.text(\"pW\" + this.value).width(),w));\n\t\t\t\t\t\t},\n\t\t\t\t\t\t\"keypress\" : function(event) {\n\t\t\t\t\t\t\tif(event.which === 13) { return false; }\n\t\t\t\t\t\t}\n\t\t\t\t\t}),\n\t\t\t\tfn = {\n\t\t\t\t\t\tfontFamily\t\t: a.css('fontFamily')\t\t|| '',\n\t\t\t\t\t\tfontSize\t\t: a.css('fontSize')\t\t\t|| '',\n\t\t\t\t\t\tfontWeight\t\t: a.css('fontWeight')\t\t|| '',\n\t\t\t\t\t\tfontStyle\t\t: a.css('fontStyle')\t\t|| '',\n\t\t\t\t\t\tfontStretch\t\t: a.css('fontStretch')\t\t|| '',\n\t\t\t\t\t\tfontVariant\t\t: a.css('fontVariant')\t\t|| '',\n\t\t\t\t\t\tletterSpacing\t: a.css('letterSpacing')\t|| '',\n\t\t\t\t\t\twordSpacing\t\t: a.css('wordSpacing')\t\t|| ''\n\t\t\t\t};\n\t\t\tthis.set_text(obj, \"\");\n\t\t\ts.attr('class', a.attr('class')).append(a.contents().clone()).append(h2);\n\t\t\ta.replaceWith(s);\n\t\t\th1.css(fn);\n\t\t\th2.css(fn).width(Math.min(h1.text(\"pW\" + h2[0].value).width(),w))[0].select();\n\t\t},"}, {"tags": [{"type": "name", "string": "set_theme(theme_name [, theme_url])"}, {"type": "param", "types": ["String"], "name": "theme_name", "description": "the name of the new theme to apply"}, {"type": "param", "types": ["mixed"], "name": "theme_url", "description": "the location of the CSS file for this theme. Omit or set to `false` if you manually included the file. Set to `true` to autoload from the `core.themes.dir` directory."}, {"type": "trigger", "string": "set_theme.jstree"}], "description": {"full": "<p>changes the theme</p>", "summary": "<p>changes the theme</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "set_theme : function (theme_name, theme_url) {\n\t\t\tif(!theme_name) { return false; }\n\t\t\tif(theme_url === true) {\n\t\t\t\tvar dir = this.settings.core.themes.dir;\n\t\t\t\tif(!dir) { dir = $.jstree.path + '/themes'; }\n\t\t\t\ttheme_url = dir + '/' + theme_name + '/style.css';\n\t\t\t}\n\t\t\tif(theme_url && $.inArray(theme_url, themes_loaded) === -1) {\n\t\t\t\t$('head').append('<'+'link rel=\"stylesheet\" href=\"' + theme_url + '\" type=\"text/css\" />');\n\t\t\t\tthemes_loaded.push(theme_url);\n\t\t\t}\n\t\t\tif(this._data.core.themes.name) {\n\t\t\t\tthis.element.removeClass('jstree-' + this._data.core.themes.name);\n\t\t\t}\n\t\t\tthis._data.core.themes.name = theme_name;\n\t\t\tthis.element.addClass('jstree-' + theme_name);\n\t\t\tthis.element[this.settings.core.themes.responsive ? 'addClass' : 'removeClass' ]('jstree-' + theme_name + '-responsive');"}, {"tags": [{"type": "event", "string": ""}, {"type": "name", "string": "set_theme.jstree"}, {"type": "param", "types": ["String"], "name": "theme", "description": "the new theme"}], "description": {"full": "<p>triggered when a theme is set</p>", "summary": "<p>triggered when a theme is set</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "this.trigger('set_theme', { 'theme' : theme_name });\n\t\t},"}, {"tags": [{"type": "name", "string": "get_theme()"}, {"type": "return", "types": ["String"], "description": ""}], "description": {"full": "<p>gets the name of the currently applied theme name</p>", "summary": "<p>gets the name of the currently applied theme name</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "get_theme : function () { return this._data.core.themes.name; },"}, {"tags": [{"type": "name", "string": "set_theme_variant(variant_name)"}, {"type": "param", "types": ["String", "Boolean"], "name": "variant_name", "description": "the variant to apply (if `false` is used the current variant is removed)"}], "description": {"full": "<p>changes the theme variant (if the theme has variants)</p>", "summary": "<p>changes the theme variant (if the theme has variants)</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "set_theme_variant : function (variant_name) {\n\t\t\tif(this._data.core.themes.variant) {\n\t\t\t\tthis.element.removeClass('jstree-' + this._data.core.themes.name + '-' + this._data.core.themes.variant);\n\t\t\t}\n\t\t\tthis._data.core.themes.variant = variant_name;\n\t\t\tif(variant_name) {\n\t\t\t\tthis.element.addClass('jstree-' + this._data.core.themes.name + '-' + this._data.core.themes.variant);\n\t\t\t}\n\t\t},"}, {"tags": [{"type": "name", "string": "get_theme()"}, {"type": "return", "types": ["String"], "description": ""}], "description": {"full": "<p>gets the name of the currently applied theme variant</p>", "summary": "<p>gets the name of the currently applied theme variant</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "get_theme_variant : function () { return this._data.core.themes.variant; },"}, {"tags": [{"type": "name", "string": "show_stripes()"}], "description": {"full": "<p>shows a striped background on the container (if the theme supports it)</p>", "summary": "<p>shows a striped background on the container (if the theme supports it)</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "show_stripes : function () { this._data.core.themes.stripes = true; this.get_container_ul().addClass(\"jstree-striped\"); },"}, {"tags": [{"type": "name", "string": "hide_stripes()"}], "description": {"full": "<p>hides the striped background on the container</p>", "summary": "<p>hides the striped background on the container</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "hide_stripes : function () { this._data.core.themes.stripes = false; this.get_container_ul().removeClass(\"jstree-striped\"); },"}, {"tags": [{"type": "name", "string": "toggle_stripes()"}], "description": {"full": "<p>toggles the striped background on the container</p>", "summary": "<p>toggles the striped background on the container</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "toggle_stripes : function () { if(this._data.core.themes.stripes) { this.hide_stripes(); } else { this.show_stripes(); } },"}, {"tags": [{"type": "name", "string": "show_dots()"}], "description": {"full": "<p>shows the connecting dots (if the theme supports it)</p>", "summary": "<p>shows the connecting dots (if the theme supports it)</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "show_dots : function () { this._data.core.themes.dots = true; this.get_container_ul().removeClass(\"jstree-no-dots\"); },"}, {"tags": [{"type": "name", "string": "hide_dots()"}], "description": {"full": "<p>hides the connecting dots</p>", "summary": "<p>hides the connecting dots</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "hide_dots : function () { this._data.core.themes.dots = false; this.get_container_ul().addClass(\"jstree-no-dots\"); },"}, {"tags": [{"type": "name", "string": "toggle_dots()"}], "description": {"full": "<p>toggles the connecting dots</p>", "summary": "<p>toggles the connecting dots</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "toggle_dots : function () { if(this._data.core.themes.dots) { this.hide_dots(); } else { this.show_dots(); } },"}, {"tags": [{"type": "name", "string": "show_icons()"}], "description": {"full": "<p>show the node icons</p>", "summary": "<p>show the node icons</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "show_icons : function () { this._data.core.themes.icons = true; this.get_container_ul().removeClass(\"jstree-no-icons\"); },"}, {"tags": [{"type": "name", "string": "hide_icons()"}], "description": {"full": "<p>hide the node icons</p>", "summary": "<p>hide the node icons</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "hide_icons : function () { this._data.core.themes.icons = false; this.get_container_ul().addClass(\"jstree-no-icons\"); },"}, {"tags": [{"type": "name", "string": "toggle_icons()"}], "description": {"full": "<p>toggle the node icons</p>", "summary": "<p>toggle the node icons</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "toggle_icons : function () { if(this._data.core.themes.icons) { this.hide_icons(); } else { this.show_icons(); } },"}, {"tags": [{"type": "name", "string": "set_icon(obj, icon)"}, {"type": "param", "types": ["mixed"], "name": "obj", "description": ""}, {"type": "param", "types": ["String"], "name": "icon", "description": "the new icon - can be a path to an icon or a className, if using an image that is in the current directory use a `./` prefix, otherwise it will be detected as a class"}], "description": {"full": "<p>set the node icon for a node</p>", "summary": "<p>set the node icon for a node</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "set_icon : function (obj, icon) {\n\t\t\tvar t1, t2, dom, old;\n\t\t\tif($.isArray(obj)) {\n\t\t\t\tobj = obj.slice();\n\t\t\t\tfor(t1 = 0, t2 = obj.length; t1 < t2; t1++) {\n\t\t\t\t\tthis.set_icon(obj[t1], icon);\n\t\t\t\t}\n\t\t\t\treturn true;\n\t\t\t}\n\t\t\tobj = this.get_node(obj);\n\t\t\tif(!obj || obj.id === '#') { return false; }\n\t\t\told = obj.icon;\n\t\t\tobj.icon = icon;\n\t\t\tdom = this.get_node(obj, true).children(\".jstree-anchor\").children(\".jstree-themeicon\");\n\t\t\tif(icon === false) {\n\t\t\t\tthis.hide_icon(obj);\n\t\t\t}\n\t\t\telse if(icon === true) {\n\t\t\t\tdom.removeClass('jstree-themeicon-custom ' + old).css(\"background\",\"\").removeAttr(\"rel\");\n\t\t\t}\n\t\t\telse if(icon.indexOf(\"/\") === -1 && icon.indexOf(\".\") === -1) {\n\t\t\t\tdom.removeClass(old).css(\"background\",\"\");\n\t\t\t\tdom.addClass(icon + ' jstree-themeicon-custom').attr(\"rel\",icon);\n\t\t\t}\n\t\t\telse {\n\t\t\t\tdom.removeClass(old).css(\"background\",\"\");\n\t\t\t\tdom.addClass('jstree-themeicon-custom').css(\"background\", \"url('\" + icon + \"') center center no-repeat\").attr(\"rel\",icon);\n\t\t\t}\n\t\t\treturn true;\n\t\t},"}, {"tags": [{"type": "name", "string": "get_icon(obj)"}, {"type": "param", "types": ["mixed"], "name": "obj", "description": ""}, {"type": "return", "types": ["String"], "description": ""}], "description": {"full": "<p>get the node icon for a node</p>", "summary": "<p>get the node icon for a node</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "get_icon : function (obj) {\n\t\t\tobj = this.get_node(obj);\n\t\t\treturn (!obj || obj.id === '#') ? false : obj.icon;\n\t\t},"}, {"tags": [{"type": "name", "string": "hide_icon(obj)"}, {"type": "param", "types": ["mixed"], "name": "obj", "description": ""}], "description": {"full": "<p>hide the icon on an individual node</p>", "summary": "<p>hide the icon on an individual node</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "hide_icon : function (obj) {\n\t\t\tvar t1, t2;\n\t\t\tif($.isArray(obj)) {\n\t\t\t\tobj = obj.slice();\n\t\t\t\tfor(t1 = 0, t2 = obj.length; t1 < t2; t1++) {\n\t\t\t\t\tthis.hide_icon(obj[t1]);\n\t\t\t\t}\n\t\t\t\treturn true;\n\t\t\t}\n\t\t\tobj = this.get_node(obj);\n\t\t\tif(!obj || obj === '#') { return false; }\n\t\t\tobj.icon = false;\n\t\t\tthis.get_node(obj, true).children(\"a\").children(\".jstree-themeicon\").addClass('jstree-themeicon-hidden');\n\t\t\treturn true;\n\t\t},"}, {"tags": [{"type": "name", "string": "show_icon(obj)"}, {"type": "param", "types": ["mixed"], "name": "obj", "description": ""}], "description": {"full": "<p>show the icon on an individual node</p>", "summary": "<p>show the icon on an individual node</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "show_icon : function (obj) {\n\t\t\tvar t1, t2, dom;\n\t\t\tif($.isArray(obj)) {\n\t\t\t\tobj = obj.slice();\n\t\t\t\tfor(t1 = 0, t2 = obj.length; t1 < t2; t1++) {\n\t\t\t\t\tthis.show_icon(obj[t1]);\n\t\t\t\t}\n\t\t\t\treturn true;\n\t\t\t}\n\t\t\tobj = this.get_node(obj);\n\t\t\tif(!obj || obj === '#') { return false; }\n\t\t\tdom = this.get_node(obj, true);\n\t\t\tobj.icon = dom.length ? dom.children(\"a\").children(\".jstree-themeicon\").attr('rel') : true;\n\t\t\tif(!obj.icon) { obj.icon = true; }\n\t\t\tdom.children(\"a\").children(\".jstree-themeicon\").removeClass('jstree-themeicon-hidden');\n\t\t\treturn true;\n\t\t}\n\t};\n\n\t// helpers\n\t$.vakata = {};\n\t// reverse\n\t$.fn.vakata_reverse = [].reverse;\n\t// collect attributes\n\t$.vakata.attributes = function(node, with_values) {\n\t\tnode = $(node)[0];\n\t\tvar attr = with_values ? {} : [];\n\t\tif(node && node.attributes) {\n\t\t\t$.each(node.attributes, function (i, v) {\n\t\t\t\tif($.inArray(v.nodeName.toLowerCase(),['style','contenteditable','hasfocus','tabindex']) !== -1) { return; }\n\t\t\t\tif(v.nodeValue !== null && $.trim(v.nodeValue) !== '') {\n\t\t\t\t\tif(with_values) { attr[v.nodeName] = v.nodeValue; }\n\t\t\t\t\telse { attr.push(v.nodeName); }\n\t\t\t\t}\n\t\t\t});\n\t\t}\n\t\treturn attr;\n\t};\n\t$.vakata.array_unique = function(array) {\n\t\tvar a = [], i, j, l;\n\t\tfor(i = 0, l = array.length; i < l; i++) {\n\t\t\tfor(j = 0; j <= i; j++) {\n\t\t\t\tif(array[i] === array[j]) {\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t}\n\t\t\tif(j === i) { a.push(array[i]); }\n\t\t}\n\t\treturn a;\n\t};\n\t// remove item from array\n\t$.vakata.array_remove = function(array, from, to) {\n\t\tvar rest = array.slice((to || from) + 1 || array.length);\n\t\tarray.length = from < 0 ? array.length + from : from;\n\t\tarray.push.apply(array, rest);\n\t\treturn array;\n\t};\n\t// remove item from array\n\t$.vakata.array_remove_item = function(array, item) {\n\t\tvar tmp = $.inArray(item, array);\n\t\treturn tmp !== -1 ? $.vakata.array_remove(array, tmp) : array;\n\t};\n\t// browser sniffing\n\t(function () {\n\t\tvar browser = {},\n\t\t\tb_match = function(ua) {\n\t\t\tua = ua.toLowerCase();\n\n\t\t\tvar match =\t/(chrome)[ \\/]([\\w.]+)/.exec( ua ) ||\n\t\t\t\t\t\t/(webkit)[ \\/]([\\w.]+)/.exec( ua ) ||\n\t\t\t\t\t\t/(opera)(?:.*version|)[ \\/]([\\w.]+)/.exec( ua ) ||\n\t\t\t\t\t\t/(msie) ([\\w.]+)/.exec( ua ) ||\n\t\t\t\t\t\t(ua.indexOf(\"compatible\") < 0 && /(mozilla)(?:.*? rv:([\\w.]+)|)/.exec( ua )) ||\n\t\t\t\t\t\t[];\n\t\t\t\treturn {\n\t\t\t\t\tbrowser: match[1] || \"\",\n\t\t\t\t\tversion: match[2] || \"0\"\n\t\t\t\t};\n\t\t\t},\n\t\t\tmatched = b_match(window.navigator.userAgent);\n\t\tif(matched.browser) {\n\t\t\tbrowser[ matched.browser ] = true;\n\t\t\tbrowser.version = matched.version;\n\t\t}\n\t\tif(browser.chrome) {\n\t\t\tbrowser.webkit = true;\n\t\t}\n\t\telse if(browser.webkit) {\n\t\t\tbrowser.safari = true;\n\t\t}\n\t\t$.vakata.browser = browser;\n\t}());\n\tif($.vakata.browser.msie && $.vakata.browser.version < 8) {\n\t\t$.jstree.defaults.core.animation = 0;\n\t}\n\t(function ($, undefined) {\n\t\t// private function for json quoting strings\n\t\tvar _quote = function (str) {\n\t\t\tvar escapeable\t= /[\"\\\\\\x00-\\x1f\\x7f-\\x9f]/g,\n\t\t\t\tmeta\t\t= { '\\b':'\\\\b','\\t':'\\\\t','\\n':'\\\\n','\\f':'\\\\f','\\r':'\\\\r','\"' :'\\\\\"','\\\\':'\\\\\\\\' };\n\t\t\tif(str.match(escapeable)) {\n\t\t\t\treturn '\"' + str.replace(escapeable, function (a) {\n\t\t\t\t\t\tvar c = meta[a];\n\t\t\t\t\t\tif(typeof c === 'string') { return c; }\n\t\t\t\t\t\tc = a.charCodeAt();\n\t\t\t\t\t\treturn '\\\\u00' + Math.floor(c / 16).toString(16) + (c % 16).toString(16);\n\t\t\t\t\t}) + '\"';\n\t\t\t}\n\t\t\treturn '\"' + str + '\"';\n\t\t};\n\t\t$.vakata.json = {\n\t\t\tencode : (JSON && JSON.stringify ? JSON.stringify : function (o) {\n\t\t\t\tif (o === null) { return \"null\"; }\n\n\t\t\t\tvar tmp = [], i;\n\t\t\t\tswitch(typeof o) {\n\t\t\t\t\tcase \"undefined\":\n\t\t\t\t\t\treturn undefined;\n\t\t\t\t\tcase \"number\":\n\t\t\t\t\tcase \"boolean\":\n\t\t\t\t\t\treturn o.toString();\n\t\t\t\t\tcase \"string\":\n\t\t\t\t\t\treturn _quote(o);\n\t\t\t\t\tcase \"object\":\n\t\t\t\t\t\tif($.isFunction(o.toJSON)) {\n\t\t\t\t\t\t\treturn $.vakata.json.encode(o.toJSON());\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif(o.constructor === Date) {\n\t\t\t\t\t\t\treturn '\"' +\n\t\t\t\t\t\t\t\to.getUTCFullYear() + '-' +\n\t\t\t\t\t\t\t\tString(\"0\" + (o.getUTCMonth() + 1)).slice(-2) + '-' +\n\t\t\t\t\t\t\t\tString(\"0\" + o.getUTCDate()).slice(-2) + 'T' +\n\t\t\t\t\t\t\t\tString(\"0\" + o.getUTCHours()).slice(-2) + ':' +\n\t\t\t\t\t\t\t\tString(\"0\" + o.getUTCMinutes()).slice(-2) + ':' +\n\t\t\t\t\t\t\t\tString(\"0\" + o.getUTCSeconds()).slice(-2) + '.' +\n\t\t\t\t\t\t\t\tString(\"00\" + o.getUTCMilliseconds()).slice(-3) + 'Z\"';\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif(o.constructor === Array) {\n\t\t\t\t\t\t\tfor(i = 0; i < o.length; i++) {\n\t\t\t\t\t\t\t\ttmp.push( $.vakata.json.encode(o[i]) || \"null\" );\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\treturn \"[\" + tmp.join(\",\") + \"]\";\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t$.each(o, function (i, v) {\n\t\t\t\t\t\t\tif($.isFunction(v)) { return true; }\n\t\t\t\t\t\t\ti = typeof i === \"number\" ? '\"' + i + '\"' : _quote(i);\n\t\t\t\t\t\t\tv = $.vakata.json.encode(v);\n\t\t\t\t\t\t\ttmp.push(i + \":\" + v);\n\t\t\t\t\t\t});\n\t\t\t\t\t\treturn \"{\" + tmp.join(\", \") + \"}\";\n\t\t\t\t}\n\t\t\t}),\n\t\t\tdecode : (JSON && JSON.parse ? JSON.parse : function (json) {\n\t\t\t\treturn $.parseJSON(json);\n\t\t\t})\n\t\t};\n\t}(jQuery));"}, {"tags": [], "description": {"full": "<h3>Checkbox plugin</h3>\n\n<p>This plugin renders checkbox icons in front of each node, making multiple selection much easier. <br />It also supports tri-state behavior, meaning that if a node has a few of its children checked it will be rendered as undetermined, and state will be propagated up.</p>", "summary": "<h3>Checkbox plugin</h3>", "body": "<p>This plugin renders checkbox icons in front of each node, making multiple selection much easier. <br />It also supports tri-state behavior, meaning that if a node has a few of its children checked it will be rendered as undetermined, and state will be propagated up.</p>"}, "ignore": false, "code": "var _i = document.createElement('I');\n\t_i.className = 'jstree-icon jstree-checkbox';", "ctx": {"type": "declaration", "name": "_i", "value": "document.createElement('I')", "string": "_i"}}, {"tags": [{"type": "name", "string": "$.jstree.defaults.checkbox"}, {"type": "plugin", "string": "checkbox"}], "description": {"full": "<p>stores all defaults for the checkbox plugin</p>", "summary": "<p>stores all defaults for the checkbox plugin</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "$.jstree.defaults.checkbox = {"}, {"tags": [{"type": "name", "string": "$.jstree.defaults.checkbox.visible"}, {"type": "plugin", "string": "checkbox"}], "description": {"full": "<p>a boolean indicating if checkboxes should be visible (can be changed at a later time using <code>show_checkboxes()</code> and <code>hide_checkboxes</code>). Defaults to <code>true</code>.</p>", "summary": "<p>a boolean indicating if checkboxes should be visible (can be changed at a later time using <code>show_checkboxes()</code> and <code>hide_checkboxes</code>). Defaults to <code>true</code>.</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "visible\t\t\t\t: true,"}, {"tags": [{"type": "name", "string": "$.jstree.defaults.checkbox.three_state"}, {"type": "plugin", "string": "checkbox"}], "description": {"full": "<p>a boolean indicating if checkboxes should cascade down and have an undetermined state. Defaults to <code>true</code>.</p>", "summary": "<p>a boolean indicating if checkboxes should cascade down and have an undetermined state. Defaults to <code>true</code>.</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "three_state\t\t\t: true,"}, {"tags": [{"type": "name", "string": "$.jstree.defaults.checkbox.whole_node"}, {"type": "plugin", "string": "checkbox"}], "description": {"full": "<p>a boolean indicating if clicking anywhere on the node should act as clicking on the checkbox. Defaults to <code>true</code>.</p>", "summary": "<p>a boolean indicating if clicking anywhere on the node should act as clicking on the checkbox. Defaults to <code>true</code>.</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "whole_node\t\t\t: true,"}, {"tags": [{"type": "name", "string": "$.jstree.defaults.checkbox.keep_selected_style"}, {"type": "plugin", "string": "checkbox"}], "description": {"full": "<p>a boolean indicating if the selected style of a node should be kept, or removed. Defaults to <code>true</code>.</p>", "summary": "<p>a boolean indicating if the selected style of a node should be kept, or removed. Defaults to <code>true</code>.</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "keep_selected_style\t: true\n\t};\n\t$.jstree.plugins.checkbox = function (options, parent) {\n\t\tthis.bind = function () {\n\t\t\tparent.bind.call(this);\n\t\t\tthis._data.checkbox.uto = false;\n\t\t\tthis.element\n\t\t\t\t.on(\"init.jstree\", $.proxy(function () {\n\t\t\t\t\t\tthis._data.checkbox.visible = this.settings.checkbox.visible;\n\t\t\t\t\t\tif(!this.settings.checkbox.keep_selected_style) {\n\t\t\t\t\t\t\tthis.element.addClass('jstree-checkbox-no-clicked');\n\t\t\t\t\t\t}\n\t\t\t\t\t}, this))\n\t\t\t\t.on(\"loading.jstree\", $.proxy(function () {\n\t\t\t\t\t\tthis[ this._data.checkbox.visible ? 'show_checkboxes' : 'hide_checkboxes' ]();\n\t\t\t\t\t}, this));\n\t\t\tif(this.settings.checkbox.three_state) {\n\t\t\t\tthis.element\n\t\t\t\t\t.on('changed.jstree move_node.jstree copy_node.jstree redraw.jstree open_node.jstree', $.proxy(function () {\n\t\t\t\t\t\t\tif(this._data.checkbox.uto) { clearTimeout(this._data.checkbox.uto); }\n\t\t\t\t\t\t\tthis._data.checkbox.uto = setTimeout($.proxy(this._undetermined, this), 50);\n\t\t\t\t\t\t}, this))\n\t\t\t\t\t.on('model.jstree', $.proxy(function (e, data) {\n\t\t\t\t\t\t\tvar m = this._model.data,\n\t\t\t\t\t\t\t\tp = m[data.parent],\n\t\t\t\t\t\t\t\tdpc = data.nodes,\n\t\t\t\t\t\t\t\tchd = [],\n\t\t\t\t\t\t\t\tc, i, j, k, l, tmp;\n\n\t\t\t\t\t\t\t// apply down\n\t\t\t\t\t\t\tif(p.state.selected) {\n\t\t\t\t\t\t\t\tfor(i = 0, j = dpc.length; i < j; i++) {\n\t\t\t\t\t\t\t\t\tm[dpc[i]].state.selected = true;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tthis._data.core.selected = this._data.core.selected.concat(dpc);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\telse {\n\t\t\t\t\t\t\t\tfor(i = 0, j = dpc.length; i < j; i++) {\n\t\t\t\t\t\t\t\t\tif(m[dpc[i]].state.selected) {\n\t\t\t\t\t\t\t\t\t\tfor(k = 0, l = m[dpc[i]].children_d.length; k < l; k++) {\n\t\t\t\t\t\t\t\t\t\t\tm[m[dpc[i]].children_d[k]].state.selected = true;\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\tthis._data.core.selected = this._data.core.selected.concat(m[dpc[i]].children_d);\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t// apply up\n\t\t\t\t\t\t\tfor(i = 0, j = p.children_d.length; i < j; i++) {\n\t\t\t\t\t\t\t\tif(!m[p.children_d[i]].children.length) {\n\t\t\t\t\t\t\t\t\tchd.push(m[p.children_d[i]].parent);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tchd = $.vakata.array_unique(chd);\n\t\t\t\t\t\t\tfor(k = 0, l = chd.length; k < l; k++) {\n\t\t\t\t\t\t\t\tp = m[chd[k]];\n\t\t\t\t\t\t\t\twhile(p && p.id !== '#') {\n\t\t\t\t\t\t\t\t\tc = 0;\n\t\t\t\t\t\t\t\t\tfor(i = 0, j = p.children.length; i < j; i++) {\n\t\t\t\t\t\t\t\t\t\tc += m[p.children[i]].state.selected;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\tif(c === j) {\n\t\t\t\t\t\t\t\t\t\tp.state.selected = true;\n\t\t\t\t\t\t\t\t\t\tthis._data.core.selected.push(p.id);\n\t\t\t\t\t\t\t\t\t\ttmp = this.get_node(p, true);\n\t\t\t\t\t\t\t\t\t\tif(tmp && tmp.length) {\n\t\t\t\t\t\t\t\t\t\t\ttmp.children('.jstree-anchor').addClass('jstree-clicked');\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\telse {\n\t\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\tp = this.get_node(p.parent);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tthis._data.core.selected = $.vakata.array_unique(this._data.core.selected);\n\t\t\t\t\t\t}, this))\n\t\t\t\t\t.on('select_node.jstree', $.proxy(function (e, data) {\n\t\t\t\t\t\t\tvar obj = data.node,\n\t\t\t\t\t\t\t\tm = this._model.data,\n\t\t\t\t\t\t\t\tpar = this.get_node(obj.parent),\n\t\t\t\t\t\t\t\tdom = this.get_node(obj, true),\n\t\t\t\t\t\t\t\ti, j, c, tmp;\n\t\t\t\t\t\t\tthis._data.core.selected = $.vakata.array_unique(this._data.core.selected.concat(obj.children_d));\n\t\t\t\t\t\t\tfor(i = 0, j = obj.children_d.length; i < j; i++) {\n\t\t\t\t\t\t\t\tm[obj.children_d[i]].state.selected = true;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\twhile(par && par.id !== '#') {\n\t\t\t\t\t\t\t\tc = 0;\n\t\t\t\t\t\t\t\tfor(i = 0, j = par.children.length; i < j; i++) {\n\t\t\t\t\t\t\t\t\tc += m[par.children[i]].state.selected;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tif(c === j) {\n\t\t\t\t\t\t\t\t\tpar.state.selected = true;\n\t\t\t\t\t\t\t\t\tthis._data.core.selected.push(par.id);\n\t\t\t\t\t\t\t\t\ttmp = this.get_node(par, true);\n\t\t\t\t\t\t\t\t\tif(tmp && tmp.length) {\n\t\t\t\t\t\t\t\t\t\ttmp.children('.jstree-anchor').addClass('jstree-clicked');\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\telse {\n\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tpar = this.get_node(par.parent);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif(dom.length) {\n\t\t\t\t\t\t\t\tdom.find('.jstree-anchor').addClass('jstree-clicked');\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}, this))\n\t\t\t\t\t.on('deselect_node.jstree', $.proxy(function (e, data) {\n\t\t\t\t\t\t\tvar obj = data.node,\n\t\t\t\t\t\t\t\tdom = this.get_node(obj, true),\n\t\t\t\t\t\t\t\ti, j, tmp;\n\t\t\t\t\t\t\tfor(i = 0, j = obj.children_d.length; i < j; i++) {\n\t\t\t\t\t\t\t\tthis._model.data[obj.children_d[i]].state.selected = false;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tfor(i = 0, j = obj.parents.length; i < j; i++) {\n\t\t\t\t\t\t\t\tthis._model.data[obj.parents[i]].state.selected = false;\n\t\t\t\t\t\t\t\ttmp = this.get_node(obj.parents[i], true);\n\t\t\t\t\t\t\t\tif(tmp && tmp.length) {\n\t\t\t\t\t\t\t\t\ttmp.children('.jstree-anchor').removeClass('jstree-clicked');\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\ttmp = [];\n\t\t\t\t\t\t\tfor(i = 0, j = this._data.core.selected.length; i < j; i++) {\n\t\t\t\t\t\t\t\tif($.inArray(this._data.core.selected[i], obj.children_d) === -1 && $.inArray(this._data.core.selected[i], obj.parents) === -1) {\n\t\t\t\t\t\t\t\t\ttmp.push(this._data.core.selected[i]);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tthis._data.core.selected = $.vakata.array_unique(tmp);\n\t\t\t\t\t\t\tif(dom.length) {\n\t\t\t\t\t\t\t\tdom.find('.jstree-anchor').removeClass('jstree-clicked');\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}, this))\n\t\t\t\t\t.on('delete_node.jstree', $.proxy(function (e, data) {\n\t\t\t\t\t\t\tvar p = this.get_node(data.parent),\n\t\t\t\t\t\t\t\tm = this._model.data,\n\t\t\t\t\t\t\t\ti, j, c, tmp;\n\t\t\t\t\t\t\twhile(p && p.id !== '#') {\n\t\t\t\t\t\t\t\tc = 0;\n\t\t\t\t\t\t\t\tfor(i = 0, j = p.children.length; i < j; i++) {\n\t\t\t\t\t\t\t\t\tc += m[p.children[i]].state.selected;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tif(c === j) {\n\t\t\t\t\t\t\t\t\tp.state.selected = true;\n\t\t\t\t\t\t\t\t\tthis._data.core.selected.push(p.id);\n\t\t\t\t\t\t\t\t\ttmp = this.get_node(p, true);\n\t\t\t\t\t\t\t\t\tif(tmp && tmp.length) {\n\t\t\t\t\t\t\t\t\t\ttmp.children('.jstree-anchor').addClass('jstree-clicked');\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\telse {\n\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tp = this.get_node(p.parent);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}, this))\n\t\t\t\t\t.on('move_node.jstree', $.proxy(function (e, data) {\n\t\t\t\t\t\t\tvar is_multi = data.is_multi,\n\t\t\t\t\t\t\t\told_par = data.old_parent,\n\t\t\t\t\t\t\t\tnew_par = this.get_node(data.parent),\n\t\t\t\t\t\t\t\tm = this._model.data,\n\t\t\t\t\t\t\t\tp, c, i, j, tmp;\n\t\t\t\t\t\t\tif(!is_multi) {\n\t\t\t\t\t\t\t\tp = this.get_node(old_par);\n\t\t\t\t\t\t\t\twhile(p && p.id !== '#') {\n\t\t\t\t\t\t\t\t\tc = 0;\n\t\t\t\t\t\t\t\t\tfor(i = 0, j = p.children.length; i < j; i++) {\n\t\t\t\t\t\t\t\t\t\tc += m[p.children[i]].state.selected;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\tif(c === j) {\n\t\t\t\t\t\t\t\t\t\tp.state.selected = true;\n\t\t\t\t\t\t\t\t\t\tthis._data.core.selected.push(p.id);\n\t\t\t\t\t\t\t\t\t\ttmp = this.get_node(p, true);\n\t\t\t\t\t\t\t\t\t\tif(tmp && tmp.length) {\n\t\t\t\t\t\t\t\t\t\t\ttmp.children('.jstree-anchor').addClass('jstree-clicked');\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\telse {\n\t\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\tp = this.get_node(p.parent);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tp = new_par;\n\t\t\t\t\t\t\twhile(p && p.id !== '#') {\n\t\t\t\t\t\t\t\tc = 0;\n\t\t\t\t\t\t\t\tfor(i = 0, j = p.children.length; i < j; i++) {\n\t\t\t\t\t\t\t\t\tc += m[p.children[i]].state.selected;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tif(c === j) {\n\t\t\t\t\t\t\t\t\tif(!p.state.selected) {\n\t\t\t\t\t\t\t\t\t\tp.state.selected = true;\n\t\t\t\t\t\t\t\t\t\tthis._data.core.selected.push(p.id);\n\t\t\t\t\t\t\t\t\t\ttmp = this.get_node(p, true);\n\t\t\t\t\t\t\t\t\t\tif(tmp && tmp.length) {\n\t\t\t\t\t\t\t\t\t\t\ttmp.children('.jstree-anchor').addClass('jstree-clicked');\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\telse {\n\t\t\t\t\t\t\t\t\tif(p.state.selected) {\n\t\t\t\t\t\t\t\t\t\tp.state.selected = false;\n\t\t\t\t\t\t\t\t\t\tthis._data.core.selected = $.vakata.array_remove_item(this._data.core.selected, p.id);\n\t\t\t\t\t\t\t\t\t\ttmp = this.get_node(p, true);\n\t\t\t\t\t\t\t\t\t\tif(tmp && tmp.length) {\n\t\t\t\t\t\t\t\t\t\t\ttmp.children('.jstree-anchor').removeClass('jstree-clicked');\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\telse {\n\t\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tp = this.get_node(p.parent);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}, this));\n\t\t\t}\n\t\t};"}, {"tags": [{"type": "private", "string": ""}, {"type": "name", "string": "_undetermined()"}, {"type": "plugin", "string": "checkbox"}], "description": {"full": "<p>set the undetermined state where and if necessary. Used internally.</p>", "summary": "<p>set the undetermined state where and if necessary. Used internally.</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "this._undetermined = function () {\n\t\t\tvar i, j, m = this._model.data, s = this._data.core.selected, p = [], t = this;\n\t\t\tfor(i = 0, j = s.length; i < j; i++) {\n\t\t\t\tif(m[s[i]] && m[s[i]].parents) {\n\t\t\t\t\tp = p.concat(m[s[i]].parents);\n\t\t\t\t}\n\t\t\t}\n\t\t\t// attempt for server side undetermined state\n\t\t\tthis.element.find('.jstree-closed').not(':has(ul)')\n\t\t\t\t.each(function () {\n\t\t\t\t\tvar tmp = t.get_node(this);\n\t\t\t\t\tif(!tmp.state.loaded && tmp.original && tmp.original.state && tmp.original.state.undetermined && tmp.original.state.undetermined === true) {\n\t\t\t\t\t\tp.push(tmp.id);\n\t\t\t\t\t\tp = p.concat(tmp.parents);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\tp = $.vakata.array_unique(p);\n\t\t\ti = $.inArray('#', p);\n\t\t\tif(i !== -1) {\n\t\t\t\tp = $.vakata.array_remove(p, i);\n\t\t\t}\n\n\t\t\tthis.element.find('.jstree-undetermined').removeClass('jstree-undetermined');\n\t\t\tfor(i = 0, j = p.length; i < j; i++) {\n\t\t\t\tif(!m[p[i]].state.selected) {\n\t\t\t\t\ts = this.get_node(p[i], true);\n\t\t\t\t\tif(s && s.length) {\n\t\t\t\t\t\ts.children('a').children('.jstree-checkbox').addClass('jstree-undetermined');\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t};\n\t\tthis.redraw_node = function(obj, deep, is_callback) {\n\t\t\tobj = parent.redraw_node.call(this, obj, deep, is_callback);\n\t\t\tif(obj) {\n\t\t\t\tvar tmp = obj.getElementsByTagName('A')[0];\n\t\t\t\ttmp.insertBefore(_i.cloneNode(), tmp.childNodes[0]);\n\t\t\t}\n\t\t\tif(!is_callback && this.settings.checkbox.three_state) {\n\t\t\t\tif(this._data.checkbox.uto) { clearTimeout(this._data.checkbox.uto); }\n\t\t\t\tthis._data.checkbox.uto = setTimeout($.proxy(this._undetermined, this), 50);\n\t\t\t}\n\t\t\treturn obj;\n\t\t};\n\t\tthis.activate_node = function (obj, e) {\n\t\t\tif(this.settings.checkbox.whole_node || $(e.target).hasClass('jstree-checkbox')) {\n\t\t\t\te.ctrlKey = true;\n\t\t\t}\n\t\t\treturn parent.activate_node.call(this, obj, e);\n\t\t};", "ctx": {"type": "method", "receiver": "this", "name": "_undetermined", "string": "this._undetermined()"}}, {"tags": [{"type": "name", "string": "show_checkboxes()"}, {"type": "plugin", "string": "checkbox"}], "description": {"full": "<p>show the node checkbox icons</p>", "summary": "<p>show the node checkbox icons</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "this.show_checkboxes = function () { this._data.core.themes.checkboxes = true; this.element.children(\"ul\").removeClass(\"jstree-no-checkboxes\"); };", "ctx": {"type": "method", "receiver": "this", "name": "show_checkboxes", "string": "this.show_checkboxes()"}}, {"tags": [{"type": "name", "string": "hide_checkboxes()"}, {"type": "plugin", "string": "checkbox"}], "description": {"full": "<p>hide the node checkbox icons</p>", "summary": "<p>hide the node checkbox icons</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "this.hide_checkboxes = function () { this._data.core.themes.checkboxes = false; this.element.children(\"ul\").addClass(\"jstree-no-checkboxes\"); };", "ctx": {"type": "method", "receiver": "this", "name": "hide_checkboxes", "string": "this.hide_checkboxes()"}}, {"tags": [{"type": "name", "string": "toggle_checkboxes()"}, {"type": "plugin", "string": "checkbox"}], "description": {"full": "<p>toggle the node icons</p>", "summary": "<p>toggle the node icons</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "this.toggle_checkboxes = function () { if(this._data.core.themes.checkboxes) { this.hide_checkboxes(); } else { this.show_checkboxes(); } };\n\t};\n\n\t// include the checkbox plugin by default\n\t// $.jstree.defaults.plugins.push(\"checkbox\");", "ctx": {"type": "method", "receiver": "this", "name": "toggle_checkboxes", "string": "this.toggle_checkboxes()"}}, {"tags": [], "description": {"full": "<h3>Contextmenu plugin</h3>\n\n<p>Shows a context menu when a node is right-clicked.</p>", "summary": "<h3>Contextmenu plugin</h3>", "body": "<p>Shows a context menu when a node is right-clicked.</p>"}, "ignore": false, "code": "// TODO: move logic outside of function + check multiple move"}, {"tags": [{"type": "name", "string": "$.jstree.defaults.contextmenu"}, {"type": "plugin", "string": "contextmenu"}], "description": {"full": "<p>stores all defaults for the contextmenu plugin</p>", "summary": "<p>stores all defaults for the contextmenu plugin</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "$.jstree.defaults.contextmenu = {"}, {"tags": [{"type": "name", "string": "$.jstree.defaults.contextmenu.select_node"}, {"type": "plugin", "string": "contextmenu"}], "description": {"full": "<p>a boolean indicating if the node should be selected when the context menu is invoked on it. Defaults to <code>true</code>.</p>", "summary": "<p>a boolean indicating if the node should be selected when the context menu is invoked on it. Defaults to <code>true</code>.</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "select_node : true,"}, {"tags": [{"type": "name", "string": "$.jstree.defaults.contextmenu.show_at_node"}, {"type": "plugin", "string": "contextmenu"}], "description": {"full": "<p>a boolean indicating if the menu should be shown aligned with the node. Defaults to <code>true</code>, otherwise the mouse coordinates are used.</p>", "summary": "<p>a boolean indicating if the menu should be shown aligned with the node. Defaults to <code>true</code>, otherwise the mouse coordinates are used.</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "show_at_node : true,"}, {"tags": [{"type": "name", "string": "$.jstree.defaults.contextmenu.items"}, {"type": "plugin", "string": "contextmenu"}], "description": {"full": "<p>an object of actions, or a function that accepts a node and returns an object of actions available for that node.</p>\n\n<p>Each action consists of a key (a unique name) and a value which is an object with the following properties:</p>\n\n<ul>\n<li><code>separator_before</code> - a boolean indicating if there should be a separator before this item</li>\n<li><code>separator_after</code> - a boolean indicating if there should be a separator after this item</li>\n<li><code>_disabled</code> - a boolean indicating if this action should be disabled</li>\n<li><code>label</code> - a string - the name of the action</li>\n<li><code>action</code> - a function to be executed if this item is chosen</li>\n</ul>", "summary": "<p>an object of actions, or a function that accepts a node and returns an object of actions available for that node.</p>", "body": "<p>Each action consists of a key (a unique name) and a value which is an object with the following properties:</p>\n\n<ul>\n<li><code>separator_before</code> - a boolean indicating if there should be a separator before this item</li>\n<li><code>separator_after</code> - a boolean indicating if there should be a separator after this item</li>\n<li><code>_disabled</code> - a boolean indicating if this action should be disabled</li>\n<li><code>label</code> - a string - the name of the action</li>\n<li><code>action</code> - a function to be executed if this item is chosen</li>\n</ul>"}, "isPrivate": false, "ignore": false, "code": "items : function (o) { // Could be an object directly\n\t\t\treturn {\n\t\t\t\t\"create\" : {\n\t\t\t\t\t\"separator_before\"\t: false,\n\t\t\t\t\t\"separator_after\"\t: true,\n\t\t\t\t\t\"_disabled\"\t\t\t: false, //(this.check(\"create_node\", data.reference, {}, \"last\")),\n\t\t\t\t\t\"label\"\t\t\t\t: \"Create\",\n\t\t\t\t\t\"action\"\t\t\t: function (data) {\n\t\t\t\t\t\tvar inst = $.jstree.reference(data.reference),\n\t\t\t\t\t\t\tobj = inst.get_node(data.reference);\n\t\t\t\t\t\tinst.create_node(obj, {}, \"last\", function (new_node) {\n\t\t\t\t\t\t\tsetTimeout(function () { inst.edit(new_node); },0);\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\t\"rename\" : {\n\t\t\t\t\t\"separator_before\"\t: false,\n\t\t\t\t\t\"separator_after\"\t: false,\n\t\t\t\t\t\"_disabled\"\t\t\t: false, //(this.check(\"rename_node\", data.reference, this.get_parent(data.reference), \"\")),\n\t\t\t\t\t\"label\"\t\t\t\t: \"Rename\",\n\t\t\t\t\t\"action\"\t\t\t: function (data) {\n\t\t\t\t\t\tvar inst = $.jstree.reference(data.reference),\n\t\t\t\t\t\t\tobj = inst.get_node(data.reference);\n\t\t\t\t\t\tinst.edit(obj);\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\t\"remove\" : {\n\t\t\t\t\t\"separator_before\"\t: false,\n\t\t\t\t\t\"icon\"\t\t\t\t: false,\n\t\t\t\t\t\"separator_after\"\t: false,\n\t\t\t\t\t\"_disabled\"\t\t\t: false, //(this.check(\"delete_node\", data.reference, this.get_parent(data.reference), \"\")),\n\t\t\t\t\t\"label\"\t\t\t\t: \"Delete\",\n\t\t\t\t\t\"action\"\t\t\t: function (data) {\n\t\t\t\t\t\tvar inst = $.jstree.reference(data.reference),\n\t\t\t\t\t\t\tobj = inst.get_node(data.reference);\n\t\t\t\t\t\tif(inst.is_selected(obj)) {\n\t\t\t\t\t\t\tinst.delete_node(inst.get_selected());\n\t\t\t\t\t\t}\n\t\t\t\t\t\telse {\n\t\t\t\t\t\t\tinst.delete_node(obj);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\t\"ccp\" : {\n\t\t\t\t\t\"separator_before\"\t: true,\n\t\t\t\t\t\"icon\"\t\t\t\t: false,\n\t\t\t\t\t\"separator_after\"\t: false,\n\t\t\t\t\t\"label\"\t\t\t\t: \"Edit\",\n\t\t\t\t\t\"action\"\t\t\t: false,\n\t\t\t\t\t\"submenu\" : {\n\t\t\t\t\t\t\"cut\" : {\n\t\t\t\t\t\t\t\"separator_before\"\t: false,\n\t\t\t\t\t\t\t\"separator_after\"\t: false,\n\t\t\t\t\t\t\t\"label\"\t\t\t\t: \"Cut\",\n\t\t\t\t\t\t\t\"action\"\t\t\t: function (data) {\n\t\t\t\t\t\t\t\tvar inst = $.jstree.reference(data.reference),\n\t\t\t\t\t\t\t\t\tobj = inst.get_node(data.reference);\n\t\t\t\t\t\t\t\tif(inst.is_selected(obj)) {\n\t\t\t\t\t\t\t\t\tinst.cut(inst.get_selected());\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\telse {\n\t\t\t\t\t\t\t\t\tinst.cut(obj);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t},\n\t\t\t\t\t\t\"copy\" : {\n\t\t\t\t\t\t\t\"separator_before\"\t: false,\n\t\t\t\t\t\t\t\"icon\"\t\t\t\t: false,\n\t\t\t\t\t\t\t\"separator_after\"\t: false,\n\t\t\t\t\t\t\t\"label\"\t\t\t\t: \"Copy\",\n\t\t\t\t\t\t\t\"action\"\t\t\t: function (data) {\n\t\t\t\t\t\t\t\tvar inst = $.jstree.reference(data.reference),\n\t\t\t\t\t\t\t\t\tobj = inst.get_node(data.reference);\n\t\t\t\t\t\t\t\tif(inst.is_selected(obj)) {\n\t\t\t\t\t\t\t\t\tinst.copy(inst.get_selected());\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\telse {\n\t\t\t\t\t\t\t\t\tinst.copy(obj);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t},\n\t\t\t\t\t\t\"paste\" : {\n\t\t\t\t\t\t\t\"separator_before\"\t: false,\n\t\t\t\t\t\t\t\"icon\"\t\t\t\t: false,\n\t\t\t\t\t\t\t\"_disabled\"\t\t\t: !(this.can_paste()),\n\t\t\t\t\t\t\t\"separator_after\"\t: false,\n\t\t\t\t\t\t\t\"label\"\t\t\t\t: \"Paste\",\n\t\t\t\t\t\t\t\"action\"\t\t\t: function (data) {\n\t\t\t\t\t\t\t\tvar inst = $.jstree.reference(data.reference),\n\t\t\t\t\t\t\t\t\tobj = inst.get_node(data.reference);\n\t\t\t\t\t\t\t\tinst.paste(obj);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t};\n\t\t}\n\t};\n\n\t$.jstree.plugins.contextmenu = function (options, parent) {\n\t\tthis.bind = function () {\n\t\t\tparent.bind.call(this);\n\n\t\t\tthis.element\n\t\t\t\t.on(\"contextmenu.jstree\", \"a\", $.proxy(function (e) {\n\t\t\t\t\t\te.preventDefault();\n\t\t\t\t\t\tif(!this.is_loading(e.currentTarget)) {\n\t\t\t\t\t\t\tthis.show_contextmenu(e.currentTarget, e.pageX, e.pageY);\n\t\t\t\t\t\t}\n\t\t\t\t\t}, this))\n\t\t\t\t.on(\"click.jstree\", \"a\", $.proxy(function (e) {\n\t\t\t\t\t\tif(this._data.contextmenu.visible) {\n\t\t\t\t\t\t\t$.vakata.context.hide();\n\t\t\t\t\t\t}\n\t\t\t\t\t}, this));\n\t\t\t$(document).on(\"context_hide.vakata\", $.proxy(function () { this._data.contextmenu.visible = false; }, this));\n\t\t};\n\t\tthis.teardown = function () {\n\t\t\tif(this._data.contextmenu.visible) {\n\t\t\t\t$.vakata.context.hide();\n\t\t\t}\n\t\t\tparent.teardown.call(this);\n\t\t};"}, {"tags": [{"type": "name", "string": "show_contextmenu(obj [, x, y])"}, {"type": "param", "types": ["mixed"], "name": "obj", "description": "the node"}, {"type": "param", "types": ["Number"], "name": "x", "description": "the x-coordinate relative to the document to show the menu at"}, {"type": "param", "types": ["Number"], "name": "y", "description": "the y-coordinate relative to the document to show the menu at"}, {"type": "plugin", "string": "contextmenu"}, {"type": "trigger", "string": "show_contextmenu.jstree"}], "description": {"full": "<p>show the context menu for a node</p>", "summary": "<p>show the context menu for a node</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "this.show_contextmenu = function (obj, x, y) {\n\t\t\tobj = this.get_node(obj);\n\t\t\tif(!obj || obj.id === '#') { return false; }\n\t\t\tvar s = this.settings.contextmenu,\n\t\t\t\td = this.get_node(obj, true),\n\t\t\t\ta = d.children(\".jstree-anchor\"),\n\t\t\t\to = false,\n\t\t\t\ti = false;\n\t\t\tif(s.show_at_node || x === undefined || y === undefined) {\n\t\t\t\to = a.offset();\n\t\t\t\tx = o.left;\n\t\t\t\ty = o.top + this._data.core.li_height;\n\t\t\t}\n\t\t\tif(this.settings.contextmenu.select_node && !this.is_selected(obj)) {\n\t\t\t\tthis.deselect_all();\n\t\t\t\tthis.select_node(obj);\n\t\t\t}\n\n\t\t\ti = s.items;\n\t\t\tif($.isFunction(i)) { i = i.call(this, obj); }\n\n\t\t\t$(document).one(\"context_show.vakata\", $.proxy(function (e, data) {\n\t\t\t\tvar cls = 'jstree-contextmenu jstree-' + this.get_theme() + '-contextmenu';\n\t\t\t\t$(data.element).addClass(cls);\n\t\t\t}, this));\n\t\t\tthis._data.contextmenu.visible = true;\n\t\t\t$.vakata.context.show(a, { 'x' : x, 'y' : y }, i);", "ctx": {"type": "method", "receiver": "this", "name": "show_contextmenu", "string": "this.show_contextmenu()"}}, {"tags": [{"type": "event", "string": ""}, {"type": "name", "string": "show_contextmenu.jstree"}, {"type": "param", "types": ["Object"], "name": "node", "description": "the node"}, {"type": "param", "types": ["Number"], "name": "x", "description": "the x-coordinate of the menu relative to the document"}, {"type": "param", "types": ["Number"], "name": "y", "description": "the y-coordinate of the menu relative to the document"}, {"type": "plugin", "string": "contextmenu"}], "description": {"full": "<p>triggered when the contextmenu is shown for a node</p>", "summary": "<p>triggered when the contextmenu is shown for a node</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "this.trigger('show_contextmenu', { \"node\" : obj, \"x\" : x, \"y\" : y });\n\t\t};\n\t};\n\n\t// contextmenu helper\n\t(function ($) {\n\t\tvar right_to_left = false,\n\t\t\tvakata_context = {\n\t\t\t\telement\t\t: false,\n\t\t\t\treference\t: false,\n\t\t\t\tposition_x\t: 0,\n\t\t\t\tposition_y\t: 0,\n\t\t\t\titems\t\t: [],\n\t\t\t\thtml\t\t: \"\",\n\t\t\t\tis_visible\t: false\n\t\t\t};\n\n\t\t$.vakata.context = {\n\t\t\tsettings : {\n\t\t\t\thide_onmouseleave\t: 0,\n\t\t\t\ticons\t\t\t\t: true\n\t\t\t},\n\t\t\t_trigger : function (event_name) {\n\t\t\t\t$(document).triggerHandler(\"context_\" + event_name + \".vakata\", {\n\t\t\t\t\t\"reference\"\t: vakata_context.reference,\n\t\t\t\t\t\"element\"\t: vakata_context.element,\n\t\t\t\t\t\"position\"\t: {\n\t\t\t\t\t\t\"x\" : vakata_context.position_x,\n\t\t\t\t\t\t\"y\" : vakata_context.position_y\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t_execute : function (i) {\n\t\t\t\ti = vakata_context.items[i];\n\t\t\t\treturn i && !i._disabled && i.action ? i.action.call(null, {\n\t\t\t\t\t\t\t\"item\"\t\t: i,\n\t\t\t\t\t\t\t\"reference\"\t: vakata_context.reference,\n\t\t\t\t\t\t\t\"element\"\t: vakata_context.element,\n\t\t\t\t\t\t\t\"position\"\t: {\n\t\t\t\t\t\t\t\t\"x\" : vakata_context.position_x,\n\t\t\t\t\t\t\t\t\"y\" : vakata_context.position_y\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}) : false;\n\t\t\t},\n\t\t\t_parse : function (o, is_callback) {\n\t\t\t\tif(!o) { return false; }\n\t\t\t\tif(!is_callback) {\n\t\t\t\t\tvakata_context.html\t\t= \"\";\n\t\t\t\t\tvakata_context.items\t= [];\n\t\t\t\t}\n\t\t\t\tvar str = \"\",\n\t\t\t\t\tsep = false,\n\t\t\t\t\ttmp;\n\n\t\t\t\tif(is_callback) { str += \"<\"+\"ul>\"; }\n\t\t\t\t$.each(o, function (i, val) {\n\t\t\t\t\tif(!val) { return true; }\n\t\t\t\t\tvakata_context.items.push(val);\n\t\t\t\t\tif(!sep && val.separator_before) {\n\t\t\t\t\t\tstr += \"<\"+\"li class='vakata-context-separator'><\"+\"a href='#' \" + ($.vakata.context.settings.icons ? '' : 'style=\"margin-left:0px;\"') + \">&#160;<\"+\"/a><\"+\"/li>\";\n\t\t\t\t\t}\n\t\t\t\t\tsep = false;\n\t\t\t\t\tstr += \"<\"+\"li class='\" + (val._class || \"\") + (val._disabled ? \" vakata-contextmenu-disabled \" : \"\") + \"'>\";\n\t\t\t\t\tstr += \"<\"+\"a href='#' rel='\" + (vakata_context.items.length - 1) + \"'>\";\n\t\t\t\t\tif($.vakata.context.settings.icons) {\n\t\t\t\t\t\tstr += \"<\"+\"ins \";\n\t\t\t\t\t\tif(val.icon) {\n\t\t\t\t\t\t\tif(val.icon.indexOf(\"/\") !== -1 || val.icon.indexOf(\".\") !== -1) { str += \" style='background:url(\\\"\" + val.icon + \"\\\") center center no-repeat' \"; }\n\t\t\t\t\t\t\telse { str += \" class='\" + val.icon + \"' \"; }\n\t\t\t\t\t\t}\n\t\t\t\t\t\tstr += \">&#160;<\"+\"/ins><\"+\"span>&#160;<\"+\"/span>\";\n\t\t\t\t\t}\n\t\t\t\t\tstr += val.label + \"<\"+\"/a>\";\n\t\t\t\t\tif(val.submenu) {\n\t\t\t\t\t\ttmp = $.vakata.context._parse(val.submenu, true);\n\t\t\t\t\t\tif(tmp) { str += tmp; }\n\t\t\t\t\t}\n\t\t\t\t\tstr += \"<\"+\"/li>\";\n\t\t\t\t\tif(val.separator_after) {\n\t\t\t\t\t\tstr += \"<\"+\"li class='vakata-context-separator'><\"+\"a href='#' \" + ($.vakata.context.settings.icons ? '' : 'style=\"margin-left:0px;\"') + \">&#160;<\"+\"/a><\"+\"/li>\";\n\t\t\t\t\t\tsep = true;\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\tstr  = str.replace(/<li class\\='vakata-context-separator'\\><\\/li\\>$/,\"\");\n\t\t\t\tif(is_callback) { str += \"</ul>\"; }\n\t\t\t\tif(!is_callback) { vakata_context.html = str; $.vakata.context._trigger(\"parse\"); }\n\t\t\t\treturn str.length > 10 ? str : false;\n\t\t\t},\n\t\t\t_show_submenu : function (o) {\n\t\t\t\to = $(o);\n\t\t\t\tif(!o.length || !o.children(\"ul\").length) { return; }\n\t\t\t\tvar e = o.children(\"ul\"),\n\t\t\t\t\tx = o.offset().left + o.outerWidth(),\n\t\t\t\t\ty = o.offset().top,\n\t\t\t\t\tw = e.width(),\n\t\t\t\t\th = e.height(),\n\t\t\t\t\tdw = $(window).width() + $(window).scrollLeft(),\n\t\t\t\t\tdh = $(window).height() + $(window).scrollTop();\n\t\t\t\t// може да се спести е една проверка - дали няма някой от класовете вече нагоре\n\t\t\t\tif(right_to_left) {\n\t\t\t\t\to[x - (w + 10 + o.outerWidth()) < 0 ? \"addClass\" : \"removeClass\"](\"vakata-context-left\");\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\to[x + w + 10 > dw ? \"addClass\" : \"removeClass\"](\"vakata-context-right\");\n\t\t\t\t}\n\t\t\t\tif(y + h + 10 > dh) {\n\t\t\t\t\te.css(\"bottom\",\"-1px\");\n\t\t\t\t}\n\t\t\t\te.show();\n\t\t\t},\n\t\t\tshow : function (reference, position, data) {\n\t\t\t\tvar o, e, x, y, w, h, dw, dh, cond = true;\n\t\t\t\tif(vakata_context.element && vakata_context.element.length) {\n\t\t\t\t\tvakata_context.element.width('');\n\t\t\t\t}\n\t\t\t\tswitch(cond) {\n\t\t\t\t\tcase (!position && !reference):\n\t\t\t\t\t\treturn false;\n\t\t\t\t\tcase (!!position && !!reference):\n\t\t\t\t\t\tvakata_context.reference\t= reference;\n\t\t\t\t\t\tvakata_context.position_x\t= position.x;\n\t\t\t\t\t\tvakata_context.position_y\t= position.y;\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase (!position && !!reference):\n\t\t\t\t\t\tvakata_context.reference\t= reference;\n\t\t\t\t\t\to = reference.offset();\n\t\t\t\t\t\tvakata_context.position_x\t= o.left + reference.outerHeight();\n\t\t\t\t\t\tvakata_context.position_y\t= o.top;\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase (!!position && !reference):\n\t\t\t\t\t\tvakata_context.position_x\t= position.x;\n\t\t\t\t\t\tvakata_context.position_y\t= position.y;\n\t\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t\tif(!!reference && !data && $(reference).data('vakata_contextmenu')) {\n\t\t\t\t\tdata = $(reference).data('vakata_contextmenu');\n\t\t\t\t}\n\t\t\t\tif($.vakata.context._parse(data)) {\n\t\t\t\t\tvakata_context.element.html(vakata_context.html);\n\t\t\t\t}\n\t\t\t\tif(vakata_context.items.length) {\n\t\t\t\t\te = vakata_context.element;\n\t\t\t\t\tx = vakata_context.position_x;\n\t\t\t\t\ty = vakata_context.position_y;\n\t\t\t\t\tw = e.width();\n\t\t\t\t\th = e.height();\n\t\t\t\t\tdw = $(window).width() + $(window).scrollLeft();\n\t\t\t\t\tdh = $(window).height() + $(window).scrollTop();\n\t\t\t\t\tif(right_to_left) {\n\t\t\t\t\t\tx -= e.outerWidth();\n\t\t\t\t\t\tif(x < $(window).scrollLeft() + 20) {\n\t\t\t\t\t\t\tx = $(window).scrollLeft() + 20;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tif(x + w + 20 > dw) {\n\t\t\t\t\t\tx = dw - (w + 20);\n\t\t\t\t\t}\n\t\t\t\t\tif(y + h + 20 > dh) {\n\t\t\t\t\t\ty = dh - (h + 20);\n\t\t\t\t\t}\n\n\t\t\t\t\tvakata_context.element\n\t\t\t\t\t\t.css({ \"left\" : x, \"top\" : y })\n\t\t\t\t\t\t.show()\n\t\t\t\t\t\t.find('a:eq(0)').focus().parent().addClass(\"vakata-context-hover\");\n\t\t\t\t\tvakata_context.is_visible = true;\n\t\t\t\t\t$.vakata.context._trigger(\"show\");\n\t\t\t\t}\n\t\t\t},\n\t\t\thide : function () {\n\t\t\t\tif(vakata_context.is_visible) {\n\t\t\t\t\tvakata_context.element.hide().find(\"ul\").hide().end().find(':focus').blur();\n\t\t\t\t\tvakata_context.is_visible = false;\n\t\t\t\t\t$.vakata.context._trigger(\"hide\");\n\t\t\t\t}\n\t\t\t}\n\t\t};\n\t\t$(function () {\n\t\t\tright_to_left = $(\"body\").css(\"direction\") === \"rtl\";\n\t\t\tvar to = false;\n\n\t\t\tvakata_context.element = $(\"<ul class='vakata-context'></ul>\");\n\t\t\tvakata_context.element\n\t\t\t\t.on(\"mouseenter\", \"li\", function (e) {\n\t\t\t\t\te.stopImmediatePropagation();\n\n\t\t\t\t\tif($.contains(this, e.relatedTarget)) {\n\t\t\t\t\t\t// премахнато заради delegate mouseleave по-долу\n\t\t\t\t\t\t// $(this).find(\".vakata-context-hover\").removeClass(\"vakata-context-hover\");\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\n\t\t\t\t\tif(to) { clearTimeout(to); }\n\t\t\t\t\tvakata_context.element.find(\".vakata-context-hover\").removeClass(\"vakata-context-hover\").end();\n\n\t\t\t\t\t$(this)\n\t\t\t\t\t\t.siblings().find(\"ul\").hide().end().end()\n\t\t\t\t\t\t.parentsUntil(\".vakata-context\", \"li\").addBack().addClass(\"vakata-context-hover\");\n\t\t\t\t\t$.vakata.context._show_submenu(this);\n\t\t\t\t})\n\t\t\t\t// тестово - дали не натоварва?\n\t\t\t\t.on(\"mouseleave\", \"li\", function (e) {\n\t\t\t\t\tif($.contains(this, e.relatedTarget)) { return; }\n\t\t\t\t\t$(this).find(\".vakata-context-hover\").addBack().removeClass(\"vakata-context-hover\");\n\t\t\t\t})\n\t\t\t\t.on(\"mouseleave\", function (e) {\n\t\t\t\t\t$(this).find(\".vakata-context-hover\").removeClass(\"vakata-context-hover\");\n\t\t\t\t\tif($.vakata.context.settings.hide_onmouseleave) {\n\t\t\t\t\t\tto = setTimeout(\n\t\t\t\t\t\t\t(function (t) {\n\t\t\t\t\t\t\t\treturn function () { $.vakata.context.hide(); };\n\t\t\t\t\t\t\t}(this)), $.vakata.context.settings.hide_onmouseleave);\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t\t.on(\"click\", \"a\", function (e) {\n\t\t\t\t\te.preventDefault();\n\t\t\t\t})\n\t\t\t\t.on(\"mouseup\", \"a\", function (e) {\n\t\t\t\t\tif(!$(this).blur().parent().hasClass(\"vakata-context-disabled\") && $.vakata.context._execute($(this).attr(\"rel\")) !== false) {\n\t\t\t\t\t\t$.vakata.context.hide();\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t\t.on('keydown', 'a', function (e) {\n\t\t\t\t\t\tvar o = null;\n\t\t\t\t\t\tswitch(e.which) {\n\t\t\t\t\t\t\tcase 13:\n\t\t\t\t\t\t\tcase 32:\n\t\t\t\t\t\t\t\te.type = \"mouseup\";\n\t\t\t\t\t\t\t\te.preventDefault();\n\t\t\t\t\t\t\t\t$(e.currentTarget).trigger(e);\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\tcase 37:\n\t\t\t\t\t\t\t\tif(vakata_context.is_visible) {\n\t\t\t\t\t\t\t\t\tvakata_context.element.find(\".vakata-context-hover\").last().parents(\"li:eq(0)\").find(\"ul\").hide().find(\".vakata-context-hover\").removeClass(\"vakata-context-hover\").end().end().children('a').focus();\n\t\t\t\t\t\t\t\t\te.stopImmediatePropagation();\n\t\t\t\t\t\t\t\t\te.preventDefault();\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\tcase 38:\n\t\t\t\t\t\t\t\tif(vakata_context.is_visible) {\n\t\t\t\t\t\t\t\t\to = vakata_context.element.find(\"ul:visible\").addBack().last().children(\".vakata-context-hover\").removeClass(\"vakata-context-hover\").prevAll(\"li:not(.vakata-context-separator)\").first();\n\t\t\t\t\t\t\t\t\tif(!o.length) { o = vakata_context.element.find(\"ul:visible\").addBack().last().children(\"li:not(.vakata-context-separator)\").last(); }\n\t\t\t\t\t\t\t\t\to.addClass(\"vakata-context-hover\").children('a').focus();\n\t\t\t\t\t\t\t\t\te.stopImmediatePropagation();\n\t\t\t\t\t\t\t\t\te.preventDefault();\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\tcase 39:\n\t\t\t\t\t\t\t\tif(vakata_context.is_visible) {\n\t\t\t\t\t\t\t\t\tvakata_context.element.find(\".vakata-context-hover\").last().children(\"ul\").show().children(\"li:not(.vakata-context-separator)\").removeClass(\"vakata-context-hover\").first().addClass(\"vakata-context-hover\").children('a').focus();\n\t\t\t\t\t\t\t\t\te.stopImmediatePropagation();\n\t\t\t\t\t\t\t\t\te.preventDefault();\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\tcase 40:\n\t\t\t\t\t\t\t\tif(vakata_context.is_visible) {\n\t\t\t\t\t\t\t\t\to = vakata_context.element.find(\"ul:visible\").addBack().last().children(\".vakata-context-hover\").removeClass(\"vakata-context-hover\").nextAll(\"li:not(.vakata-context-separator)\").first();\n\t\t\t\t\t\t\t\t\tif(!o.length) { o = vakata_context.element.find(\"ul:visible\").addBack().last().children(\"li:not(.vakata-context-separator)\").first(); }\n\t\t\t\t\t\t\t\t\to.addClass(\"vakata-context-hover\").children('a').focus();\n\t\t\t\t\t\t\t\t\te.stopImmediatePropagation();\n\t\t\t\t\t\t\t\t\te.preventDefault();\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\tcase 27:\n\t\t\t\t\t\t\t\t$.vakata.context.hide();\n\t\t\t\t\t\t\t\te.preventDefault();\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\t\t//console.log(e.which);\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t.appendTo(\"body\");\n\n\t\t\t$(document)\n\t\t\t\t.on(\"mousedown\", function (e) {\n\t\t\t\t\tif(vakata_context.is_visible && !$.contains(vakata_context.element[0], e.target)) { $.vakata.context.hide(); }\n\t\t\t\t})\n\t\t\t\t.on(\"context_show.vakata\", function (e, data) {\n\t\t\t\t\tvakata_context.element.find(\"li:has(ul)\").children(\"a\").addClass(\"vakata-context-parent\");\n\t\t\t\t\tif(right_to_left) {\n\t\t\t\t\t\tvakata_context.element.addClass(\"vakata-context-rtl\").css(\"direction\", \"rtl\");\n\t\t\t\t\t}\n\t\t\t\t\t// also apply a RTL class?\n\t\t\t\t\tvakata_context.element.find(\"ul\").hide().end();\n\t\t\t\t});\n\t\t});\n\t}($));\n\t// $.jstree.defaults.plugins.push(\"contextmenu\");"}, {"tags": [], "description": {"full": "<h3>Drag'n'drop plugin</h3>\n\n<p>Enables dragging and dropping of nodes in the tree, resulting in a move or copy operations.</p>", "summary": "<h3>Drag'n'drop plugin</h3>", "body": "<p>Enables dragging and dropping of nodes in the tree, resulting in a move or copy operations.</p>"}, "ignore": false}, {"tags": [{"type": "name", "string": "$.jstree.defaults.dnd"}, {"type": "plugin", "string": "dnd"}], "description": {"full": "<p>stores all defaults for the drag'n'drop plugin</p>", "summary": "<p>stores all defaults for the drag'n'drop plugin</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "$.jstree.defaults.dnd = {"}, {"tags": [{"type": "name", "string": "$.jstree.defaults.dnd.copy"}, {"type": "plugin", "string": "dnd"}], "description": {"full": "<p>a boolean indicating if a copy should be possible while dragging (by pressint the meta key or Ctrl). Defaults to <code>true</code>.</p>", "summary": "<p>a boolean indicating if a copy should be possible while dragging (by pressint the meta key or Ctrl). Defaults to <code>true</code>.</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "copy : true,"}, {"tags": [{"type": "name", "string": "$.jstree.defaults.dnd.open_timeout"}, {"type": "plugin", "string": "dnd"}], "description": {"full": "<p>a number indicating how long a node should remain hovered while dragging to be opened. Defaults to <code>500</code>.</p>", "summary": "<p>a number indicating how long a node should remain hovered while dragging to be opened. Defaults to <code>500</code>.</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "open_timeout : 500\n\t};\n\t// TODO: now check works by checking for each node individually, how about max_children, unique, etc?\n\t// TODO: drop somewhere else - maybe demo only?\n\t$.jstree.plugins.dnd = function (options, parent) {\n\t\tthis.bind = function () {\n\t\t\tparent.bind.call(this);\n\n\t\t\tthis.element\n\t\t\t\t.on('mousedown', 'a', $.proxy(function (e) {\n\t\t\t\t\tvar obj = this.get_node(e.target),\n\t\t\t\t\t\tmlt = this.is_selected(obj) ? this.get_selected().length : 1;\n\t\t\t\t\tif(obj && obj.id && obj.id !== \"#\" && e.which === 1) {\n\t\t\t\t\t\tthis.element.trigger('mousedown.jstree');\n\t\t\t\t\t\treturn $.vakata.dnd.start(e, { 'jstree' : true, 'origin' : this, 'obj' : this.get_node(obj,true), 'nodes' : mlt > 1 ? this.get_selected() : [obj.id] }, '<div id=\"jstree-dnd\" class=\"jstree-' + this.get_theme() + '\"><i class=\"jstree-icon jstree-er\"></i>' + (mlt > 1 ? mlt + ' ' + this.get_string('nodes') : this.get_text(e.currentTarget, true)) + '<ins class=\"jstree-copy\" style=\"display:none;\">+</ins></div>');\n\t\t\t\t\t}\n\t\t\t\t}, this));\n\t\t};\n\t};\n\n\t$(function() {\n\t\t// bind only once for all instances\n\t\tvar lastmv = false,\n\t\t\topento = false,\n\t\t\tmarker = $('<div id=\"jstree-marker\">&#160;</div>').hide().appendTo('body');\n\n\t\t$(document)\n\t\t\t.bind('dnd_start.vakata', function (e, data) {\n\t\t\t\tlastmv = false;\n\t\t\t})\n\t\t\t.bind('dnd_move.vakata', function (e, data) {\n\t\t\t\tif(opento) { clearTimeout(opento); }\n\t\t\t\tif(!data.data.jstree) { return; }\n\n\t\t\t\t// if we are hovering the marker image do nothing (can happen on \"inside\" drags)\n\t\t\t\tif(data.event.target.id && data.event.target.id === 'jstree-marker') {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tvar ins = $.jstree.reference(data.event.target),\n\t\t\t\t\tref = false,\n\t\t\t\t\toff = false,\n\t\t\t\t\trel = false,\n\t\t\t\t\tl, t, h, p, i, o, ok, t1, t2, op, ps, pr;\n\t\t\t\t// if we are over an instance\n\t\t\t\tif(ins && ins._data && ins._data.dnd) {\n\t\t\t\t\tmarker.attr('class', 'jstree-' + ins.get_theme());\n\t\t\t\t\tdata.helper\n\t\t\t\t\t\t.children().attr('class', 'jstree-' + ins.get_theme())\n\t\t\t\t\t\t.find('.jstree-copy:eq(0)')[ data.data.origin && data.data.origin.settings.dnd.copy && (data.event.metaKey || data.event.ctrlKey) ? 'show' : 'hide' ]();\n\n\n\t\t\t\t\t// if are hovering the container itself add a new root node\n\t\t\t\t\tif( (data.event.target === ins.element[0] || data.event.target === ins.get_container_ul()[0]) && ins.get_container_ul().children().length === 0) {\n\t\t\t\t\t\tok = true;\n\t\t\t\t\t\tfor(t1 = 0, t2 = data.data.nodes.length; t1 < t2; t1++) {\n\t\t\t\t\t\t\tok = ok && ins.check( (data.data.origin && data.data.origin.settings.dnd.copy && (data.event.metaKey || data.event.ctrlKey) ? \"copy_node\" : \"move_node\"), data.data.nodes[t1], '#', 'last');\n\t\t\t\t\t\t\tif(!ok) { break; }\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif(ok) {\n\t\t\t\t\t\t\tlastmv = { 'ins' : ins, 'par' : '#', 'pos' : 'last' };\n\t\t\t\t\t\t\tmarker.hide();\n\t\t\t\t\t\t\tdata.helper.find('.jstree-icon:eq(0)').removeClass('jstree-er').addClass('jstree-ok');\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\t// if we are hovering a tree node\n\t\t\t\t\t\tref = $(data.event.target).closest('a');\n\t\t\t\t\t\tif(ref && ref.length && ref.parent().is('.jstree-closed, .jstree-open, .jstree-leaf')) {\n\t\t\t\t\t\t\toff = ref.offset();\n\t\t\t\t\t\t\trel = data.event.pageY - off.top;\n\t\t\t\t\t\t\th = ref.height();\n\t\t\t\t\t\t\tif(rel < h / 3) {\n\t\t\t\t\t\t\t\to = ['b', 'i', 'a'];\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\telse if(rel > h - h / 3) {\n\t\t\t\t\t\t\t\to = ['a', 'i', 'b'];\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\telse {\n\t\t\t\t\t\t\t\to = rel > h / 2 ? ['i', 'a', 'b'] : ['i', 'b', 'a'];\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t$.each(o, function (j, v) {\n\t\t\t\t\t\t\t\tswitch(v) {\n\t\t\t\t\t\t\t\t\tcase 'b':\n\t\t\t\t\t\t\t\t\t\tl = off.left - 6;\n\t\t\t\t\t\t\t\t\t\tt = off.top - 5;\n\t\t\t\t\t\t\t\t\t\tp = ins.get_parent(ref);\n\t\t\t\t\t\t\t\t\t\ti = ref.parent().index();\n\t\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\t\tcase 'i':\n\t\t\t\t\t\t\t\t\t\tl = off.left - 2;\n\t\t\t\t\t\t\t\t\t\tt = off.top - 5 + h / 2 + 1;\n\t\t\t\t\t\t\t\t\t\tp = ref.parent();\n\t\t\t\t\t\t\t\t\t\ti = 0;\n\t\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\t\tcase 'a':\n\t\t\t\t\t\t\t\t\t\tl = off.left - 6;\n\t\t\t\t\t\t\t\t\t\tt = off.top - 5 + h;\n\t\t\t\t\t\t\t\t\t\tp = ins.get_parent(ref);\n\t\t\t\t\t\t\t\t\t\ti = ref.parent().index() + 1;\n\t\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\t}"}, {"tags": [], "description": {"full": "<p>// TODO: moving inside, but the node is not yet loaded?<br />                                // the check will work anyway, as when moving the node will be loaded first and checked again<br />                                if(v === 'i' &amp;&amp; !ins.is_loaded(p)) { }</p>", "summary": "<p>// TODO: moving inside, but the node is not yet loaded?<br />                                // the check will work anyway, as when moving the node will be loaded first and checked again<br />                                if(v === 'i' &amp;&amp; !ins.is_loaded(p)) { }</p>", "body": ""}, "ignore": true, "code": "ok = true;\n\t\t\t\t\t\t\t\tfor(t1 = 0, t2 = data.data.nodes.length; t1 < t2; t1++) {\n\t\t\t\t\t\t\t\t\top = data.data.origin && data.data.origin.settings.dnd.copy && (data.event.metaKey || data.event.ctrlKey) ? \"copy_node\" : \"move_node\";\n\t\t\t\t\t\t\t\t\tps = i;\n\t\t\t\t\t\t\t\t\tif(op === \"move_node\" && v === 'a' && (data.data.origin && data.data.origin === ins) && p === ins.get_parent(data.data.nodes[t1])) {\n\t\t\t\t\t\t\t\t\t\tpr = ins.get_node(p);\n\t\t\t\t\t\t\t\t\t\tif(ps > $.inArray(data.data.nodes[t1], pr.children)) {\n\t\t\t\t\t\t\t\t\t\t\tps -= 1;\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\tok = ok && ins.check(op, data.data.nodes[t1], p, ps);\n\t\t\t\t\t\t\t\t\tif(!ok) { break; }\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tif(ok) {\n\t\t\t\t\t\t\t\t\tif(v === 'i' && ref.parent().is('.jstree-closed') && ins.settings.dnd.open_timeout) {\n\t\t\t\t\t\t\t\t\t\topento = setTimeout((function (x, z) { return function () { x.open_node(z); }; }(ins, ref)), ins.settings.dnd.open_timeout);\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\tlastmv = { 'ins' : ins, 'par' : p, 'pos' : i };\n\t\t\t\t\t\t\t\t\tmarker.css({ 'left' : l + 'px', 'top' : t + 'px' }).show();\n\t\t\t\t\t\t\t\t\tdata.helper.find('.jstree-icon:eq(0)').removeClass('jstree-er').addClass('jstree-ok');\n\t\t\t\t\t\t\t\t\to = true;\n\t\t\t\t\t\t\t\t\treturn false;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\tif(o === true) { return; }\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tlastmv = false;\n\t\t\t\tdata.helper.find('.jstree-icon').removeClass('jstree-ok').addClass('jstree-er');\n\t\t\t\tmarker.hide();\n\t\t\t})\n\t\t\t.bind('dnd_scroll.vakata', function (e, data) {\n\t\t\t\tif(!data.data.jstree) { return; }\n\t\t\t\tmarker.hide();\n\t\t\t\tlastmv = false;\n\t\t\t\tdata.helper.find('.jstree-icon:eq(0)').removeClass('jstree-ok').addClass('jstree-er');\n\t\t\t})\n\t\t\t.bind('dnd_stop.vakata', function (e, data) {\n\t\t\t\tif(opento) { clearTimeout(opento); }\n\t\t\t\tif(!data.data.jstree) { return; }\n\t\t\t\tmarker.hide();\n\t\t\t\tif(lastmv) {\n\t\t\t\t\tvar i, j, nodes = [];\n\t\t\t\t\tfor(i = 0, j = data.data.nodes.length; i < j; i++) {\n\t\t\t\t\t\tnodes[i] = data.data.origin ? data.data.origin.get_node(data.data.nodes[i]) : data.data.nodes[i];\n\t\t\t\t\t}\n\t\t\t\t\tlastmv.ins[ data.data.origin && data.data.origin.settings.dnd.copy && (data.event.metaKey || data.event.ctrlKey) ? 'copy_node' : 'move_node' ](nodes, lastmv.par, lastmv.pos);\n\t\t\t\t}\n\t\t\t})\n\t\t\t.bind('keyup keydown', function (e, data) {\n\t\t\t\tdata = $.vakata.dnd._get();\n\t\t\t\tif(data.data && data.data.jstree) {\n\t\t\t\t\tdata.helper.find('.jstree-copy:eq(0)')[ data.data.origin && data.data.origin.settings.dnd.copy && (e.metaKey || e.ctrlKey) ? 'show' : 'hide' ]();\n\t\t\t\t}\n\t\t\t});\n\t});\n\n\t// helpers\n\t(function ($) {\n\t\t$.fn.vakata_reverse = [].reverse;\n\t\t// private variable\n\t\tvar vakata_dnd = {\n\t\t\telement\t: false,\n\t\t\tis_down\t: false,\n\t\t\tis_drag\t: false,\n\t\t\thelper\t: false,\n\t\t\thelper_w: 0,\n\t\t\tdata\t: false,\n\t\t\tinit_x\t: 0,\n\t\t\tinit_y\t: 0,\n\t\t\tscroll_l: 0,\n\t\t\tscroll_t: 0,\n\t\t\tscroll_e: false,\n\t\t\tscroll_i: false\n\t\t};\n\t\t$.vakata.dnd = {\n\t\t\tsettings : {\n\t\t\t\tscroll_speed\t\t: 10,\n\t\t\t\tscroll_proximity\t: 20,\n\t\t\t\thelper_left\t\t\t: 5,\n\t\t\t\thelper_top\t\t\t: 10,\n\t\t\t\tthreshold\t\t\t: 5\n\t\t\t},\n\t\t\t_trigger : function (event_name, e) {\n\t\t\t\tvar data = $.vakata.dnd._get();\n\t\t\t\tdata.event = e;\n\t\t\t\t$(document).triggerHandler(\"dnd_\" + event_name + \".vakata\", data);\n\t\t\t},\n\t\t\t_get : function () {\n\t\t\t\treturn {\n\t\t\t\t\t\"data\"\t\t: vakata_dnd.data,\n\t\t\t\t\t\"element\"\t: vakata_dnd.element,\n\t\t\t\t\t\"helper\"\t: vakata_dnd.helper\n\t\t\t\t};\n\t\t\t},\n\t\t\t_clean : function () {\n\t\t\t\tif(vakata_dnd.helper) { vakata_dnd.helper.remove(); }\n\t\t\t\tif(vakata_dnd.scroll_i) { clearInterval(vakata_dnd.scroll_i); vakata_dnd.scroll_i = false; }\n\t\t\t\tvakata_dnd = {\n\t\t\t\t\telement\t: false,\n\t\t\t\t\tis_down\t: false,\n\t\t\t\t\tis_drag\t: false,\n\t\t\t\t\thelper\t: false,\n\t\t\t\t\thelper_w: 0,\n\t\t\t\t\tdata\t: false,\n\t\t\t\t\tinit_x\t: 0,\n\t\t\t\t\tinit_y\t: 0,\n\t\t\t\t\tscroll_l: 0,\n\t\t\t\t\tscroll_t: 0,\n\t\t\t\t\tscroll_e: false,\n\t\t\t\t\tscroll_i: false\n\t\t\t\t};\n\t\t\t\t$(document).unbind(\"mousemove\",\t$.vakata.dnd.drag);\n\t\t\t\t$(document).unbind(\"mouseup\",\t$.vakata.dnd.stop);\n\t\t\t},\n\t\t\t_scroll : function (init_only) {\n\t\t\t\tif(!vakata_dnd.scroll_e || (!vakata_dnd.scroll_l && !vakata_dnd.scroll_t)) {\n\t\t\t\t\tif(vakata_dnd.scroll_i) { clearInterval(vakata_dnd.scroll_i); vakata_dnd.scroll_i = false; }\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t\tif(!vakata_dnd.scroll_i) {\n\t\t\t\t\tvakata_dnd.scroll_i = setInterval($.vakata.dnd._scroll, 100);\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t\tif(init_only === true) { return false; }\n\n\t\t\t\tvar i = vakata_dnd.scroll_e.scrollTop(),\n\t\t\t\t\tj = vakata_dnd.scroll_e.scrollLeft();\n\t\t\t\tvakata_dnd.scroll_e.scrollTop(i + vakata_dnd.scroll_t * $.vakata.dnd.settings.scroll_speed);\n\t\t\t\tvakata_dnd.scroll_e.scrollLeft(j + vakata_dnd.scroll_l * $.vakata.dnd.settings.scroll_speed);\n\t\t\t\tif(i !== vakata_dnd.scroll_e.scrollTop() || j !== vakata_dnd.scroll_e.scrollLeft()) {\n\t\t\t\t\t$.vakata.dnd._trigger(\"scroll\", vakata_dnd.scroll_e);\n\t\t\t\t}\n\t\t\t},\n\t\t\tstart : function (e, data, html) {\n\t\t\t\tif(vakata_dnd.is_drag) { $.vakata.dnd.stop({}); }\n\t\t\t\ttry {\n\t\t\t\t\te.currentTarget.unselectable = \"on\";\n\t\t\t\t\te.currentTarget.onselectstart = function() { return false; };\n\t\t\t\t\tif(e.currentTarget.style) { e.currentTarget.style.MozUserSelect = \"none\"; }\n\t\t\t\t} catch(ignore) { }\n\t\t\t\tvakata_dnd.init_x\t= e.pageX;\n\t\t\t\tvakata_dnd.init_y\t= e.pageY;\n\t\t\t\tvakata_dnd.data\t\t= data;\n\t\t\t\tvakata_dnd.is_down\t= true;\n\t\t\t\tvakata_dnd.element\t= e.currentTarget;\n\t\t\t\tif(html !== false) {\n\t\t\t\t\tvakata_dnd.helper = $(\"<div id='vakata-dnd'></div>\").html(html).css({\n\t\t\t\t\t\t\"display\"\t\t: \"block\",\n\t\t\t\t\t\t\"margin\"\t\t: \"0\",\n\t\t\t\t\t\t\"padding\"\t\t: \"0\",\n\t\t\t\t\t\t\"position\"\t\t: \"absolute\",\n\t\t\t\t\t\t\"top\"\t\t\t: \"-2000px\",\n\t\t\t\t\t\t\"lineHeight\"\t: \"16px\",\n\t\t\t\t\t\t\"zIndex\"\t\t: \"10000\"\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\t$(document).bind(\"mousemove\", $.vakata.dnd.drag);\n\t\t\t\t$(document).bind(\"mouseup\", $.vakata.dnd.stop);\n\t\t\t\treturn false;\n\t\t\t},\n\t\t\tdrag : function (e) {\n\t\t\t\tif(!vakata_dnd.is_down) { return; }\n\t\t\t\tif(!vakata_dnd.is_drag) {\n\t\t\t\t\tif(\n\t\t\t\t\t\tMath.abs(e.pageX - vakata_dnd.init_x) > $.vakata.dnd.settings.threshold ||\n\t\t\t\t\t\tMath.abs(e.pageY - vakata_dnd.init_y) > $.vakata.dnd.settings.threshold\n\t\t\t\t\t) {\n\t\t\t\t\t\tif(vakata_dnd.helper) {\n\t\t\t\t\t\t\tvakata_dnd.helper.appendTo(\"body\");\n\t\t\t\t\t\t\tvakata_dnd.helper_w = vakata_dnd.helper.outerWidth();\n\t\t\t\t\t\t}\n\t\t\t\t\t\tvakata_dnd.is_drag = true;\n\t\t\t\t\t\t$.vakata.dnd._trigger(\"start\", e);\n\t\t\t\t\t}\n\t\t\t\t\telse { return; }\n\t\t\t\t}\n\n\t\t\t\tvar d  = false, w  = false,\n\t\t\t\t\tdh = false, wh = false,\n\t\t\t\t\tdw = false, ww = false,\n\t\t\t\t\tdt = false, dl = false,\n\t\t\t\t\tht = false, hl = false;\n\n\t\t\t\tvakata_dnd.scroll_t = 0;\n\t\t\t\tvakata_dnd.scroll_l = 0;\n\t\t\t\tvakata_dnd.scroll_e = false;\n\t\t\t\t$(e.target)\n\t\t\t\t\t.parentsUntil(\"body\").addBack().vakata_reverse()\n\t\t\t\t\t.filter(function () {\n\t\t\t\t\t\treturn\t(/^auto|scroll$/).test($(this).css(\"overflow\")) &&\n\t\t\t\t\t\t\t\t(this.scrollHeight > this.offsetHeight || this.scrollWidth > this.offsetWidth);\n\t\t\t\t\t})\n\t\t\t\t\t.each(function () {\n\t\t\t\t\t\tvar t = $(this), o = t.offset();\n\t\t\t\t\t\tif(this.scrollHeight > this.offsetHeight) {\n\t\t\t\t\t\t\tif(o.top + t.height() - e.pageY < $.vakata.dnd.settings.scroll_proximity)\t{ vakata_dnd.scroll_t = 1; }\n\t\t\t\t\t\t\tif(e.pageY - o.top < $.vakata.dnd.settings.scroll_proximity)\t\t\t\t{ vakata_dnd.scroll_t = -1; }\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif(this.scrollWidth > this.offsetWidth) {\n\t\t\t\t\t\t\tif(o.left + t.width() - e.pageX < $.vakata.dnd.settings.scroll_proximity)\t{ vakata_dnd.scroll_l = 1; }\n\t\t\t\t\t\t\tif(e.pageX - o.left < $.vakata.dnd.settings.scroll_proximity)\t\t\t\t{ vakata_dnd.scroll_l = -1; }\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif(vakata_dnd.scroll_t || vakata_dnd.scroll_l) {\n\t\t\t\t\t\t\tvakata_dnd.scroll_e = $(this);\n\t\t\t\t\t\t\treturn false;\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\n\t\t\t\tif(!vakata_dnd.scroll_e) {\n\t\t\t\t\td  = $(document); w = $(window);\n\t\t\t\t\tdh = d.height(); wh = w.height();\n\t\t\t\t\tdw = d.width(); ww = w.width();\n\t\t\t\t\tdt = d.scrollTop(); dl = d.scrollLeft();\n\t\t\t\t\tif(dh > wh && e.pageY - dt < $.vakata.dnd.settings.scroll_proximity)\t\t{ vakata_dnd.scroll_t = -1;  }\n\t\t\t\t\tif(dh > wh && wh - (e.pageY - dt) < $.vakata.dnd.settings.scroll_proximity)\t{ vakata_dnd.scroll_t = 1; }\n\t\t\t\t\tif(dw > ww && e.pageX - dl < $.vakata.dnd.settings.scroll_proximity)\t\t{ vakata_dnd.scroll_l = -1; }\n\t\t\t\t\tif(dw > ww && ww - (e.pageX - dl) < $.vakata.dnd.settings.scroll_proximity)\t{ vakata_dnd.scroll_l = 1; }\n\t\t\t\t\tif(vakata_dnd.scroll_t || vakata_dnd.scroll_l) {\n\t\t\t\t\t\tvakata_dnd.scroll_e = d;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif(vakata_dnd.scroll_e) { $.vakata.dnd._scroll(true); }\n\n\t\t\t\tif(vakata_dnd.helper) {\n\t\t\t\t\tht = parseInt(e.pageY + $.vakata.dnd.settings.helper_top, 10);\n\t\t\t\t\thl = parseInt(e.pageX + $.vakata.dnd.settings.helper_left, 10);\n\t\t\t\t\tif(dh && ht + 25 > dh) { ht = dh - 50; }\n\t\t\t\t\tif(dw && hl + vakata_dnd.helper_w > dw) { hl = dw - (vakata_dnd.helper_w + 2); }\n\t\t\t\t\tvakata_dnd.helper.css({\n\t\t\t\t\t\tleft\t: hl + \"px\",\n\t\t\t\t\t\ttop\t\t: ht + \"px\"\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\t$.vakata.dnd._trigger(\"move\", e);\n\t\t\t},\n\t\t\tstop : function (e) {\n\t\t\t\tif(vakata_dnd.is_drag) {\n\t\t\t\t\t$.vakata.dnd._trigger(\"stop\", e);\n\t\t\t\t}\n\t\t\t\t$.vakata.dnd._clean();\n\t\t\t}\n\t\t};\n\t}(jQuery));\n\n\t// include the dnd plugin by default\n\t// $.jstree.defaults.plugins.push(\"dnd\");"}, {"tags": [], "description": {"full": "<h3>Search plugin</h3>\n\n<p>Adds search functionality to jsTree.</p>", "summary": "<h3>Search plugin</h3>", "body": "<p>Adds search functionality to jsTree.</p>"}, "ignore": false}, {"tags": [{"type": "name", "string": "$.jstree.defaults.search"}, {"type": "plugin", "string": "search"}], "description": {"full": "<p>stores all defaults for the search plugin</p>", "summary": "<p>stores all defaults for the search plugin</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "$.jstree.defaults.search = {"}, {"tags": [{"type": "name", "string": "$.jstree.defaults.search.ajax"}, {"type": "plugin", "string": "search"}], "description": {"full": "<p>a jQuery-like AJAX config, which j<PERSON><PERSON> uses if a server should be queried for results. </p>\n\n<p>A <code>str</code> (which is the search string) parameter will be added with the request. The expected result is a JSON array with nodes that need to be opened so that matching nodes will be revealed.<br />Leave this setting as <code>false</code> to not query the server.</p>", "summary": "<p>a jQuery-like AJAX config, which jstree uses if a server should be queried for results. </p>", "body": "<p>A <code>str</code> (which is the search string) parameter will be added with the request. The expected result is a JSON array with nodes that need to be opened so that matching nodes will be revealed.<br />Leave this setting as <code>false</code> to not query the server.</p>"}, "isPrivate": false, "ignore": false, "code": "ajax : false,"}, {"tags": [{"type": "name", "string": "$.jstree.defaults.search.fuzzy"}, {"type": "plugin", "string": "search"}], "description": {"full": "<p>Indicates if the search should be fuzzy or not (should <code>chnd3</code> match <code>child node 3</code>). Default is <code>true</code>.</p>", "summary": "<p>Indicates if the search should be fuzzy or not (should <code>chnd3</code> match <code>child node 3</code>). Default is <code>true</code>.</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "fuzzy : true,"}, {"tags": [{"type": "name", "string": "$.jstree.defaults.search.case_sensitive"}, {"type": "plugin", "string": "search"}], "description": {"full": "<p>Indicates if the search should be case sensitive. Default is <code>false</code>.</p>", "summary": "<p>Indicates if the search should be case sensitive. Default is <code>false</code>.</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "case_sensitive : false,"}, {"tags": [{"type": "name", "string": "$.jstree.defaults.search.show_only_matches"}, {"type": "plugin", "string": "search"}], "description": {"full": "<p>Indicates if the tree should be filtered to show only matching nodes (keep in mind this can be a heavy on large trees in old browsers). Default is <code>false</code>.</p>", "summary": "<p>Indicates if the tree should be filtered to show only matching nodes (keep in mind this can be a heavy on large trees in old browsers). Default is <code>false</code>.</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "show_only_matches : false,"}, {"tags": [{"type": "name", "string": "$.jstree.defaults.search.close_opened_onclear"}, {"type": "plugin", "string": "search"}], "description": {"full": "<p>Indicates if all nodes opened to reveal the search result, should be closed when the search is cleared or a new search is performed. Default is <code>true</code>.</p>", "summary": "<p>Indicates if all nodes opened to reveal the search result, should be closed when the search is cleared or a new search is performed. Default is <code>true</code>.</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "close_opened_onclear : true\n\t};\n\n\t$.jstree.plugins.search = function (options, parent) {\n\t\tthis.bind = function () {\n\t\t\tparent.bind.call(this);\n\n\t\t\tthis._data.search.str = \"\";\n\t\t\tthis._data.search.dom = $();\n\t\t\tthis._data.search.res = [];\n\t\t\tthis._data.search.opn = [];\n\n\t\t\tif(this.settings.search.show_only_matches) {\n\t\t\t\tthis.element\n\t\t\t\t\t.on(\"search.jstree\", function (e, data) {\n\t\t\t\t\t\tif(data.nodes.length) {\n\t\t\t\t\t\t\t$(this).find(\"li\").hide().filter('.jstree-last').filter(function() { return this.nextSibling; }).removeClass('jstree-last');\n\t\t\t\t\t\t\tdata.nodes.parentsUntil(\".jstree\").addBack().show()\n\t\t\t\t\t\t\t\t.filter(\"ul\").each(function () { $(this).children(\"li:visible\").eq(-1).addClass(\"jstree-last\"); });\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t\t.on(\"clear_search.jstree\", function (e, data) {\n\t\t\t\t\t\tif(data.nodes.length) {\n\t\t\t\t\t\t\t$(this).find(\"li\").css(\"display\",\"\").filter('.jstree-last').filter(function() { return this.nextSibling; }).removeClass('jstree-last');\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t}\n\t\t};"}, {"tags": [{"type": "name", "string": "search(str [, skip_async])"}, {"type": "param", "types": ["String"], "name": "str", "description": "the search string"}, {"type": "param", "types": ["Boolean"], "name": "skip_async", "description": "if set to true server will not be queried even if configured"}, {"type": "plugin", "string": "search"}, {"type": "trigger", "string": "search.jstree"}], "description": {"full": "<p>used to search the tree nodes for a given string</p>", "summary": "<p>used to search the tree nodes for a given string</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "this.search = function (str, skip_async) {\n\t\t\tif(str === false || $.trim(str) === \"\") {\n\t\t\t\treturn this.clear_search();\n\t\t\t}\n\t\t\tvar s = this.settings.search,\n\t\t\t\ta = s.ajax ? $.extend({}, s.ajax) : false,\n\t\t\t\tf = null,\n\t\t\t\tr = [],\n\t\t\t\tp = [], i, j;\n\t\t\tif(this._data.search.res.length) {\n\t\t\t\tthis.clear_search();\n\t\t\t}\n\t\t\tif(!skip_async && a !== false) {\n\t\t\t\tif(!a.data) { a.data = {}; }\n\t\t\t\ta.data.str = str;\n\t\t\t\treturn $.ajax(s.ajax).done($.proxy(function (d) {\n\t\t\t\t\tthis._search_load(d, str);\n\t\t\t\t}, this));\n\t\t\t}\n\t\t\tthis._data.search.str = str;\n\t\t\tthis._data.search.dom = $();\n\t\t\tthis._data.search.res = [];\n\t\t\tthis._data.search.opn = [];\n\n\t\t\tf = new $.vakata.search(str, true, { caseSensitive : s.case_sensitive, fuzzy : s.fuzzy });\n\n\t\t\t$.each(this._model.data, function (i, v) {\n\t\t\t\tif(v.text && f.search(v.text).isMatch) {\n\t\t\t\t\tr.push(i);\n\t\t\t\t\tp = p.concat(v.parents);\n\t\t\t\t}\n\t\t\t});\n\t\t\tif(r.length) {\n\t\t\t\tp = $.vakata.array_unique(p);\n\t\t\t\tthis._search_open(p);\n\t\t\t\tfor(i = 0, j = r.length; i < j; i++) {\n\t\t\t\t\tf = this.get_node(r[i], true);\n\t\t\t\t\tif(f) {\n\t\t\t\t\t\tthis._data.search.dom = this._data.search.dom.add(f);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tthis._data.search.res = r;\n\t\t\t\tthis._data.search.dom.children(\".jstree-anchor\").addClass('jstree-search');\n\t\t\t}", "ctx": {"type": "method", "receiver": "this", "name": "search", "string": "this.search()"}}, {"tags": [{"type": "event", "string": ""}, {"type": "name", "string": "search.jstree"}, {"type": "param", "types": ["j<PERSON><PERSON><PERSON>"], "name": "nodes", "description": "a jQuery collection of matching nodes"}, {"type": "param", "types": ["String"], "name": "str", "description": "the search string"}, {"type": "param", "types": ["Array"], "name": "res", "description": "a collection of objects represeing the matching nodes"}, {"type": "plugin", "string": "search"}], "description": {"full": "<p>triggered after search is complete</p>", "summary": "<p>triggered after search is complete</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "this.trigger('search', { nodes : this._data.search.dom, str : str, res : this._data.search.res });\n\t\t};"}, {"tags": [{"type": "name", "string": "clear_search()"}, {"type": "plugin", "string": "search"}, {"type": "trigger", "string": "clear_search.jstree"}], "description": {"full": "<p>used to clear the last search (removes classes and shows all nodes if filtering is on)</p>", "summary": "<p>used to clear the last search (removes classes and shows all nodes if filtering is on)</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "this.clear_search = function () {\n\t\t\tthis._data.search.dom.children(\".jstree-anchor\").removeClass(\"jstree-search\");\n\t\t\tif(this.settings.search.close_opened_onclear) {\n\t\t\t\tthis.close_node(this._data.search.opn);\n\t\t\t}", "ctx": {"type": "method", "receiver": "this", "name": "clear_search", "string": "this.clear_search()"}}, {"tags": [{"type": "event", "string": ""}, {"type": "name", "string": "clear_search.jstree"}, {"type": "param", "types": ["j<PERSON><PERSON><PERSON>"], "name": "nodes", "description": "a jQuery collection of matching nodes (the result from the last search)"}, {"type": "param", "types": ["String"], "name": "str", "description": "the search string (the last search string)"}, {"type": "param", "types": ["Array"], "name": "res", "description": "a collection of objects represeing the matching nodes (the result from the last search)"}, {"type": "plugin", "string": "search"}], "description": {"full": "<p>triggered after search is complete</p>", "summary": "<p>triggered after search is complete</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "this.trigger('clear_search', { 'nodes' : this._data.search.dom, str : this._data.search.str, res : this._data.search.res });\n\t\t\tthis._data.search.str = \"\";\n\t\t\tthis._data.search.res = [];\n\t\t\tthis._data.search.opn = [];\n\t\t\tthis._data.search.dom = $();\n\t\t};"}, {"tags": [{"type": "private", "string": ""}, {"type": "name", "string": "_search_open(d)"}, {"type": "param", "types": ["Array"], "name": "d", "description": "an array of node IDs"}, {"type": "plugin", "string": "search"}], "description": {"full": "<p>opens nodes that need to be opened to reveal the search results. Used only internally.</p>", "summary": "<p>opens nodes that need to be opened to reveal the search results. Used only internally.</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "this._search_open = function (d) {\n\t\t\tvar t = this;\n\t\t\t$.each(d.concat([]), function (i, v) {\n\t\t\t\tv = document.getElementById(v);\n\t\t\t\tif(v) {\n\t\t\t\t\tif(t.is_closed(v)) {\n\t\t\t\t\t\tt._data.search.opn.push(v.id);\n\t\t\t\t\t\tt.open_node(v, function () { t._search_open(d); });\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t};", "ctx": {"type": "method", "receiver": "this", "name": "_search_open", "string": "this._search_open()"}}, {"tags": [{"type": "private", "string": ""}, {"type": "name", "string": "_search_load(d, str)"}, {"type": "param", "types": ["Array"], "name": "d", "description": "an array of node IDs"}, {"type": "param", "types": ["String"], "name": "str", "description": "the search string"}, {"type": "plugin", "string": "search"}], "description": {"full": "<p>loads nodes that need to be opened to reveal the search results. Used only internally.</p>", "summary": "<p>loads nodes that need to be opened to reveal the search results. Used only internally.</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "this._search_load = function (d, str) {\n\t\t\tvar res = true,\n\t\t\t\tt = this,\n\t\t\t\tm = t._model.data;\n\t\t\t$.each(d.concat([]), function (i, v) {\n\t\t\t\tif(m[v]) {\n\t\t\t\t\tif(!m[v].state.loaded) {\n\t\t\t\t\t\tt.load_node(v, function () { t._search_load(d, str); });\n\t\t\t\t\t\tres = false;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t\tif(res) {\n\t\t\t\tthis.search(str, true);\n\t\t\t}\n\t\t};\n\t};\n\n\t// helpers\n\t(function ($) {\n\t\t// from http://kiro.me/projects/fuse.html\n\t\t$.vakata.search = function(pattern, txt, options) {\n\t\t\toptions = options || {};\n\t\t\tif(options.fuzzy !== false) {\n\t\t\t\toptions.fuzzy = true;\n\t\t\t}\n\t\t\tpattern = options.caseSensitive ? pattern : pattern.toLowerCase();\n\t\t\tvar MATCH_LOCATION\t= options.location || 0,\n\t\t\t\tMATCH_DISTANCE\t= options.distance || 100,\n\t\t\t\tMATCH_THRESHOLD\t= options.threshold || 0.6,\n\t\t\t\tpatternLen = pattern.length,\n\t\t\t\tmatchmask, pattern_alphabet, match_bitapScore, search;\n\t\t\tif(patternLen > 32) {\n\t\t\t\toptions.fuzzy = false;\n\t\t\t}\n\t\t\tif(options.fuzzy) {\n\t\t\t\tmatchmask = 1 << (patternLen - 1);\n\t\t\t\tpattern_alphabet = (function () {\n\t\t\t\t\tvar mask = {},\n\t\t\t\t\t\ti = 0;\n\t\t\t\t\tfor (i = 0; i < patternLen; i++) {\n\t\t\t\t\t\tmask[pattern.charAt(i)] = 0;\n\t\t\t\t\t}\n\t\t\t\t\tfor (i = 0; i < patternLen; i++) {\n\t\t\t\t\t\tmask[pattern.charAt(i)] |= 1 << (patternLen - i - 1);\n\t\t\t\t\t}\n\t\t\t\t\treturn mask;\n\t\t\t\t}());\n\t\t\t\tmatch_bitapScore = function (e, x) {\n\t\t\t\t\tvar accuracy = e / patternLen,\n\t\t\t\t\t\tproximity = Math.abs(MATCH_LOCATION - x);\n\t\t\t\t\tif(!MATCH_DISTANCE) {\n\t\t\t\t\t\treturn proximity ? 1.0 : accuracy;\n\t\t\t\t\t}\n\t\t\t\t\treturn accuracy + (proximity / MATCH_DISTANCE);\n\t\t\t\t};\n\t\t\t}\n\t\t\tsearch = function (text) {\n\t\t\t\ttext = options.caseSensitive ? text : text.toLowerCase();\n\t\t\t\tif(pattern === text || text.indexOf(pattern) !== -1) {\n\t\t\t\t\treturn {\n\t\t\t\t\t\tisMatch: true,\n\t\t\t\t\t\tscore: 0\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t\tif(!options.fuzzy) {\n\t\t\t\t\treturn {\n\t\t\t\t\t\tisMatch: false,\n\t\t\t\t\t\tscore: 1\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t\tvar i, j,\n\t\t\t\t\ttextLen = text.length,\n\t\t\t\t\tscoreThreshold = MATCH_THRESHOLD,\n\t\t\t\t\tbestLoc = text.indexOf(pattern, MATCH_LOCATION),\n\t\t\t\t\tbinMin, binMid,\n\t\t\t\t\tbinMax = patternLen + textLen,\n\t\t\t\t\tlastRd, start, finish, rd, charMatch,\n\t\t\t\t\tscore = 1,\n\t\t\t\t\tlocations = [];\n\t\t\t\tif (bestLoc !== -1) {\n\t\t\t\t\tscoreThreshold = Math.min(match_bitapScore(0, bestLoc), scoreThreshold);\n\t\t\t\t\tbestLoc = text.lastIndexOf(pattern, MATCH_LOCATION + patternLen);\n\t\t\t\t\tif (bestLoc !== -1) {\n\t\t\t\t\t\tscoreThreshold = Math.min(match_bitapScore(0, bestLoc), scoreThreshold);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tbestLoc = -1;\n\t\t\t\tfor (i = 0; i < patternLen; i++) {\n\t\t\t\t\tbinMin = 0;\n\t\t\t\t\tbinMid = binMax;\n\t\t\t\t\twhile (binMin < binMid) {\n\t\t\t\t\t\tif (match_bitapScore(i, MATCH_LOCATION + binMid) <= scoreThreshold) {\n\t\t\t\t\t\t\tbinMin = binMid;\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tbinMax = binMid;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tbinMid = Math.floor((binMax - binMin) / 2 + binMin);\n\t\t\t\t\t}\n\t\t\t\t\tbinMax = binMid;\n\t\t\t\t\tstart = Math.max(1, MATCH_LOCATION - binMid + 1);\n\t\t\t\t\tfinish = Math.min(MATCH_LOCATION + binMid, textLen) + patternLen;\n\t\t\t\t\trd = new Array(finish + 2);\n\t\t\t\t\trd[finish + 1] = (1 << i) - 1;\n\t\t\t\t\tfor (j = finish; j >= start; j--) {\n\t\t\t\t\t\tcharMatch = pattern_alphabet[text.charAt(j - 1)];\n\t\t\t\t\t\tif (i === 0) {\n\t\t\t\t\t\t\trd[j] = ((rd[j + 1] << 1) | 1) & charMatch;\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\trd[j] = ((rd[j + 1] << 1) | 1) & charMatch | (((lastRd[j + 1] | lastRd[j]) << 1) | 1) | lastRd[j + 1];\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (rd[j] & matchmask) {\n\t\t\t\t\t\t\tscore = match_bitapScore(i, j - 1);\n\t\t\t\t\t\t\tif (score <= scoreThreshold) {\n\t\t\t\t\t\t\t\tscoreThreshold = score;\n\t\t\t\t\t\t\t\tbestLoc = j - 1;\n\t\t\t\t\t\t\t\tlocations.push(bestLoc);\n\t\t\t\t\t\t\t\tif (bestLoc > MATCH_LOCATION) {\n\t\t\t\t\t\t\t\t\tstart = Math.max(1, 2 * MATCH_LOCATION - bestLoc);\n\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tif (match_bitapScore(i + 1, MATCH_LOCATION) > scoreThreshold) {\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t\tlastRd = rd;\n\t\t\t\t}\n\t\t\t\treturn {\n\t\t\t\t\tisMatch: bestLoc >= 0,\n\t\t\t\t\tscore: score\n\t\t\t\t};\n\t\t\t};\n\t\t\treturn txt === true ? { 'search' : search } : search(txt);\n\t\t};\n\t}(jQuery));\n\n\t// include the search plugin by default\n\t// $.jstree.defaults.plugins.push(\"search\");", "ctx": {"type": "method", "receiver": "this", "name": "_search_load", "string": "this._search_load()"}}, {"tags": [], "description": {"full": "<h3>Sort plugin</h3>\n\n<p>Autmatically sorts all siblings in the tree according to a sorting function.</p>", "summary": "<h3>Sort plugin</h3>", "body": "<p>Autmatically sorts all siblings in the tree according to a sorting function.</p>"}, "ignore": false}, {"tags": [{"type": "name", "string": "$.jstree.defaults.sort"}, {"type": "plugin", "string": "sort"}], "description": {"full": "<p>the settings function used to sort the nodes.<br />It is executed in the tree's context, accepts two nodes as arguments and should return <code>1</code> or <code>-1</code>.</p>", "summary": "<p>the settings function used to sort the nodes.<br />It is executed in the tree's context, accepts two nodes as arguments and should return <code>1</code> or <code>-1</code>.</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "$.jstree.defaults.sort = function (a, b) {\n\t\t//return this.get_type(a) === this.get_type(b) ? (this.get_text(a) > this.get_text(b) ? 1 : -1) : this.get_type(a) >= this.get_type(b);\n\t\treturn this.get_text(a) > this.get_text(b) ? 1 : -1;\n\t};\n\t$.jstree.plugins.sort = function (options, parent) {\n\t\tthis.bind = function () {\n\t\t\tparent.bind.call(this);\n\t\t\tthis.element\n\t\t\t\t.on(\"model.jstree\", $.proxy(function (e, data) {\n\t\t\t\t\t\tthis.sort(data.parent, true);\n\t\t\t\t\t}, this))\n\t\t\t\t.on(\"rename_node.jstree create_node.jstree\", $.proxy(function (e, data) {\n\t\t\t\t\t\tthis.sort(data.parent || data.node.parent, false);\n\t\t\t\t\t\tthis.redraw_node(data.parent || data.node.parent, true);\n\t\t\t\t\t}, this))\n\t\t\t\t.on(\"move_node.jstree copy_node.jstree\", $.proxy(function (e, data) {\n\t\t\t\t\t\tthis.sort(data.parent, false);\n\t\t\t\t\t\tthis.redraw_node(data.parent, true);\n\t\t\t\t\t}, this));\n\t\t};", "ctx": {"type": "method", "receiver": "$.jstree.defaults", "name": "sort", "string": "$.jstree.defaults.sort()"}}, {"tags": [{"type": "private", "string": ""}, {"type": "name", "string": "sort(obj [, deep])"}, {"type": "param", "types": ["mixed"], "name": "obj", "description": "the node"}, {"type": "param", "types": ["Boolean"], "name": "deep", "description": "if set to `true` nodes are sorted recursively."}, {"type": "plugin", "string": "sort"}, {"type": "trigger", "string": "search.jstree"}], "description": {"full": "<p>used to sort a node's children</p>", "summary": "<p>used to sort a node's children</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "this.sort = function (obj, deep) {\n\t\t\tvar i, j;\n\t\t\tobj = this.get_node(obj);\n\t\t\tif(obj && obj.children && obj.children.length) {\n\t\t\t\tobj.children.sort($.proxy(this.settings.sort, this));\n\t\t\t\tif(deep) {\n\t\t\t\t\tfor(i = 0, j = obj.children_d.length; i < j; i++) {\n\t\t\t\t\t\tthis.sort(obj.children_d[i], false);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t};\n\t};\n\n\t// include the sort plugin by default\n\t// $.jstree.defaults.plugins.push(\"sort\");", "ctx": {"type": "method", "receiver": "this", "name": "sort", "string": "this.sort()"}}, {"tags": [], "description": {"full": "<h3>State plugin</h3>\n\n<p>Saves the state of the tree (selected nodes, opened nodes) on the user's computer using available options (localStorage, cookies, etc)</p>", "summary": "<h3>State plugin</h3>", "body": "<p>Saves the state of the tree (selected nodes, opened nodes) on the user's computer using available options (localStorage, cookies, etc)</p>"}, "ignore": false, "code": "var to = false;", "ctx": {"type": "declaration", "name": "to", "value": "false", "string": "to"}}, {"tags": [{"type": "name", "string": "$.jstree.defaults.state"}, {"type": "plugin", "string": "state"}], "description": {"full": "<p>stores all defaults for the state plugin</p>", "summary": "<p>stores all defaults for the state plugin</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "$.jstree.defaults.state = {"}, {"tags": [{"type": "name", "string": "$.jstree.defaults.state.key"}, {"type": "plugin", "string": "state"}], "description": {"full": "<p>A string for the key to use when saving the current tree (change if using multiple trees in your project). Defaults to <code>jstree</code>.</p>", "summary": "<p>A string for the key to use when saving the current tree (change if using multiple trees in your project). Defaults to <code>jstree</code>.</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "key\t\t: 'jstree',"}, {"tags": [{"type": "name", "string": "$.jstree.defaults.state.events"}, {"type": "plugin", "string": "state"}], "description": {"full": "<p>A space separated list of events that trigger a state save. Defaults to <code>changed.jstree open_node.jstree close_node.jstree</code>.</p>", "summary": "<p>A space separated list of events that trigger a state save. Defaults to <code>changed.jstree open_node.jstree close_node.jstree</code>.</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "events\t: 'changed.jstree open_node.jstree close_node.jstree',\n\t\tttl\t\t: false\n\t};\n\t$.jstree.plugins.state = function (options, parent) {\n\t\tthis.bind = function () {\n\t\t\tparent.bind.call(this);\n\n\t\t\tthis.element\n\t\t\t\t.on(\"ready.jstree\", $.proxy(function (e, data) {\n\t\t\t\t\t\tthis.element.one(\"restore_state.jstree set_state.jstree\", $.proxy(function () {\n\t\t\t\t\t\t\tthis.element.on(this.settings.state.events, $.proxy(function () {\n\t\t\t\t\t\t\t\tif(to) { clearTimeout(to); }\n\t\t\t\t\t\t\t\tto = setTimeout($.proxy(function () { this.save_state(); }, this), 100);\n\t\t\t\t\t\t\t}, this));\n\t\t\t\t\t\t}, this));\n\t\t\t\t\t\tthis.restore_state();\n\t\t\t\t\t}, this));\n\t\t};"}, {"tags": [{"type": "name", "string": "save_state()"}, {"type": "plugin", "string": "state"}], "description": {"full": "<p>save the state</p>", "summary": "<p>save the state</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "this.save_state = function () {\n\t\t\t$.vakata.storage.set(this.settings.state.key, this.get_state(), this.settings.state.ttl);\n\t\t};", "ctx": {"type": "method", "receiver": "this", "name": "save_state", "string": "this.save_state()"}}, {"tags": [{"type": "name", "string": "restore_state()"}, {"type": "plugin", "string": "state"}], "description": {"full": "<p>restore the state from the user's computer</p>", "summary": "<p>restore the state from the user's computer</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "this.restore_state = function () {\n\t\t\tvar k = $.vakata.storage.get(this.settings.state.key);\n\n\t\t\tif(!!k) { this.set_state(k); }\n\t\t\tthis.trigger('restore_state', { 'state' : k });\n\t\t};", "ctx": {"type": "method", "receiver": "this", "name": "restore_state", "string": "this.restore_state()"}}, {"tags": [{"type": "name", "string": "clear_state()"}, {"type": "plugin", "string": "state"}], "description": {"full": "<p>clear the state on the user's computer</p>", "summary": "<p>clear the state on the user's computer</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "this.clear_state = function () {\n\t\t\treturn $.vakata.storage.del(this.settings.state.key);\n\t\t};\n\t};\n\n\t(function ($, document, undefined) {\n\t\tvar raw\t\t= function (s) { return s; },\n\t\t\tdecoded\t= function (s) { return decodeURIComponent(s.replace(/\\+/g, ' ')); },\n\t\t\tconfig = $.vakata.cookie = function (key, value, options) {\n\t\t\t\tvar days, t, decode, cookies, i, l, parts, cookie;\n\t\t\t\t// write\n\t\t\t\tif (value !== undefined) {\n\t\t\t\t\toptions = $.extend({}, config.defaults, options);\n\n\t\t\t\t\tif (value === null) {\n\t\t\t\t\t\toptions.expires = -1;\n\t\t\t\t\t}\n\n\t\t\t\t\tif (typeof options.expires === 'number') {\n\t\t\t\t\t\tdays = options.expires;\n\t\t\t\t\t\tt = options.expires = new Date();\n\t\t\t\t\t\tt.setDate(t.getDate() + days);\n\t\t\t\t\t}\n\n\t\t\t\t\tvalue = config.json ? $.vakata.json.encode(value) : String(value);\n\t\t\t\t\tvalue = [\n\t\t\t\t\t\tencodeURIComponent(key), '=', config.raw ? value : encodeURIComponent(value),\n\t\t\t\t\t\toptions.expires ? '; expires=' + options.expires.toUTCString() : '', // use expires attribute, max-age is not supported by IE\n\t\t\t\t\t\toptions.path    ? '; path=' + options.path : '',\n\t\t\t\t\t\toptions.domain  ? '; domain=' + options.domain : '',\n\t\t\t\t\t\toptions.secure  ? '; secure' : ''\n\t\t\t\t\t].join('');\n\t\t\t\t\tdocument.cookie = value;\n\t\t\t\t\treturn value;\n\t\t\t\t}\n\t\t\t\t// read\n\t\t\t\tdecode = config.raw ? raw : decoded;\n\t\t\t\tcookies = document.cookie.split('; ');\n\t\t\t\tfor (i = 0, l = cookies.length; i < l; i++) {\n\t\t\t\t\tparts = cookies[i].split('=');\n\t\t\t\t\tif (decode(parts.shift()) === key) {\n\t\t\t\t\t\tcookie = decode(parts.join('='));\n\t\t\t\t\t\treturn config.json ? $.vakata.json.decode(cookie) : cookie;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn null;\n\t\t\t};\n\t\tconfig.defaults = {};\n\t\t$.vakata.removeCookie = function (key, options) {\n\t\t\tif ($.vakata.cookie(key) !== null) {\n\t\t\t\t$.vakata.cookie(key, null, options);\n\t\t\t\treturn true;\n\t\t\t}\n\t\t\treturn false;\n\t\t};\n\t}(jQuery, document));\n\n\t(function ($, undefined) {\n\t\tvar _storage = {},\n\t\t\t_storage_service = {jStorage:\"{}\"},\n\t\t\t_storage_elm = null,\n\t\t\t_storage_size = 0,\n\t\t\tjson_encode = $.vakata.json.encode,\n\t\t\tjson_decode = $.vakata.json.decode,\n\t\t\t_backend = false,\n\t\t\t_ttl_timeout = false;\n\n\t\tfunction _load_storage() {\n\t\t\tif(_storage_service.jStorage) {\n\t\t\t\ttry {\n\t\t\t\t\t_storage = json_decode(String(_storage_service.jStorage));\n\t\t\t\t} catch(ex) { _storage_service.jStorage = \"{}\"; }\n\t\t\t} else {\n\t\t\t\t_storage_service.jStorage = \"{}\";\n\t\t\t}\n\t\t\t_storage_size = _storage_service.jStorage ? String(_storage_service.jStorage).length : 0;\n\t\t}\n\n\t\tfunction _save() {\n\t\t\ttry {\n\t\t\t\t_storage_service.jStorage = json_encode(_storage);\n\t\t\t\tif(_backend === 'userDataBehavior') {\n\t\t\t\t\t_storage_elm.setAttribute(\"jStorage\", _storage_service.jStorage);\n\t\t\t\t\t_storage_elm.save(\"jStorage\");\n\t\t\t\t}\n\t\t\t\tif(_backend === 'cookie') {\n\t\t\t\t\t$.vakata.cookie('__vjstorage', _storage_service.jStorage, { 'expires' : 365 });\n\t\t\t\t}\n\t\t\t\t_storage_size = _storage_service.jStorage?String(_storage_service.jStorage).length:0;\n\t\t\t} catch(ignore) {", "ctx": {"type": "method", "receiver": "this", "name": "clear_state", "string": "this.clear_state()"}}, {"tags": [], "description": {"full": "<p>probably cache is full, nothing is saved this way</p>", "summary": "<p>probably cache is full, nothing is saved this way</p>", "body": ""}, "ignore": true, "code": "}\n\t\t}\n\n\t\tfunction _checkKey(key) {\n\t\t\tif(!key || (typeof key !== \"string\" && typeof key !== \"number\")){\n\t\t\t\tthrow new TypeError('Key name must be string or numeric');\n\t\t\t}\n\t\t\tif(key === \"__jstorage_meta\") {\n\t\t\t\tthrow new TypeError('Reserved key name');\n\t\t\t}\n\t\t\treturn true;\n\t\t}\n\n\t\tfunction _handleTTL() {\n\t\t\tvar curtime = +new Date(),\n\t\t\t\ti,\n\t\t\t\tTTL,\n\t\t\t\tnextExpire = Infinity,\n\t\t\t\tchanged = false;\n\n\t\t\tif(_ttl_timeout !== false) {\n\t\t\t\tclearTimeout(_ttl_timeout);\n\t\t\t}\n\t\t\tif(!_storage.__jstorage_meta || typeof _storage.__jstorage_meta.TTL !== \"object\"){\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tTTL = _storage.__jstorage_meta.TTL;\n\t\t\tfor(i in TTL) {\n\t\t\t\tif(TTL.hasOwnProperty(i)) {\n\t\t\t\t\tif(TTL[i] <= curtime) {\n\t\t\t\t\t\tdelete TTL[i];\n\t\t\t\t\t\tdelete _storage[i];\n\t\t\t\t\t\tchanged = true;\n\t\t\t\t\t}\n\t\t\t\t\telse if(TTL[i] < nextExpire) {\n\t\t\t\t\t\tnextExpire = TTL[i];\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// set next check\n\t\t\tif(nextExpire !== Infinity) {\n\t\t\t\t_ttl_timeout = setTimeout(_handleTTL, nextExpire - curtime);\n\t\t\t}\n\t\t\t// save changes\n\t\t\tif(changed) {\n\t\t\t\t_save();\n\t\t\t}\n\t\t}\n\n\t\tfunction _init() {\n\t\t\tvar localStorageReallyWorks = false, data;\n\t\t\t//if(window.hasOwnProperty(\"localStorage\")){\n\t\t\tif(Object.prototype.hasOwnProperty.call(window, \"localStorage\")){\n\t\t\t\ttry {\n\t\t\t\t\twindow.localStorage.setItem('_tmptest', 'tmpval');\n\t\t\t\t\tlocalStorageReallyWorks = true;\n\t\t\t\t\twindow.localStorage.removeItem('_tmptest');\n\t\t\t\t} catch(ignore) {\n\t\t\t\t\t// Thanks be to iOS5 Private Browsing mode which throws\n\t\t\t\t\t// QUOTA_EXCEEDED_ERRROR DOM Exception 22.\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif(localStorageReallyWorks){\n\t\t\t\ttry {\n\t\t\t\t\tif(window.localStorage) {\n\t\t\t\t\t\t_storage_service = window.localStorage;\n\t\t\t\t\t\t_backend = \"localStorage\";\n\t\t\t\t\t}\n\t\t\t\t} catch(ignore) {"}, {"tags": [], "description": {"full": "<p>Firefox fails when touching localStorage and cookies are disabled</p>", "summary": "<p>Firefox fails when touching localStorage and cookies are disabled</p>", "body": ""}, "ignore": true, "code": "}\n\t\t\t//else if(window.hasOwnProperty(\"globalStorage\")) {\n\t\t\telse if(Object.prototype.hasOwnProperty.call(window, \"globalStorage\")) {\n\t\t\t\ttry {\n\t\t\t\t\tif(window.globalStorage) {\n\t\t\t\t\t\t_storage_service = window.globalStorage[window.location.hostname];\n\t\t\t\t\t\t_backend = \"globalStorage\";\n\t\t\t\t\t}\n\t\t\t\t} catch(ignore) {"}, {"tags": [], "description": {"full": "<p>Firefox fails when touching localStorage and cookies are disabled</p>", "summary": "<p>Firefox fails when touching localStorage and cookies are disabled</p>", "body": ""}, "ignore": true, "code": "}\n\t\t\telse {\n\t\t\t\t_storage_elm = document.createElement('link');\n\t\t\t\tif(_storage_elm.addBehavior) {\n\t\t\t\t\t_storage_elm.style.behavior = 'url(#default#userData)';\n\t\t\t\t\tdocument.getElementsByTagName('head')[0].appendChild(_storage_elm);\n\t\t\t\t\ttry {\n\t\t\t\t\t\t_storage_elm.load(\"jStorage\");\n\t\t\t\t\t\tdata = \"{}\";\n\t\t\t\t\t\tdata = _storage_elm.getAttribute(\"jStorage\");\n\t\t\t\t\t\t_storage_service.jStorage = data;\n\t\t\t\t\t\t_backend = \"userDataBehavior\";\n\t\t\t\t\t} catch(ignore) {}\n\t\t\t\t}\n\t\t\t\tif(\n\t\t\t\t\t!_backend && (\n\t\t\t\t\t\t!!$.vakata.cookie('__vjstorage') ||\n\t\t\t\t\t\t($.vakata.cookie('__vjstorage', '{}', { 'expires' : 365 }) && $.vakata.cookie('__vjstorage') === '{}')\n\t\t\t\t\t)\n\t\t\t\t) {\n\t\t\t\t\t_storage_elm = null;\n\t\t\t\t\t_storage_service.jStorage = $.vakata.cookie('__vjstorage');\n\t\t\t\t\t_backend = \"cookie\";\n\t\t\t\t}\n\n\t\t\t\tif(!_backend) {\n\t\t\t\t\t_storage_elm = null;\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t}\n\t\t\t_load_storage();\n\t\t\t_handleTTL();\n\t\t}"}, {"tags": [], "description": {"full": "<p>Variable: $.vakata.storage<br />object* holds all storage related functions and properties.</p>", "summary": "<p>Variable: $.vakata.storage<br />object* holds all storage related functions and properties.</p>", "body": ""}, "ignore": true, "code": "$.vakata.storage = {"}, {"tags": [], "description": {"full": "<p>Variable: $.vakata.storage.version<br />string* the version of jstorage used HEAVILY MODIFIED</p>", "summary": "<p>Variable: $.vakata.storage.version<br />string* the version of jstorage used HEAVILY MODIFIED</p>", "body": ""}, "ignore": true, "code": "version: \"0.3.0\","}, {"tags": [], "description": {"full": "<p>Function: $.vakata.storage.set<br />                Set a key to a value</p>\n\n<pre><code>            Parameters:\n                key - the key\n                value - the value\n\n            Returns:\n                _value_\n</code></pre>", "summary": "<p>Function: $.vakata.storage.set<br />                Set a key to a value</p>", "body": "<pre><code>            Parameters:\n                key - the key\n                value - the value\n\n            Returns:\n                _value_\n</code></pre>"}, "ignore": true, "code": "set : function (key, value, ttl) {\n\t\t\t\t_check<PERSON>ey(key);\n\t\t\t\tif(typeof value === \"object\") {\n\t\t\t\t\tvalue = json_decode(json_encode(value));\n\t\t\t\t}\n\t\t\t\t_storage[key] = value;\n\t\t\t\t_save();\n\t\t\t\tif(ttl && parseInt(ttl, 10)) {\n\t\t\t\t\t$.vakata.storage.setTTL(key, parseInt(ttl, 10));\n\t\t\t\t}\n\t\t\t\treturn value;\n\t\t\t},"}, {"tags": [], "description": {"full": "<p>Function: $.vakata.storage.get<br />                Get a value by key.</p>\n\n<pre><code>            Parameters:\n                key - the key\n                def - the value to return if _key_ is not found\n\n            Returns:\n                The found value, _def_ if key not found or _null_ if _def_ is not supplied.\n</code></pre>", "summary": "<p>Function: $.vakata.storage.get<br />                Get a value by key.</p>", "body": "<pre><code>            Parameters:\n                key - the key\n                def - the value to return if _key_ is not found\n\n            Returns:\n                The found value, _def_ if key not found or _null_ if _def_ is not supplied.\n</code></pre>"}, "ignore": true, "code": "get : function (key, def) {\n\t\t\t\t_checkKey(key);\n\t\t\t\tif(_storage.hasOwnProperty(key)){\n\t\t\t\t\treturn _storage[key];\n\t\t\t\t}\n\t\t\t\treturn def === undefined ? null : def;\n\t\t\t},"}, {"tags": [], "description": {"full": "<p>Function: $.vakata.storage.del<br />                Remove a key.</p>\n\n<pre><code>            Parameters:\n                key - the key\n\n            Returns:\n</code></pre>\n\n<p>boolean*</p>", "summary": "<p>Function: $.vakata.storage.del<br />                Remove a key.</p>", "body": "<pre><code>            Parameters:\n                key - the key\n\n            Returns:\n</code></pre>\n\n<p>boolean*</p>"}, "ignore": true, "code": "del : function (key) {\n\t\t\t\t_checkKey(key);\n\t\t\t\tif(_storage.hasOwnProperty(key)) {\n\t\t\t\t\tdelete _storage[key];\n\n\t\t\t\t\tif(_storage.__jstorage_meta && typeof _storage.__jstorage_meta.TTL === \"object\" && _storage.__jstorage_meta.TTL.hasOwnProperty(key)) {\n\t\t\t\t\t\tdelete _storage.__jstorage_meta.TTL[key];\n\t\t\t\t\t}\n\t\t\t\t\t_save();\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\t\t\t\treturn false;\n\t\t\t},\n\n\t\t\tsetTTL: function(key, ttl){\n\t\t\t\tvar curtime = +new Date();\n\n\t\t\t\t_checkKey(key);\n\t\t\t\tttl = Number(ttl) || 0;\n\t\t\t\tif(_storage.hasOwnProperty(key)){\n\t\t\t\t\tif(!_storage.__jstorage_meta){\n\t\t\t\t\t\t_storage.__jstorage_meta = {};\n\t\t\t\t\t}\n\t\t\t\t\tif(!_storage.__jstorage_meta.TTL) {\n\t\t\t\t\t\t_storage.__jstorage_meta.TTL = {};\n\t\t\t\t\t}\n\t\t\t\t\tif(ttl > 0) {\n\t\t\t\t\t\t_storage.__jstorage_meta.TTL[key] = curtime + ttl;\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\tdelete _storage.__jstorage_meta.TTL[key];\n\t\t\t\t\t}\n\t\t\t\t\t_save();\n\t\t\t\t\t_handleTTL();\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\t\t\t\treturn false;\n\t\t\t},\n\t\t\tgetTTL: function(key){\n\t\t\t\tvar curtime = +new Date(), ttl;\n\t\t\t\t_checkKey(key);\n\t\t\t\tif(_storage.hasOwnProperty(key) && _storage.__jstorage_meta.TTL && _storage.__jstorage_meta.TTL[key]) {\n\t\t\t\t\tttl = _storage.__jstorage_meta.TTL[key] - curtime;\n\t\t\t\t\treturn ttl || 0;\n\t\t\t\t}\n\t\t\t\treturn 0;\n\t\t\t},"}, {"tags": [], "description": {"full": "<p>Function: $.vakata.storage.flush<br />                Empty the storage.</p>\n\n<pre><code>            Returns:\n                _true_\n</code></pre>", "summary": "<p>Function: $.vakata.storage.flush<br />                Empty the storage.</p>", "body": "<pre><code>            Returns:\n                _true_\n</code></pre>"}, "ignore": true, "code": "flush : function(){\n\t\t\t\t_storage = {};\n\t\t\t\t_save();\n\t\t\t\t// try{ window.localStorage.clear(); } catch(E8) { }\n\t\t\t\treturn true;\n\t\t\t},"}, {"tags": [], "description": {"full": "<p>Function: $.vakata.storage.storageObj<br />                Get a read only copy of the whole storage.</p>\n\n<pre><code>            Returns:\n</code></pre>\n\n<p>object*</p>", "summary": "<p>Function: $.vakata.storage.storageObj<br />                Get a read only copy of the whole storage.</p>", "body": "<pre><code>            Returns:\n</code></pre>\n\n<p>object*</p>"}, "ignore": true, "code": "storageObj : function(){\n\t\t\t\treturn $.extend(true, {}, _storage);\n\t\t\t},"}, {"tags": [], "description": {"full": "<p>Function: $.vakata.storage.index<br />                Get an array of all the set keys in the storage.</p>\n\n<pre><code>            Returns:\n</code></pre>\n\n<p>array*</p>", "summary": "<p>Function: $.vakata.storage.index<br />                Get an array of all the set keys in the storage.</p>", "body": "<pre><code>            Returns:\n</code></pre>\n\n<p>array*</p>"}, "ignore": true, "code": "index : function(){\n\t\t\t\tvar index = [];\n\t\t\t\t$.each(_storage, function (i, v) { if(i !== \"__jstorage_meta\") { index.push(i); } });\n\t\t\t\treturn index;\n\t\t\t},"}, {"tags": [], "description": {"full": "<p>Function: $.vakata.storage.storageSize<br />                Get the size of all items in the storage in bytes.</p>\n\n<pre><code>            Returns:\n</code></pre>\n\n<p>number*</p>", "summary": "<p>Function: $.vakata.storage.storageSize<br />                Get the size of all items in the storage in bytes.</p>", "body": "<pre><code>            Returns:\n</code></pre>\n\n<p>number*</p>"}, "ignore": true, "code": "storageSize : function(){\n\t\t\t\treturn _storage_size;\n\t\t\t},"}, {"tags": [], "description": {"full": "<p>Function: $.vakata.storage.currentBackend<br />                Get the current backend used.</p>\n\n<pre><code>            Returns:\n</code></pre>\n\n<p>string*</p>", "summary": "<p>Function: $.vakata.storage.currentBackend<br />                Get the current backend used.</p>", "body": "<pre><code>            Returns:\n</code></pre>\n\n<p>string*</p>"}, "ignore": true, "code": "currentBackend : function(){\n\t\t\t\treturn _backend;\n\t\t\t},"}, {"tags": [], "description": {"full": "<p>Function: $.vakata.storage.storageAvailable<br />                See if storage functionality is available.</p>\n\n<pre><code>            Returns:\n</code></pre>\n\n<p>boolean*</p>", "summary": "<p>Function: $.vakata.storage.storageAvailable<br />                See if storage functionality is available.</p>", "body": "<pre><code>            Returns:\n</code></pre>\n\n<p>boolean*</p>"}, "ignore": true, "code": "storageAvailable : function(){\n\t\t\t\treturn !!_backend;\n\t\t\t}\n\t\t};\n\t\t_init();\n\t}(jQuery));\n\n\t// include the state plugin by default\n\t// $.jstree.defaults.plugins.push(\"state\");"}, {"tags": [], "description": {"full": "<h3>Types plugin</h3>\n\n<p>Makes it possible to add predefined types for groups of nodes, which make it possible to easily control nesting rules and icon for each group.</p>", "summary": "<h3>Types plugin</h3>", "body": "<p>Makes it possible to add predefined types for groups of nodes, which make it possible to easily control nesting rules and icon for each group.</p>"}, "ignore": false}, {"tags": [{"type": "name", "string": "$.jstree.defaults.types"}, {"type": "plugin", "string": "types"}], "description": {"full": "<p>An object storing all types as key value pairs, where the key is the type name and the value is an object that could contain following keys (all optional).</p>\n\n<ul>\n<li><code>max_children</code> the maximum number of immediate children this node type can have. Do not specify or set to <code>-1</code> for unlimited.</li>\n<li><code>max_depth</code> the maximum number of nesting this node type can have. A value of <code>1</code> would mean that the node can have children, but no grandchildren. Do not specify or set to <code>-1</code> for unlimited.</li>\n<li><code>valid_children</code> an array of node type strings, that nodes of this type can have as children. Do not specify or set to <code>-1</code> for no limits.</li>\n<li><code>icon</code> a string - can be a path to an icon or a className, if using an image that is in the current directory use a <code>./</code> prefix, otherwise it will be detected as a class. Omit to use the default icon from your theme.</li>\n</ul>\n\n<p>There are two predefined types:</p>\n\n<ul>\n<li><code>#</code> represents the root of the tree, for example <code>max_children</code> would control the maximum number of root nodes.</li>\n<li><code>default</code> represents the default node - any settings here will be applied to all nodes that do not have a type specified.</li>\n</ul>", "summary": "<p>An object storing all types as key value pairs, where the key is the type name and the value is an object that could contain following keys (all optional).</p>", "body": "<ul>\n<li><code>max_children</code> the maximum number of immediate children this node type can have. Do not specify or set to <code>-1</code> for unlimited.</li>\n<li><code>max_depth</code> the maximum number of nesting this node type can have. A value of <code>1</code> would mean that the node can have children, but no grandchildren. Do not specify or set to <code>-1</code> for unlimited.</li>\n<li><code>valid_children</code> an array of node type strings, that nodes of this type can have as children. Do not specify or set to <code>-1</code> for no limits.</li>\n<li><code>icon</code> a string - can be a path to an icon or a className, if using an image that is in the current directory use a <code>./</code> prefix, otherwise it will be detected as a class. Omit to use the default icon from your theme.</li>\n</ul>\n\n<p>There are two predefined types:</p>\n\n<ul>\n<li><code>#</code> represents the root of the tree, for example <code>max_children</code> would control the maximum number of root nodes.</li>\n<li><code>default</code> represents the default node - any settings here will be applied to all nodes that do not have a type specified.</li>\n</ul>"}, "isPrivate": false, "ignore": false, "code": "$.jstree.defaults.types = {\n\t\t'#' : {},\n\t\t'default' : {}\n\t};\n\n\t$.jstree.plugins.types = function (options, parent) {\n\t\tthis.init = function (el, options) {\n\t\t\tparent.init.call(this, el, options);\n\t\t\tthis._model.data['#'].type = '#';\n\t\t};\n\t\tthis.bind = function () {\n\t\t\tparent.bind.call(this);\n\t\t\tthis.element\n\t\t\t\t.on('model.jstree', $.proxy(function (e, data) {\n\t\t\t\t\t\tvar m = this._model.data,\n\t\t\t\t\t\t\tdpc = data.nodes,\n\t\t\t\t\t\t\tt = this.settings.types,\n\t\t\t\t\t\t\ti, j, c = 'default';\n\t\t\t\t\t\tfor(i = 0, j = dpc.length; i < j; i++) {\n\t\t\t\t\t\t\tc = 'default';\n\t\t\t\t\t\t\tif(m[dpc[i]].original && m[dpc[i]].original.type && t[m[dpc[i]].original.type]) {\n\t\t\t\t\t\t\t\tc = m[dpc[i]].original.type;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif(m[dpc[i]].data && m[dpc[i]].data.jstree && m[dpc[i]].data.jstree.type && t[m[dpc[i]].data.jstree.type]) {\n\t\t\t\t\t\t\t\tc = m[dpc[i]].data.jstree.type;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tm[dpc[i]].type = c;\n\t\t\t\t\t\t\tif(m[dpc[i]].icon === true && t[c].icon) {\n\t\t\t\t\t\t\t\tm[dpc[i]].icon = t[c].icon;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}, this));\n\t\t};\n\t\tthis.get_json = function (obj, options) {\n\t\t\tvar i, j,\n\t\t\t\tm = this._model.data,\n\t\t\t\ttmp = parent.get_json.call(this, obj, options);\n\t\t\tif(tmp === false) { return false; }\n\t\t\tif($.isArray(tmp)) {\n\t\t\t\tfor(i = 0, j = tmp.length; i < j; i++) {\n\t\t\t\t\ttmp[i].type = tmp[i].id && m[tmp[i].id] && m[tmp[i].id].type ? m[tmp[i].id].type : \"default\";\n\t\t\t\t}\n\t\t\t}\n\t\t\telse {\n\t\t\t\ttmp.type = tmp.id && m[tmp.id] && m[tmp.id].type ? m[tmp.id].type : \"default\";\n\t\t\t}\n\t\t\treturn tmp;\n\t\t};\n\t\tthis.check = function (chk, obj, par, pos) {\n\t\t\tif(parent.check.call(this, chk, obj, par, pos) === false) { return false; }\n\t\t\tobj = obj && obj.id ? obj : this.get_node(obj);\n\t\t\tpar = par && par.id ? par : this.get_node(par);\n\t\t\tvar m = this._model.data, tmp, d, i, j;\n\t\t\tswitch(chk) {\n\t\t\t\tcase \"create_node\":\n\t\t\t\tcase \"move_node\":\n\t\t\t\tcase \"copy_node\":\n\t\t\t\t\tif(chk !== 'move_node' || $.inArray(obj.id, par.children) === -1) {\n\t\t\t\t\t\ttmp = this.get_rules(par);\n\t\t\t\t\t\tif(tmp.max_children !== undefined && tmp.max_children !== -1 && tmp.max_children === par.children.length) {\n\t\t\t\t\t\t\treturn false;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif(tmp.valid_children !== undefined && tmp.valid_children !== -1 && $.inArray(obj.type, tmp.valid_children) === -1) {\n\t\t\t\t\t\t\treturn false;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif(obj.children_d && obj.parents) {\n\t\t\t\t\t\t\td = 0;\n\t\t\t\t\t\t\tfor(i = 0, j = obj.children_d.length; i < j; i++) {\n\t\t\t\t\t\t\t\td = Math.max(d, m[obj.children_d[i]].parents.length);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\td = d - obj.parents.length + 1;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif(d <= 0) { d = 1; }\n\t\t\t\t\t\tdo {\n\t\t\t\t\t\t\tif(tmp.max_depth !== undefined && tmp.max_depth !== -1 && tmp.max_depth < d) {\n\t\t\t\t\t\t\t\treturn false;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tpar = this.get_node(par.parent);\n\t\t\t\t\t\t\ttmp = this.get_rules(par);\n\t\t\t\t\t\t\td++;\n\t\t\t\t\t\t} while(par);\n\t\t\t\t\t}\n\t\t\t\t\tbreak;\n\t\t\t}\n\t\t\treturn true;\n\t\t};"}, {"tags": [{"type": "name", "string": "get_rules(obj)"}, {"type": "param", "types": ["mixed"], "name": "obj", "description": "the node to find the rules for"}, {"type": "return", "types": ["Object"], "description": ""}, {"type": "plugin", "string": "types"}], "description": {"full": "<p>used to retrieve the type settings object for a node</p>", "summary": "<p>used to retrieve the type settings object for a node</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "this.get_rules = function (obj) {\n\t\t\tobj = this.get_node(obj);\n\t\t\tif(!obj) { return false; }\n\t\t\tvar tmp = this.get_type(obj, true);\n\t\t\tif(tmp.max_depth === undefined) { tmp.max_depth = -1; }\n\t\t\tif(tmp.max_children === undefined) { tmp.max_children = -1; }\n\t\t\tif(tmp.valid_children === undefined) { tmp.valid_children = -1; }\n\t\t\treturn tmp;\n\t\t};", "ctx": {"type": "method", "receiver": "this", "name": "get_rules", "string": "this.get_rules()"}}, {"tags": [{"type": "name", "string": "get_type(obj [, rules])"}, {"type": "param", "types": ["mixed"], "name": "obj", "description": "the node to find the rules for"}, {"type": "param", "types": ["Boolean"], "name": "rules", "description": "if set to `true` instead of a string the settings object will be returned"}, {"type": "return", "types": ["String", "Object"], "description": ""}, {"type": "plugin", "string": "types"}], "description": {"full": "<p>used to retrieve the type string or settings object for a node</p>", "summary": "<p>used to retrieve the type string or settings object for a node</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "this.get_type = function (obj, rules) {\n\t\t\tobj = this.get_node(obj);\n\t\t\treturn (!obj) ? false : ( rules ? $.extend({ 'type' : obj.type }, this.settings.types[obj.type]) : obj.type);\n\t\t};", "ctx": {"type": "method", "receiver": "this", "name": "get_type", "string": "this.get_type()"}}, {"tags": [{"type": "name", "string": "set_type(obj, type)"}, {"type": "param", "types": ["mixed"], "name": "obj", "description": "the node to change"}, {"type": "param", "types": ["String"], "name": "type", "description": "the new type"}, {"type": "plugin", "string": "types"}], "description": {"full": "<p>used to change a node's type</p>", "summary": "<p>used to change a node's type</p>", "body": ""}, "isPrivate": false, "ignore": false, "code": "this.set_type = function (obj, type) {\n\t\t\tvar t, t1, t2, old_type, old_icon;\n\t\t\tif($.isArray(obj)) {\n\t\t\t\tobj = obj.slice();\n\t\t\t\tfor(t1 = 0, t2 = obj.length; t1 < t2; t1++) {\n\t\t\t\t\tthis.set_type(obj[t1], type);\n\t\t\t\t}\n\t\t\t\treturn true;\n\t\t\t}\n\t\t\tt = this.settings.types;\n\t\t\tobj = this.get_node(obj);\n\t\t\tif(!t[type] || !obj) { return false; }\n\t\t\told_type = obj.type;\n\t\t\told_icon = this.get_icon(obj);\n\t\t\tobj.type = type;\n\t\t\tif(old_icon === true || (t[old_type] && t[old_type].icon && old_icon === t[old_type].icon)) {\n\t\t\t\tthis.set_icon(obj, t[type].icon || true);\n\t\t\t}\n\t\t\treturn true;\n\t\t};\n\t};\n\t// include the types plugin by default\n\t// $.jstree.defaults.plugins.push(\"types\");", "ctx": {"type": "method", "receiver": "this", "name": "set_type", "string": "this.set_type()"}}, {"tags": [], "description": {"full": "<h3>Unique plugin</h3>\n\n<p>Enforces that no nodes with the same name can coexist as siblings.</p>", "summary": "<h3>Unique plugin</h3>", "body": "<p>Enforces that no nodes with the same name can coexist as siblings.</p>"}, "ignore": false, "code": "$.jstree.plugins.unique = function (options, parent) {\n\t\tthis.check = function (chk, obj, par, pos) {\n\t\t\tif(parent.check.call(this, chk, obj, par, pos) === false) { return false; }\n\t\t\tobj = obj && obj.id ? obj : this.get_node(obj);\n\t\t\tpar = par && par.id ? par : this.get_node(par);\n\t\t\tif(!par || !par.children) { return true; }\n\t\t\tvar n = chk === \"rename_node\" ? pos : obj.text,\n\t\t\t\tc = [],\n\t\t\t\tm = this._model.data, i, j;\n\t\t\tfor(i = 0, j = par.children.length; i < j; i++) {\n\t\t\t\tc.push(m[par.children[i]].text);\n\t\t\t}\n\t\t\tswitch(chk) {\n\t\t\t\tcase \"delete_node\":\n\t\t\t\t\treturn true;\n\t\t\t\tcase \"rename_node\":\n\t\t\t\tcase \"copy_node\":\n\t\t\t\t\treturn ($.inArray(n, c) === -1);\n\t\t\t\tcase \"move_node\":\n\t\t\t\t\treturn (obj.parent === par.id || $.inArray(n, c) === -1);\n\t\t\t}\n\t\t\treturn true;\n\t\t};\n\t};\n\n\t// include the unique plugin by default\n\t// $.jstree.defaults.plugins.push(\"unique\");", "ctx": {"type": "method", "receiver": "$.jstree.plugins", "name": "unique", "string": "$.jstree.plugins.unique()"}}, {"tags": [], "description": {"full": "<h3>Wholerow plugin</h3>\n\n<p>Makes each node appear block level. Making selection easier. May cause slow down for large trees in old browsers.</p>", "summary": "<h3>Wholerow plugin</h3>", "body": "<p>Makes each node appear block level. Making selection easier. May cause slow down for large trees in old browsers.</p>"}, "ignore": false, "code": "var div = document.createElement('DIV');\n\tdiv.setAttribute('unselectable','on');\n\tdiv.className = 'jstree-wholerow';\n\tdiv.innerHTML = '&#160;';\n\t$.jstree.plugins.wholerow = function (options, parent) {\n\t\tthis.bind = function () {\n\t\t\tparent.bind.call(this);\n\n\t\t\tthis.element\n\t\t\t\t.on('loading', $.proxy(function () {\n\t\t\t\t\t\tdiv.style.height = this._data.core.li_height + 'px';\n\t\t\t\t\t}, this))\n\t\t\t\t.on('ready.jstree set_state.jstree', $.proxy(function () {\n\t\t\t\t\t\tthis.hide_dots();\n\t\t\t\t\t}, this))\n\t\t\t\t.on(\"ready.jstree\", $.proxy(function () {\n\t\t\t\t\t\tthis.get_container_ul().addClass('jstree-wholerow-ul');\n\t\t\t\t\t}, this))\n\t\t\t\t.on(\"deselect_all.jstree\", $.proxy(function (e, data) {\n\t\t\t\t\t\tthis.element.find('.jstree-wholerow-clicked').removeClass('jstree-wholerow-clicked');\n\t\t\t\t\t}, this))\n\t\t\t\t.on(\"changed.jstree\", $.proxy(function (e, data) {\n\t\t\t\t\t\tthis.element.find('.jstree-wholerow-clicked').removeClass('jstree-wholerow-clicked');\n\t\t\t\t\t\tvar tmp = false, i, j;\n\t\t\t\t\t\tfor(i = 0, j = data.selected.length; i < j; i++) {\n\t\t\t\t\t\t\ttmp = this.get_node(data.selected[i], true);\n\t\t\t\t\t\t\tif(tmp && tmp.length) {\n\t\t\t\t\t\t\t\ttmp.children('.jstree-wholerow').addClass('jstree-wholerow-clicked');\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}, this))\n\t\t\t\t.on(\"open_node.jstree\", $.proxy(function (e, data) {\n\t\t\t\t\t\tthis.get_node(data.node, true).find('.jstree-clicked').parent().children('.jstree-wholerow').addClass('jstree-wholerow-clicked');\n\t\t\t\t\t}, this))\n\t\t\t\t.on(\"hover_node.jstree dehover_node.jstree\", $.proxy(function (e, data) {\n\t\t\t\t\t\tthis.element.find('.jstree-wholerow-hovered').removeClass('jstree-wholerow-hovered');\n\t\t\t\t\t\tif(e.type === \"hover_node\") {\n\t\t\t\t\t\t\tthis.get_node(data.node, true).each(function () {\n\t\t\t\t\t\t\t\t$(this).children('.jstree-wholerow').addClass('jstree-wholerow-hovered');\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t}, this))\n\t\t\t\t.on(\"contextmenu.jstree\", \".jstree-wholerow\", $.proxy(function (e) {\n\t\t\t\t\t\t//if(typeof this._data.contextmenu !== 'undefined') {\n\t\t\t\t\t\t\te.preventDefault();\n\t\t\t\t\t\t\t$(e.currentTarget).closest(\"li\").children(\"a:eq(0)\").trigger('contextmenu',e);\n\t\t\t\t\t\t//}\n\t\t\t\t\t}, this))\n\t\t\t\t.on(\"click.jstree\", \".jstree-wholerow\", function (e) {\n\t\t\t\t\t\te.stopImmediatePropagation();\n\t\t\t\t\t\tvar tmp = $.Event('click', { metaKey : e.metaKey, ctrlKey : e.ctrlKey, altKey : e.altKey, shiftKey : e.shiftKey });\n\t\t\t\t\t\t$(e.currentTarget).closest(\"li\").children(\"a:eq(0)\").trigger(tmp).focus();\n\t\t\t\t\t})\n\t\t\t\t.on(\"click.jstree\", \".jstree-leaf > .jstree-ocl\", $.proxy(function (e) {\n\t\t\t\t\t\te.stopImmediatePropagation();\n\t\t\t\t\t\tvar tmp = $.Event('click', { metaKey : e.metaKey, ctrlKey : e.ctrlKey, altKey : e.altKey, shiftKey : e.shiftKey });\n\t\t\t\t\t\t$(e.currentTarget).closest(\"li\").children(\"a:eq(0)\").trigger(tmp).focus();\n\t\t\t\t\t}, this))\n\t\t\t\t.on(\"mouseover.jstree\", \".jstree-wholerow, .jstree-icon\", $.proxy(function (e) {\n\t\t\t\t\t\te.stopImmediatePropagation();\n\t\t\t\t\t\t//if($(e.currentTarget).closest('li').children(\".jstree-clicked\").length) {\n\t\t\t\t\t\t//\treturn false;\n\t\t\t\t\t\t//}\n\t\t\t\t\t\tthis.hover_node(e.currentTarget);\n\t\t\t\t\t\treturn false;\n\t\t\t\t\t}, this))\n\t\t\t\t.on(\"mouseleave.jstree\", \"li\", $.proxy(function (e) {\n\t\t\t\t\t\tthis.dehover_node(e.currentTarget);\n\t\t\t\t\t}, this));\n\t\t};\n\t\tthis.teardown = function () {\n\t\t\tif(this.settings.wholerow) {\n\t\t\t\tthis.element.find(\".jstree-wholerow\").remove();\n\t\t\t}\n\t\t\tparent.teardown.call(this);\n\t\t};\n\t\tthis.redraw_node = function(obj, deep, callback) {\n\t\t\tobj = parent.redraw_node.call(this, obj, deep, callback);\n\t\t\tif(obj) {\n\t\t\t\tvar tmp = div.cloneNode(true);\n\t\t\t\t//tmp.style.height = this._data.core.li_height + 'px';\n\t\t\t\tif($.inArray(obj.id, this._data.core.selected) !== -1) { tmp.className += ' jstree-wholerow-clicked'; }\n\t\t\t\tobj.insertBefore(tmp, obj.childNodes[0]);\n\t\t\t}\n\t\t\treturn obj;\n\t\t};\n\t};\n\t// include the wholerow plugin by default\n\t// $.jstree.defaults.plugins.push(\"wholerow\");\n\n}));", "ctx": {"type": "declaration", "name": "div", "value": "document.createElement('DIV')", "string": "div"}}]