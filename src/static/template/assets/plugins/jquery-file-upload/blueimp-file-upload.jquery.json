{"name": "blueimp-file-upload", "version": "9.5.2", "title": "jQuery File Upload", "author": {"name": "<PERSON>", "url": "https://blueimp.net"}, "licenses": [{"type": "MIT", "url": "http://www.opensource.org/licenses/MIT"}], "dependencies": {"jquery": ">=1.6"}, "description": "File Upload widget with multiple file selection, drag&amp;drop support, progress bar, validation and preview images, audio and video for jQuery. Supports cross-domain, chunked and resumable file uploads. Works with any server-side platform (Google App Engine, PHP, Python, Ruby on Rails, Java, etc.) that supports standard HTML form file uploads.", "keywords": ["j<PERSON>y", "file", "upload", "widget", "multiple", "selection", "drag", "drop", "progress", "preview", "cross-domain", "cross-site", "chunk", "resume", "gae", "go", "python", "php", "bootstrap"], "homepage": "https://github.com/blueimp/jQuery-File-Upload", "docs": "https://github.com/blueimp/jQuery-File-Upload/wiki", "demo": "http://blueimp.github.io/jQuery-File-Upload/", "bugs": "https://github.com/blueimp/jQuery-File-Upload/issues", "maintainers": [{"name": "<PERSON>", "url": "https://blueimp.net"}]}