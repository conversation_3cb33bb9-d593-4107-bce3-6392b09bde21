{"name": "blueimp-file-upload-node", "version": "2.1.0", "title": "jQuery File Upload Node.js example", "description": "Node.js implementation example of a file upload handler for jQuery File Upload.", "keywords": ["file", "upload", "cross-domain", "cross-site", "node"], "homepage": "https://github.com/blueimp/jQuery-File-Upload", "author": {"name": "<PERSON>", "url": "https://blueimp.net"}, "maintainers": [{"name": "<PERSON>", "url": "https://blueimp.net"}], "repository": {"type": "git", "url": "git://github.com/blueimp/jQuery-File-Upload.git"}, "bugs": "https://github.com/blueimp/jQuery-File-Upload/issues", "licenses": [{"type": "MIT", "url": "http://www.opensource.org/licenses/MIT"}], "dependencies": {"formidable": ">=1.0.11", "node-static": ">=0.6.5", "imagemagick": ">=0.1.3"}, "main": "server.js"}