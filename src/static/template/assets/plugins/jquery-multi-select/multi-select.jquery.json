{"name": "multi-select", "title": "multiselect", "description": "This is a user-friendlier drop-in replacement for the standard <select> with multiple attribute activated.", "keywords": ["multiselect", "select", "multiple", "form"], "version": "0.9.8", "author": {"name": "lou", "email": "louis<PERSON><EMAIL>", "url": "https://github.com/lou"}, "maintainers": [{"name": "Louis CUNY", "email": "louis<PERSON><EMAIL>", "url": "https://github.com/lou"}], "licenses": [{"type": "wtfpl", "url": "http://sam.zoy.org/wtfpl/COPYING"}], "bugs": "https://github.com/lou/multi-select/issues", "homepage": "http://loudev.com/", "docs": "http://loudev.com/", "download": "https://github.com/lou/multi-select/archive/master.zip", "dependencies": {"jquery": ">= 1.8"}}