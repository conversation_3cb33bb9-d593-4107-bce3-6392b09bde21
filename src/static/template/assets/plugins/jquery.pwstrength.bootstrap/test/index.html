<!DOCTYPE html>
<html>
<head>
    <title>Bootstrap Password Strength Meter Example</title>
    <link rel="stylesheet" media="screen" href="bootstrap.css" />
</head>
<body>
    <form>
        <fieldset>
            <legend>Please type in your password</legend>
            User: <input type="text" id="username" /><br />
            Pass: <input type="password" id="password" />
            <div id="messages"></div>
        </fieldset>
    </form>

    <script src="//ajax.googleapis.com/ajax/libs/jquery/1.8.3/jquery.min.js"></script>
    <script type="text/javascript" src="pwstrength.js"></script>
    <script type="text/javascript">
        jQuery(document).ready(function () {
            "use strict";
            var options = {
                onLoad: function () {
                    $('#messages').text('Start typing password');
                },
                onKeyUp: function (evt) {
                    $(evt.target).pwstrength("outputErrorList");
                }
            };
            $(':password').pwstrength(options);
        });
    </script>
</body>
</html>
