/**
 * Swedish translation for bootstrap-wysihtml5
 */
(function($){
    $.fn.wysihtml5.locale["sv-SE"] = {
        font_styles: {
            normal: "Normal Text",
            h1: "Rubrik 1",
            h2: "Rubrik 2",
            h3: "Rubrik 3"
        },
        emphasis: {
            bold: "Fet",
            italic: "Kursiv",
            underline: "Understruken"
        },
        lists: {
            unordered: "Osorterad lista",
            ordered: "Sorterad lista",
            outdent: "Minska indrag",
            indent: "Öka indrag"
        },
        link: {
            insert: "<PERSON>ägg till länk",
            cancel: "Avbryt"
        },
        image: {
            insert: "<PERSON>ägg till Bild",
            cancel: "Avbryt"
        },
        html: {
            edit: "Redigera HTML"
        },
        colours: {
            black: "Svart",
            silver: "Silver",
            gray: "Grå",
            maroon: "Ka<PERSON><PERSON><PERSON><PERSON>",
            red: "<PERSON>ö<PERSON>",
            purple: "<PERSON>",
            green: "<PERSON><PERSON><PERSON><PERSON>",
            olive: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
            navy: "<PERSON><PERSON><PERSON><PERSON>",
            blue: "<PERSON><PERSON><PERSON>",
            orange: "Orange"
        }
    };
}(jQuery));