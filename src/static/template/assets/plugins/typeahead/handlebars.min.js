/*!

 handlebars v1.3.0

Copyright (C) 2011 by <PERSON><PERSON><PERSON>

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in
all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
THE SOFTWARE.

@license
*/
/* exported Handlebars */
var Handlebars=function(){var t=function(){"use strict";function t(t){this.string=t}var e;return t.prototype.toString=function(){return""+this.string},e=t}(),e=function(t){"use strict";function e(t){return a[t]||"&amp;"}function s(t,e){for(var s in e)Object.prototype.hasOwnProperty.call(e,s)&&(t[s]=e[s])}function i(t){return t instanceof o?t.toString():t||0===t?(t=""+t,p.test(t)?t.replace(h,e):t):""}function n(t){return t||0===t?u(t)&&0===t.length?!0:!1:!0}var r={},o=t,a={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#x27;","`":"&#x60;"},h=/[&<>"'`]/g,p=/[&<>"'`]/;r.extend=s;var l=Object.prototype.toString;r.toString=l;var c=function(t){return"function"==typeof t};c(/x/)&&(c=function(t){return"function"==typeof t&&"[object Function]"===l.call(t)});var c;r.isFunction=c;var u=Array.isArray||function(t){return t&&"object"==typeof t?"[object Array]"===l.call(t):!1};return r.isArray=u,r.escapeExpression=i,r.isEmpty=n,r}(t),s=function(){"use strict";function t(t,e){var i;e&&e.firstLine&&(i=e.firstLine,t+=" - "+i+":"+e.firstColumn);for(var n=Error.prototype.constructor.call(this,t),r=0;r<s.length;r++)this[s[r]]=n[s[r]];i&&(this.lineNumber=i,this.column=e.firstColumn)}var e,s=["description","fileName","lineNumber","message","name","number","stack"];return t.prototype=new Error,e=t}(),i=function(t,e){"use strict";function s(t,e){this.helpers=t||{},this.partials=e||{},i(this)}function i(t){t.registerHelper("helperMissing",function(t){if(2===arguments.length)return void 0;throw new a("Missing helper: '"+t+"'")}),t.registerHelper("blockHelperMissing",function(e,s){var i=s.inverse||function(){},n=s.fn;return u(e)&&(e=e.call(this)),e===!0?n(this):e===!1||null==e?i(this):c(e)?e.length>0?t.helpers.each(e,s):i(this):n(e)}),t.registerHelper("each",function(t,e){var s,i=e.fn,n=e.inverse,r=0,o="";if(u(t)&&(t=t.call(this)),e.data&&(s=m(e.data)),t&&"object"==typeof t)if(c(t))for(var a=t.length;a>r;r++)s&&(s.index=r,s.first=0===r,s.last=r===t.length-1),o+=i(t[r],{data:s});else for(var h in t)t.hasOwnProperty(h)&&(s&&(s.key=h,s.index=r,s.first=0===r),o+=i(t[h],{data:s}),r++);return 0===r&&(o=n(this)),o}),t.registerHelper("if",function(t,e){return u(t)&&(t=t.call(this)),!e.hash.includeZero&&!t||o.isEmpty(t)?e.inverse(this):e.fn(this)}),t.registerHelper("unless",function(e,s){return t.helpers["if"].call(this,e,{fn:s.inverse,inverse:s.fn,hash:s.hash})}),t.registerHelper("with",function(t,e){return u(t)&&(t=t.call(this)),o.isEmpty(t)?void 0:e.fn(t)}),t.registerHelper("log",function(e,s){var i=s.data&&null!=s.data.level?parseInt(s.data.level,10):1;t.log(i,e)})}function n(t,e){g.log(t,e)}var r={},o=t,a=e,h="1.3.0";r.VERSION=h;var p=4;r.COMPILER_REVISION=p;var l={1:"<= 1.0.rc.2",2:"== 1.0.0-rc.3",3:"== 1.0.0-rc.4",4:">= 1.0.0"};r.REVISION_CHANGES=l;var c=o.isArray,u=o.isFunction,f=o.toString,d="[object Object]";r.HandlebarsEnvironment=s,s.prototype={constructor:s,logger:g,log:n,registerHelper:function(t,e,s){if(f.call(t)===d){if(s||e)throw new a("Arg not supported with multiple helpers");o.extend(this.helpers,t)}else s&&(e.not=s),this.helpers[t]=e},registerPartial:function(t,e){f.call(t)===d?o.extend(this.partials,t):this.partials[t]=e}};var g={methodMap:{0:"debug",1:"info",2:"warn",3:"error"},DEBUG:0,INFO:1,WARN:2,ERROR:3,level:3,log:function(t,e){if(g.level<=t){var s=g.methodMap[t];"undefined"!=typeof console&&console[s]&&console[s].call(console,e)}}};r.logger=g,r.log=n;var m=function(t){var e={};return o.extend(e,t),e};return r.createFrame=m,r}(e,s),n=function(t,e,s){"use strict";function i(t){var e=t&&t[0]||1,s=u;if(e!==s){if(s>e){var i=f[s],n=f[e];throw new c("Template was precompiled with an older version of Handlebars than the current runtime. Please update your precompiler to a newer version ("+i+") or downgrade your runtime to an older version ("+n+").")}throw new c("Template was precompiled with a newer version of Handlebars than the current runtime. Please update your runtime to a newer version ("+t[1]+").")}}function n(t,e){if(!e)throw new c("No environment passed to template");var s=function(t,s,i,n,r,o){var a=e.VM.invokePartial.apply(this,arguments);if(null!=a)return a;if(e.compile){var h={helpers:n,partials:r,data:o};return r[s]=e.compile(t,{data:void 0!==o},e),r[s](i,h)}throw new c("The partial "+s+" could not be compiled when running in runtime-only mode")},i={escapeExpression:l.escapeExpression,invokePartial:s,programs:[],program:function(t,e,s){var i=this.programs[t];return s?i=o(t,e,s):i||(i=this.programs[t]=o(t,e)),i},merge:function(t,e){var s=t||e;return t&&e&&t!==e&&(s={},l.extend(s,e),l.extend(s,t)),s},programWithDepth:e.VM.programWithDepth,noop:e.VM.noop,compilerInfo:null};return function(s,n){n=n||{};var r,o,a=n.partial?n:e;n.partial||(r=n.helpers,o=n.partials);var h=t.call(i,a,s,r,o,n.data);return n.partial||e.VM.checkRevision(i.compilerInfo),h}}function r(t,e,s){var i=Array.prototype.slice.call(arguments,3),n=function(t,n){return n=n||{},e.apply(this,[t,n.data||s].concat(i))};return n.program=t,n.depth=i.length,n}function o(t,e,s){var i=function(t,i){return i=i||{},e(t,i.data||s)};return i.program=t,i.depth=0,i}function a(t,e,s,i,n,r){var o={partial:!0,helpers:i,partials:n,data:r};if(void 0===t)throw new c("The partial "+e+" could not be found");return t instanceof Function?t(s,o):void 0}function h(){return""}var p={},l=t,c=e,u=s.COMPILER_REVISION,f=s.REVISION_CHANGES;return p.checkRevision=i,p.template=n,p.programWithDepth=r,p.program=o,p.invokePartial=a,p.noop=h,p}(e,s,i),r=function(t,e,s,i,n){"use strict";var r,o=t,a=e,h=s,p=i,l=n,c=function(){var t=new o.HandlebarsEnvironment;return p.extend(t,o),t.SafeString=a,t.Exception=h,t.Utils=p,t.VM=l,t.template=function(e){return l.template(e,t)},t},u=c();return u.create=c,r=u}(i,t,s,e,n),o=function(t){"use strict";function e(t){t=t||{},this.firstLine=t.first_line,this.firstColumn=t.first_column,this.lastColumn=t.last_column,this.lastLine=t.last_line}var s,i=t,n={ProgramNode:function(t,s,i,r){var o,a;3===arguments.length?(r=i,i=null):2===arguments.length&&(r=s,s=null),e.call(this,r),this.type="program",this.statements=t,this.strip={},i?(a=i[0],a?(o={first_line:a.firstLine,last_line:a.lastLine,last_column:a.lastColumn,first_column:a.firstColumn},this.inverse=new n.ProgramNode(i,s,o)):this.inverse=new n.ProgramNode(i,s),this.strip.right=s.left):s&&(this.strip.left=s.right)},MustacheNode:function(t,s,i,r,o){if(e.call(this,o),this.type="mustache",this.strip=r,null!=i&&i.charAt){var a=i.charAt(3)||i.charAt(2);this.escaped="{"!==a&&"&"!==a}else this.escaped=!!i;this.sexpr=t instanceof n.SexprNode?t:new n.SexprNode(t,s),this.sexpr.isRoot=!0,this.id=this.sexpr.id,this.params=this.sexpr.params,this.hash=this.sexpr.hash,this.eligibleHelper=this.sexpr.eligibleHelper,this.isHelper=this.sexpr.isHelper},SexprNode:function(t,s,i){e.call(this,i),this.type="sexpr",this.hash=s;var n=this.id=t[0],r=this.params=t.slice(1),o=this.eligibleHelper=n.isSimple;this.isHelper=o&&(r.length||s)},PartialNode:function(t,s,i,n){e.call(this,n),this.type="partial",this.partialName=t,this.context=s,this.strip=i},BlockNode:function(t,s,n,r,o){if(e.call(this,o),t.sexpr.id.original!==r.path.original)throw new i(t.sexpr.id.original+" doesn't match "+r.path.original,this);this.type="block",this.mustache=t,this.program=s,this.inverse=n,this.strip={left:t.strip.left,right:r.strip.right},(s||n).strip.left=t.strip.right,(n||s).strip.right=r.strip.left,n&&!s&&(this.isInverse=!0)},ContentNode:function(t,s){e.call(this,s),this.type="content",this.string=t},HashNode:function(t,s){e.call(this,s),this.type="hash",this.pairs=t},IdNode:function(t,s){e.call(this,s),this.type="ID";for(var n="",r=[],o=0,a=0,h=t.length;h>a;a++){var p=t[a].part;if(n+=(t[a].separator||"")+p,".."===p||"."===p||"this"===p){if(r.length>0)throw new i("Invalid path: "+n,this);".."===p?o++:this.isScoped=!0}else r.push(p)}this.original=n,this.parts=r,this.string=r.join("."),this.depth=o,this.isSimple=1===t.length&&!this.isScoped&&0===o,this.stringModeValue=this.string},PartialNameNode:function(t,s){e.call(this,s),this.type="PARTIAL_NAME",this.name=t.original},DataNode:function(t,s){e.call(this,s),this.type="DATA",this.id=t},StringNode:function(t,s){e.call(this,s),this.type="STRING",this.original=this.string=this.stringModeValue=t},IntegerNode:function(t,s){e.call(this,s),this.type="INTEGER",this.original=this.integer=t,this.stringModeValue=Number(t)},BooleanNode:function(t,s){e.call(this,s),this.type="BOOLEAN",this.bool=t,this.stringModeValue="true"===t},CommentNode:function(t,s){e.call(this,s),this.type="comment",this.comment=t}};return s=n}(s),a=function(){"use strict";var t,e=function(){function t(t,e){return{left:"~"===t.charAt(2),right:"~"===e.charAt(0)||"~"===e.charAt(1)}}function e(){this.yy={}}var s={trace:function(){},yy:{},symbols_:{error:2,root:3,statements:4,EOF:5,program:6,simpleInverse:7,statement:8,openInverse:9,closeBlock:10,openBlock:11,mustache:12,partial:13,CONTENT:14,COMMENT:15,OPEN_BLOCK:16,sexpr:17,CLOSE:18,OPEN_INVERSE:19,OPEN_ENDBLOCK:20,path:21,OPEN:22,OPEN_UNESCAPED:23,CLOSE_UNESCAPED:24,OPEN_PARTIAL:25,partialName:26,partial_option0:27,sexpr_repetition0:28,sexpr_option0:29,dataName:30,param:31,STRING:32,INTEGER:33,BOOLEAN:34,OPEN_SEXPR:35,CLOSE_SEXPR:36,hash:37,hash_repetition_plus0:38,hashSegment:39,ID:40,EQUALS:41,DATA:42,pathSegments:43,SEP:44,$accept:0,$end:1},terminals_:{2:"error",5:"EOF",14:"CONTENT",15:"COMMENT",16:"OPEN_BLOCK",18:"CLOSE",19:"OPEN_INVERSE",20:"OPEN_ENDBLOCK",22:"OPEN",23:"OPEN_UNESCAPED",24:"CLOSE_UNESCAPED",25:"OPEN_PARTIAL",32:"STRING",33:"INTEGER",34:"BOOLEAN",35:"OPEN_SEXPR",36:"CLOSE_SEXPR",40:"ID",41:"EQUALS",42:"DATA",44:"SEP"},productions_:[0,[3,2],[3,1],[6,2],[6,3],[6,2],[6,1],[6,1],[6,0],[4,1],[4,2],[8,3],[8,3],[8,1],[8,1],[8,1],[8,1],[11,3],[9,3],[10,3],[12,3],[12,3],[13,4],[7,2],[17,3],[17,1],[31,1],[31,1],[31,1],[31,1],[31,1],[31,3],[37,1],[39,3],[26,1],[26,1],[26,1],[30,2],[21,1],[43,3],[43,1],[27,0],[27,1],[28,0],[28,2],[29,0],[29,1],[38,1],[38,2]],performAction:function(e,s,i,n,r,o){var a=o.length-1;switch(r){case 1:return new n.ProgramNode(o[a-1],this._$);case 2:return new n.ProgramNode([],this._$);case 3:this.$=new n.ProgramNode([],o[a-1],o[a],this._$);break;case 4:this.$=new n.ProgramNode(o[a-2],o[a-1],o[a],this._$);break;case 5:this.$=new n.ProgramNode(o[a-1],o[a],[],this._$);break;case 6:this.$=new n.ProgramNode(o[a],this._$);break;case 7:this.$=new n.ProgramNode([],this._$);break;case 8:this.$=new n.ProgramNode([],this._$);break;case 9:this.$=[o[a]];break;case 10:o[a-1].push(o[a]),this.$=o[a-1];break;case 11:this.$=new n.BlockNode(o[a-2],o[a-1].inverse,o[a-1],o[a],this._$);break;case 12:this.$=new n.BlockNode(o[a-2],o[a-1],o[a-1].inverse,o[a],this._$);break;case 13:this.$=o[a];break;case 14:this.$=o[a];break;case 15:this.$=new n.ContentNode(o[a],this._$);break;case 16:this.$=new n.CommentNode(o[a],this._$);break;case 17:this.$=new n.MustacheNode(o[a-1],null,o[a-2],t(o[a-2],o[a]),this._$);break;case 18:this.$=new n.MustacheNode(o[a-1],null,o[a-2],t(o[a-2],o[a]),this._$);break;case 19:this.$={path:o[a-1],strip:t(o[a-2],o[a])};break;case 20:this.$=new n.MustacheNode(o[a-1],null,o[a-2],t(o[a-2],o[a]),this._$);break;case 21:this.$=new n.MustacheNode(o[a-1],null,o[a-2],t(o[a-2],o[a]),this._$);break;case 22:this.$=new n.PartialNode(o[a-2],o[a-1],t(o[a-3],o[a]),this._$);break;case 23:this.$=t(o[a-1],o[a]);break;case 24:this.$=new n.SexprNode([o[a-2]].concat(o[a-1]),o[a],this._$);break;case 25:this.$=new n.SexprNode([o[a]],null,this._$);break;case 26:this.$=o[a];break;case 27:this.$=new n.StringNode(o[a],this._$);break;case 28:this.$=new n.IntegerNode(o[a],this._$);break;case 29:this.$=new n.BooleanNode(o[a],this._$);break;case 30:this.$=o[a];break;case 31:o[a-1].isHelper=!0,this.$=o[a-1];break;case 32:this.$=new n.HashNode(o[a],this._$);break;case 33:this.$=[o[a-2],o[a]];break;case 34:this.$=new n.PartialNameNode(o[a],this._$);break;case 35:this.$=new n.PartialNameNode(new n.StringNode(o[a],this._$),this._$);break;case 36:this.$=new n.PartialNameNode(new n.IntegerNode(o[a],this._$));break;case 37:this.$=new n.DataNode(o[a],this._$);break;case 38:this.$=new n.IdNode(o[a],this._$);break;case 39:o[a-2].push({part:o[a],separator:o[a-1]}),this.$=o[a-2];break;case 40:this.$=[{part:o[a]}];break;case 43:this.$=[];break;case 44:o[a-1].push(o[a]);break;case 47:this.$=[o[a]];break;case 48:o[a-1].push(o[a])}},table:[{3:1,4:2,5:[1,3],8:4,9:5,11:6,12:7,13:8,14:[1,9],15:[1,10],16:[1,12],19:[1,11],22:[1,13],23:[1,14],25:[1,15]},{1:[3]},{5:[1,16],8:17,9:5,11:6,12:7,13:8,14:[1,9],15:[1,10],16:[1,12],19:[1,11],22:[1,13],23:[1,14],25:[1,15]},{1:[2,2]},{5:[2,9],14:[2,9],15:[2,9],16:[2,9],19:[2,9],20:[2,9],22:[2,9],23:[2,9],25:[2,9]},{4:20,6:18,7:19,8:4,9:5,11:6,12:7,13:8,14:[1,9],15:[1,10],16:[1,12],19:[1,21],20:[2,8],22:[1,13],23:[1,14],25:[1,15]},{4:20,6:22,7:19,8:4,9:5,11:6,12:7,13:8,14:[1,9],15:[1,10],16:[1,12],19:[1,21],20:[2,8],22:[1,13],23:[1,14],25:[1,15]},{5:[2,13],14:[2,13],15:[2,13],16:[2,13],19:[2,13],20:[2,13],22:[2,13],23:[2,13],25:[2,13]},{5:[2,14],14:[2,14],15:[2,14],16:[2,14],19:[2,14],20:[2,14],22:[2,14],23:[2,14],25:[2,14]},{5:[2,15],14:[2,15],15:[2,15],16:[2,15],19:[2,15],20:[2,15],22:[2,15],23:[2,15],25:[2,15]},{5:[2,16],14:[2,16],15:[2,16],16:[2,16],19:[2,16],20:[2,16],22:[2,16],23:[2,16],25:[2,16]},{17:23,21:24,30:25,40:[1,28],42:[1,27],43:26},{17:29,21:24,30:25,40:[1,28],42:[1,27],43:26},{17:30,21:24,30:25,40:[1,28],42:[1,27],43:26},{17:31,21:24,30:25,40:[1,28],42:[1,27],43:26},{21:33,26:32,32:[1,34],33:[1,35],40:[1,28],43:26},{1:[2,1]},{5:[2,10],14:[2,10],15:[2,10],16:[2,10],19:[2,10],20:[2,10],22:[2,10],23:[2,10],25:[2,10]},{10:36,20:[1,37]},{4:38,8:4,9:5,11:6,12:7,13:8,14:[1,9],15:[1,10],16:[1,12],19:[1,11],20:[2,7],22:[1,13],23:[1,14],25:[1,15]},{7:39,8:17,9:5,11:6,12:7,13:8,14:[1,9],15:[1,10],16:[1,12],19:[1,21],20:[2,6],22:[1,13],23:[1,14],25:[1,15]},{17:23,18:[1,40],21:24,30:25,40:[1,28],42:[1,27],43:26},{10:41,20:[1,37]},{18:[1,42]},{18:[2,43],24:[2,43],28:43,32:[2,43],33:[2,43],34:[2,43],35:[2,43],36:[2,43],40:[2,43],42:[2,43]},{18:[2,25],24:[2,25],36:[2,25]},{18:[2,38],24:[2,38],32:[2,38],33:[2,38],34:[2,38],35:[2,38],36:[2,38],40:[2,38],42:[2,38],44:[1,44]},{21:45,40:[1,28],43:26},{18:[2,40],24:[2,40],32:[2,40],33:[2,40],34:[2,40],35:[2,40],36:[2,40],40:[2,40],42:[2,40],44:[2,40]},{18:[1,46]},{18:[1,47]},{24:[1,48]},{18:[2,41],21:50,27:49,40:[1,28],43:26},{18:[2,34],40:[2,34]},{18:[2,35],40:[2,35]},{18:[2,36],40:[2,36]},{5:[2,11],14:[2,11],15:[2,11],16:[2,11],19:[2,11],20:[2,11],22:[2,11],23:[2,11],25:[2,11]},{21:51,40:[1,28],43:26},{8:17,9:5,11:6,12:7,13:8,14:[1,9],15:[1,10],16:[1,12],19:[1,11],20:[2,3],22:[1,13],23:[1,14],25:[1,15]},{4:52,8:4,9:5,11:6,12:7,13:8,14:[1,9],15:[1,10],16:[1,12],19:[1,11],20:[2,5],22:[1,13],23:[1,14],25:[1,15]},{14:[2,23],15:[2,23],16:[2,23],19:[2,23],20:[2,23],22:[2,23],23:[2,23],25:[2,23]},{5:[2,12],14:[2,12],15:[2,12],16:[2,12],19:[2,12],20:[2,12],22:[2,12],23:[2,12],25:[2,12]},{14:[2,18],15:[2,18],16:[2,18],19:[2,18],20:[2,18],22:[2,18],23:[2,18],25:[2,18]},{18:[2,45],21:56,24:[2,45],29:53,30:60,31:54,32:[1,57],33:[1,58],34:[1,59],35:[1,61],36:[2,45],37:55,38:62,39:63,40:[1,64],42:[1,27],43:26},{40:[1,65]},{18:[2,37],24:[2,37],32:[2,37],33:[2,37],34:[2,37],35:[2,37],36:[2,37],40:[2,37],42:[2,37]},{14:[2,17],15:[2,17],16:[2,17],19:[2,17],20:[2,17],22:[2,17],23:[2,17],25:[2,17]},{5:[2,20],14:[2,20],15:[2,20],16:[2,20],19:[2,20],20:[2,20],22:[2,20],23:[2,20],25:[2,20]},{5:[2,21],14:[2,21],15:[2,21],16:[2,21],19:[2,21],20:[2,21],22:[2,21],23:[2,21],25:[2,21]},{18:[1,66]},{18:[2,42]},{18:[1,67]},{8:17,9:5,11:6,12:7,13:8,14:[1,9],15:[1,10],16:[1,12],19:[1,11],20:[2,4],22:[1,13],23:[1,14],25:[1,15]},{18:[2,24],24:[2,24],36:[2,24]},{18:[2,44],24:[2,44],32:[2,44],33:[2,44],34:[2,44],35:[2,44],36:[2,44],40:[2,44],42:[2,44]},{18:[2,46],24:[2,46],36:[2,46]},{18:[2,26],24:[2,26],32:[2,26],33:[2,26],34:[2,26],35:[2,26],36:[2,26],40:[2,26],42:[2,26]},{18:[2,27],24:[2,27],32:[2,27],33:[2,27],34:[2,27],35:[2,27],36:[2,27],40:[2,27],42:[2,27]},{18:[2,28],24:[2,28],32:[2,28],33:[2,28],34:[2,28],35:[2,28],36:[2,28],40:[2,28],42:[2,28]},{18:[2,29],24:[2,29],32:[2,29],33:[2,29],34:[2,29],35:[2,29],36:[2,29],40:[2,29],42:[2,29]},{18:[2,30],24:[2,30],32:[2,30],33:[2,30],34:[2,30],35:[2,30],36:[2,30],40:[2,30],42:[2,30]},{17:68,21:24,30:25,40:[1,28],42:[1,27],43:26},{18:[2,32],24:[2,32],36:[2,32],39:69,40:[1,70]},{18:[2,47],24:[2,47],36:[2,47],40:[2,47]},{18:[2,40],24:[2,40],32:[2,40],33:[2,40],34:[2,40],35:[2,40],36:[2,40],40:[2,40],41:[1,71],42:[2,40],44:[2,40]},{18:[2,39],24:[2,39],32:[2,39],33:[2,39],34:[2,39],35:[2,39],36:[2,39],40:[2,39],42:[2,39],44:[2,39]},{5:[2,22],14:[2,22],15:[2,22],16:[2,22],19:[2,22],20:[2,22],22:[2,22],23:[2,22],25:[2,22]},{5:[2,19],14:[2,19],15:[2,19],16:[2,19],19:[2,19],20:[2,19],22:[2,19],23:[2,19],25:[2,19]},{36:[1,72]},{18:[2,48],24:[2,48],36:[2,48],40:[2,48]},{41:[1,71]},{21:56,30:60,31:73,32:[1,57],33:[1,58],34:[1,59],35:[1,61],40:[1,28],42:[1,27],43:26},{18:[2,31],24:[2,31],32:[2,31],33:[2,31],34:[2,31],35:[2,31],36:[2,31],40:[2,31],42:[2,31]},{18:[2,33],24:[2,33],36:[2,33],40:[2,33]}],defaultActions:{3:[2,2],16:[2,1],50:[2,42]},parseError:function(t){throw new Error(t)},parse:function(t){function e(){var t;return t=s.lexer.lex()||1,"number"!=typeof t&&(t=s.symbols_[t]||t),t}var s=this,i=[0],n=[null],r=[],o=this.table,a="",h=0,p=0,l=0;this.lexer.setInput(t),this.lexer.yy=this.yy,this.yy.lexer=this.lexer,this.yy.parser=this,"undefined"==typeof this.lexer.yylloc&&(this.lexer.yylloc={});var c=this.lexer.yylloc;r.push(c);var u=this.lexer.options&&this.lexer.options.ranges;"function"==typeof this.yy.parseError&&(this.parseError=this.yy.parseError);for(var f,d,g,m,v,y,S,k,x,b={};;){if(g=i[i.length-1],this.defaultActions[g]?m=this.defaultActions[g]:((null===f||"undefined"==typeof f)&&(f=e()),m=o[g]&&o[g][f]),"undefined"==typeof m||!m.length||!m[0]){var _="";if(!l){x=[];for(y in o[g])this.terminals_[y]&&y>2&&x.push("'"+this.terminals_[y]+"'");_=this.lexer.showPosition?"Parse error on line "+(h+1)+":\n"+this.lexer.showPosition()+"\nExpecting "+x.join(", ")+", got '"+(this.terminals_[f]||f)+"'":"Parse error on line "+(h+1)+": Unexpected "+(1==f?"end of input":"'"+(this.terminals_[f]||f)+"'"),this.parseError(_,{text:this.lexer.match,token:this.terminals_[f]||f,line:this.lexer.yylineno,loc:c,expected:x})}}if(m[0]instanceof Array&&m.length>1)throw new Error("Parse Error: multiple actions possible at state: "+g+", token: "+f);switch(m[0]){case 1:i.push(f),n.push(this.lexer.yytext),r.push(this.lexer.yylloc),i.push(m[1]),f=null,d?(f=d,d=null):(p=this.lexer.yyleng,a=this.lexer.yytext,h=this.lexer.yylineno,c=this.lexer.yylloc,l>0&&l--);break;case 2:if(S=this.productions_[m[1]][1],b.$=n[n.length-S],b._$={first_line:r[r.length-(S||1)].first_line,last_line:r[r.length-1].last_line,first_column:r[r.length-(S||1)].first_column,last_column:r[r.length-1].last_column},u&&(b._$.range=[r[r.length-(S||1)].range[0],r[r.length-1].range[1]]),v=this.performAction.call(b,a,p,h,this.yy,m[1],n,r),"undefined"!=typeof v)return v;S&&(i=i.slice(0,-1*S*2),n=n.slice(0,-1*S),r=r.slice(0,-1*S)),i.push(this.productions_[m[1]][0]),n.push(b.$),r.push(b._$),k=o[i[i.length-2]][i[i.length-1]],i.push(k);break;case 3:return!0}}return!0}},i=function(){var t={EOF:1,parseError:function(t,e){if(!this.yy.parser)throw new Error(t);this.yy.parser.parseError(t,e)},setInput:function(t){return this._input=t,this._more=this._less=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},input:function(){var t=this._input[0];this.yytext+=t,this.yyleng++,this.offset++,this.match+=t,this.matched+=t;var e=t.match(/(?:\r\n?|\n).*/g);return e?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),t},unput:function(t){var e=t.length,s=t.split(/(?:\r\n?|\n)/g);this._input=t+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-e-1),this.offset-=e;var i=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),s.length-1&&(this.yylineno-=s.length-1);var n=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:s?(s.length===i.length?this.yylloc.first_column:0)+i[i.length-s.length].length-s[0].length:this.yylloc.first_column-e},this.options.ranges&&(this.yylloc.range=[n[0],n[0]+this.yyleng-e]),this},more:function(){return this._more=!0,this},less:function(t){this.unput(this.match.slice(t))},pastInput:function(){var t=this.matched.substr(0,this.matched.length-this.match.length);return(t.length>20?"...":"")+t.substr(-20).replace(/\n/g,"")},upcomingInput:function(){var t=this.match;return t.length<20&&(t+=this._input.substr(0,20-t.length)),(t.substr(0,20)+(t.length>20?"...":"")).replace(/\n/g,"")},showPosition:function(){var t=this.pastInput(),e=new Array(t.length+1).join("-");return t+this.upcomingInput()+"\n"+e+"^"},next:function(){if(this.done)return this.EOF;this._input||(this.done=!0);var t,e,s,i,n;this._more||(this.yytext="",this.match="");for(var r=this._currentRules(),o=0;o<r.length&&(s=this._input.match(this.rules[r[o]]),!s||e&&!(s[0].length>e[0].length)||(e=s,i=o,this.options.flex));o++);return e?(n=e[0].match(/(?:\r\n?|\n).*/g),n&&(this.yylineno+=n.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:n?n[n.length-1].length-n[n.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+e[0].length},this.yytext+=e[0],this.match+=e[0],this.matches=e,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._input=this._input.slice(e[0].length),this.matched+=e[0],t=this.performAction.call(this,this.yy,this,r[i],this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),t?t:void 0):""===this._input?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+". Unrecognized text.\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})},lex:function(){var t=this.next();return"undefined"!=typeof t?t:this.lex()},begin:function(t){this.conditionStack.push(t)},popState:function(){return this.conditionStack.pop()},_currentRules:function(){return this.conditions[this.conditionStack[this.conditionStack.length-1]].rules},topState:function(){return this.conditionStack[this.conditionStack.length-2]},pushState:function(t){this.begin(t)}};return t.options={},t.performAction=function(t,e,s,i){function n(t,s){return e.yytext=e.yytext.substr(t,e.yyleng-s)}switch(s){case 0:if("\\\\"===e.yytext.slice(-2)?(n(0,1),this.begin("mu")):"\\"===e.yytext.slice(-1)?(n(0,1),this.begin("emu")):this.begin("mu"),e.yytext)return 14;break;case 1:return 14;case 2:return this.popState(),14;case 3:return n(0,4),this.popState(),15;case 4:return 35;case 5:return 36;case 6:return 25;case 7:return 16;case 8:return 20;case 9:return 19;case 10:return 19;case 11:return 23;case 12:return 22;case 13:this.popState(),this.begin("com");break;case 14:return n(3,5),this.popState(),15;case 15:return 22;case 16:return 41;case 17:return 40;case 18:return 40;case 19:return 44;case 20:break;case 21:return this.popState(),24;case 22:return this.popState(),18;case 23:return e.yytext=n(1,2).replace(/\\"/g,'"'),32;case 24:return e.yytext=n(1,2).replace(/\\'/g,"'"),32;case 25:return 42;case 26:return 34;case 27:return 34;case 28:return 33;case 29:return 40;case 30:return e.yytext=n(1,2),40;case 31:return"INVALID";case 32:return 5}},t.rules=[/^(?:[^\x00]*?(?=(\{\{)))/,/^(?:[^\x00]+)/,/^(?:[^\x00]{2,}?(?=(\{\{|\\\{\{|\\\\\{\{|$)))/,/^(?:[\s\S]*?--\}\})/,/^(?:\()/,/^(?:\))/,/^(?:\{\{(~)?>)/,/^(?:\{\{(~)?#)/,/^(?:\{\{(~)?\/)/,/^(?:\{\{(~)?\^)/,/^(?:\{\{(~)?\s*else\b)/,/^(?:\{\{(~)?\{)/,/^(?:\{\{(~)?&)/,/^(?:\{\{!--)/,/^(?:\{\{![\s\S]*?\}\})/,/^(?:\{\{(~)?)/,/^(?:=)/,/^(?:\.\.)/,/^(?:\.(?=([=~}\s\/.)])))/,/^(?:[\/.])/,/^(?:\s+)/,/^(?:\}(~)?\}\})/,/^(?:(~)?\}\})/,/^(?:"(\\["]|[^"])*")/,/^(?:'(\\[']|[^'])*')/,/^(?:@)/,/^(?:true(?=([~}\s)])))/,/^(?:false(?=([~}\s)])))/,/^(?:-?[0-9]+(?=([~}\s)])))/,/^(?:([^\s!"#%-,\.\/;->@\[-\^`\{-~]+(?=([=~}\s\/.)]))))/,/^(?:\[[^\]]*\])/,/^(?:.)/,/^(?:$)/],t.conditions={mu:{rules:[4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32],inclusive:!1},emu:{rules:[2],inclusive:!1},com:{rules:[3],inclusive:!1},INITIAL:{rules:[0,1,32],inclusive:!0}},t}();return s.lexer=i,e.prototype=s,s.Parser=e,new e}();return t=e}(),h=function(t,e){"use strict";function s(t){return t.constructor===r.ProgramNode?t:(n.yy=r,n.parse(t))}var i={},n=t,r=e;return i.parser=n,i.parse=s,i}(a,o),p=function(t){"use strict";function e(){}function s(t,e,s){if(null==t||"string"!=typeof t&&t.constructor!==s.AST.ProgramNode)throw new r("You must pass a string or Handlebars AST to Handlebars.precompile. You passed "+t);e=e||{},"data"in e||(e.data=!0);var i=s.parse(t),n=(new s.Compiler).compile(i,e);return(new s.JavaScriptCompiler).compile(n,e)}function i(t,e,s){function i(){var i=s.parse(t),n=(new s.Compiler).compile(i,e),r=(new s.JavaScriptCompiler).compile(n,e,void 0,!0);return s.template(r)}if(null==t||"string"!=typeof t&&t.constructor!==s.AST.ProgramNode)throw new r("You must pass a string or Handlebars AST to Handlebars.compile. You passed "+t);e=e||{},"data"in e||(e.data=!0);var n;return function(t,e){return n||(n=i()),n.call(this,t,e)}}var n={},r=t;return n.Compiler=e,e.prototype={compiler:e,disassemble:function(){for(var t,e,s,i=this.opcodes,n=[],r=0,o=i.length;o>r;r++)if(t=i[r],"DECLARE"===t.opcode)n.push("DECLARE "+t.name+"="+t.value);else{e=[];for(var a=0;a<t.args.length;a++)s=t.args[a],"string"==typeof s&&(s='"'+s.replace("\n","\\n")+'"'),e.push(s);n.push(t.opcode+" "+e.join(" "))}return n.join("\n")},equals:function(t){var e=this.opcodes.length;if(t.opcodes.length!==e)return!1;for(var s=0;e>s;s++){var i=this.opcodes[s],n=t.opcodes[s];if(i.opcode!==n.opcode||i.args.length!==n.args.length)return!1;for(var r=0;r<i.args.length;r++)if(i.args[r]!==n.args[r])return!1}if(e=this.children.length,t.children.length!==e)return!1;for(s=0;e>s;s++)if(!this.children[s].equals(t.children[s]))return!1;return!0},guid:0,compile:function(t,e){this.opcodes=[],this.children=[],this.depths={list:[]},this.options=e;var s=this.options.knownHelpers;if(this.options.knownHelpers={helperMissing:!0,blockHelperMissing:!0,each:!0,"if":!0,unless:!0,"with":!0,log:!0},s)for(var i in s)this.options.knownHelpers[i]=s[i];return this.accept(t)},accept:function(t){var e,s=t.strip||{};return s.left&&this.opcode("strip"),e=this[t.type](t),s.right&&this.opcode("strip"),e},program:function(t){for(var e=t.statements,s=0,i=e.length;i>s;s++)this.accept(e[s]);return this.isSimple=1===i,this.depths.list=this.depths.list.sort(function(t,e){return t-e}),this},compileProgram:function(t){var e,s=(new this.compiler).compile(t,this.options),i=this.guid++;this.usePartial=this.usePartial||s.usePartial,this.children[i]=s;for(var n=0,r=s.depths.list.length;r>n;n++)e=s.depths.list[n],2>e||this.addDepth(e-1);return i},block:function(t){var e=t.mustache,s=t.program,i=t.inverse;s&&(s=this.compileProgram(s)),i&&(i=this.compileProgram(i));var n=e.sexpr,r=this.classifySexpr(n);"helper"===r?this.helperSexpr(n,s,i):"simple"===r?(this.simpleSexpr(n),this.opcode("pushProgram",s),this.opcode("pushProgram",i),this.opcode("emptyHash"),this.opcode("blockValue")):(this.ambiguousSexpr(n,s,i),this.opcode("pushProgram",s),this.opcode("pushProgram",i),this.opcode("emptyHash"),this.opcode("ambiguousBlockValue")),this.opcode("append")},hash:function(t){var e,s,i=t.pairs;this.opcode("pushHash");for(var n=0,r=i.length;r>n;n++)e=i[n],s=e[1],this.options.stringParams?(s.depth&&this.addDepth(s.depth),this.opcode("getContext",s.depth||0),this.opcode("pushStringParam",s.stringModeValue,s.type),"sexpr"===s.type&&this.sexpr(s)):this.accept(s),this.opcode("assignToHash",e[0]);this.opcode("popHash")},partial:function(t){var e=t.partialName;this.usePartial=!0,t.context?this.ID(t.context):this.opcode("push","depth0"),this.opcode("invokePartial",e.name),this.opcode("append")},content:function(t){this.opcode("appendContent",t.string)},mustache:function(t){this.sexpr(t.sexpr),this.opcode(t.escaped&&!this.options.noEscape?"appendEscaped":"append")},ambiguousSexpr:function(t,e,s){var i=t.id,n=i.parts[0],r=null!=e||null!=s;this.opcode("getContext",i.depth),this.opcode("pushProgram",e),this.opcode("pushProgram",s),this.opcode("invokeAmbiguous",n,r)},simpleSexpr:function(t){var e=t.id;"DATA"===e.type?this.DATA(e):e.parts.length?this.ID(e):(this.addDepth(e.depth),this.opcode("getContext",e.depth),this.opcode("pushContext")),this.opcode("resolvePossibleLambda")},helperSexpr:function(t,e,s){var i=this.setupFullMustacheParams(t,e,s),n=t.id.parts[0];if(this.options.knownHelpers[n])this.opcode("invokeKnownHelper",i.length,n);else{if(this.options.knownHelpersOnly)throw new r("You specified knownHelpersOnly, but used the unknown helper "+n,t);this.opcode("invokeHelper",i.length,n,t.isRoot)}},sexpr:function(t){var e=this.classifySexpr(t);"simple"===e?this.simpleSexpr(t):"helper"===e?this.helperSexpr(t):this.ambiguousSexpr(t)},ID:function(t){this.addDepth(t.depth),this.opcode("getContext",t.depth);var e=t.parts[0];e?this.opcode("lookupOnContext",t.parts[0]):this.opcode("pushContext");for(var s=1,i=t.parts.length;i>s;s++)this.opcode("lookup",t.parts[s])},DATA:function(t){if(this.options.data=!0,t.id.isScoped||t.id.depth)throw new r("Scoped data references are not supported: "+t.original,t);this.opcode("lookupData");for(var e=t.id.parts,s=0,i=e.length;i>s;s++)this.opcode("lookup",e[s])},STRING:function(t){this.opcode("pushString",t.string)},INTEGER:function(t){this.opcode("pushLiteral",t.integer)},BOOLEAN:function(t){this.opcode("pushLiteral",t.bool)},comment:function(){},opcode:function(t){this.opcodes.push({opcode:t,args:[].slice.call(arguments,1)})},declare:function(t,e){this.opcodes.push({opcode:"DECLARE",name:t,value:e})},addDepth:function(t){0!==t&&(this.depths[t]||(this.depths[t]=!0,this.depths.list.push(t)))},classifySexpr:function(t){var e=t.isHelper,s=t.eligibleHelper,i=this.options;if(s&&!e){var n=t.id.parts[0];i.knownHelpers[n]?e=!0:i.knownHelpersOnly&&(s=!1)}return e?"helper":s?"ambiguous":"simple"},pushParams:function(t){for(var e,s=t.length;s--;)e=t[s],this.options.stringParams?(e.depth&&this.addDepth(e.depth),this.opcode("getContext",e.depth||0),this.opcode("pushStringParam",e.stringModeValue,e.type),"sexpr"===e.type&&this.sexpr(e)):this[e.type](e)},setupFullMustacheParams:function(t,e,s){var i=t.params;return this.pushParams(i),this.opcode("pushProgram",e),this.opcode("pushProgram",s),t.hash?this.hash(t.hash):this.opcode("emptyHash"),i}},n.precompile=s,n.compile=i,n}(s),l=function(t,e){"use strict";function s(t){this.value=t}function i(){}var n,r=t.COMPILER_REVISION,o=t.REVISION_CHANGES,a=t.log,h=e;i.prototype={nameLookup:function(t,e){var s,n;return 0===t.indexOf("depth")&&(s=!0),n=/^[0-9]+$/.test(e)?t+"["+e+"]":i.isValidJavaScriptVariableName(e)?t+"."+e:t+"['"+e+"']",s?"("+t+" && "+n+")":n},compilerInfo:function(){var t=r,e=o[t];return"this.compilerInfo = ["+t+",'"+e+"'];\n"},appendToBuffer:function(t){return this.environment.isSimple?"return "+t+";":{appendToBuffer:!0,content:t,toString:function(){return"buffer += "+t+";"}}},initializeBuffer:function(){return this.quotedString("")},namespace:"Handlebars",compile:function(t,e,s,i){this.environment=t,this.options=e||{},a("debug",this.environment.disassemble()+"\n\n"),this.name=this.environment.name,this.isChild=!!s,this.context=s||{programs:[],environments:[],aliases:{}},this.preamble(),this.stackSlot=0,this.stackVars=[],this.registers={list:[]},this.hashes=[],this.compileStack=[],this.inlineStack=[],this.compileChildren(t,e);
var n,r=t.opcodes;this.i=0;for(var o=r.length;this.i<o;this.i++)n=r[this.i],"DECLARE"===n.opcode?this[n.name]=n.value:this[n.opcode].apply(this,n.args),n.opcode!==this.stripNext&&(this.stripNext=!1);if(this.pushSource(""),this.stackSlot||this.inlineStack.length||this.compileStack.length)throw new h("Compile completed with content left on stack");return this.createFunctionContext(i)},preamble:function(){var t=[];if(this.isChild)t.push("");else{var e=this.namespace,s="helpers = this.merge(helpers, "+e+".helpers);";this.environment.usePartial&&(s=s+" partials = this.merge(partials, "+e+".partials);"),this.options.data&&(s+=" data = data || {};"),t.push(s)}t.push(this.environment.isSimple?"":", buffer = "+this.initializeBuffer()),this.lastContext=0,this.source=t},createFunctionContext:function(t){var e=this.stackVars.concat(this.registers.list);if(e.length>0&&(this.source[1]=this.source[1]+", "+e.join(", ")),!this.isChild)for(var s in this.context.aliases)this.context.aliases.hasOwnProperty(s)&&(this.source[1]=this.source[1]+", "+s+"="+this.context.aliases[s]);this.source[1]&&(this.source[1]="var "+this.source[1].substring(2)+";"),this.isChild||(this.source[1]+="\n"+this.context.programs.join("\n")+"\n"),this.environment.isSimple||this.pushSource("return buffer;");for(var i=this.isChild?["depth0","data"]:["Handlebars","depth0","helpers","partials","data"],n=0,r=this.environment.depths.list.length;r>n;n++)i.push("depth"+this.environment.depths.list[n]);var o=this.mergeSource();if(this.isChild||(o=this.compilerInfo()+o),t)return i.push(o),Function.apply(this,i);var h="function "+(this.name||"")+"("+i.join(",")+") {\n  "+o+"}";return a("debug",h+"\n\n"),h},mergeSource:function(){for(var t,e="",s=0,i=this.source.length;i>s;s++){var n=this.source[s];n.appendToBuffer?t=t?t+"\n    + "+n.content:n.content:(t&&(e+="buffer += "+t+";\n  ",t=void 0),e+=n+"\n  ")}return e},blockValue:function(){this.context.aliases.blockHelperMissing="helpers.blockHelperMissing";var t=["depth0"];this.setupParams(0,t),this.replaceStack(function(e){return t.splice(1,0,e),"blockHelperMissing.call("+t.join(", ")+")"})},ambiguousBlockValue:function(){this.context.aliases.blockHelperMissing="helpers.blockHelperMissing";var t=["depth0"];this.setupParams(0,t);var e=this.topStack();t.splice(1,0,e),this.pushSource("if (!"+this.lastHelper+") { "+e+" = blockHelperMissing.call("+t.join(", ")+"); }")},appendContent:function(t){this.pendingContent&&(t=this.pendingContent+t),this.stripNext&&(t=t.replace(/^\s+/,"")),this.pendingContent=t},strip:function(){this.pendingContent&&(this.pendingContent=this.pendingContent.replace(/\s+$/,"")),this.stripNext="strip"},append:function(){this.flushInline();var t=this.popStack();this.pushSource("if("+t+" || "+t+" === 0) { "+this.appendToBuffer(t)+" }"),this.environment.isSimple&&this.pushSource("else { "+this.appendToBuffer("''")+" }")},appendEscaped:function(){this.context.aliases.escapeExpression="this.escapeExpression",this.pushSource(this.appendToBuffer("escapeExpression("+this.popStack()+")"))},getContext:function(t){this.lastContext!==t&&(this.lastContext=t)},lookupOnContext:function(t){this.push(this.nameLookup("depth"+this.lastContext,t,"context"))},pushContext:function(){this.pushStackLiteral("depth"+this.lastContext)},resolvePossibleLambda:function(){this.context.aliases.functionType='"function"',this.replaceStack(function(t){return"typeof "+t+" === functionType ? "+t+".apply(depth0) : "+t})},lookup:function(t){this.replaceStack(function(e){return e+" == null || "+e+" === false ? "+e+" : "+this.nameLookup(e,t,"context")})},lookupData:function(){this.pushStackLiteral("data")},pushStringParam:function(t,e){this.pushStackLiteral("depth"+this.lastContext),this.pushString(e),"sexpr"!==e&&("string"==typeof t?this.pushString(t):this.pushStackLiteral(t))},emptyHash:function(){this.pushStackLiteral("{}"),this.options.stringParams&&(this.push("{}"),this.push("{}"))},pushHash:function(){this.hash&&this.hashes.push(this.hash),this.hash={values:[],types:[],contexts:[]}},popHash:function(){var t=this.hash;this.hash=this.hashes.pop(),this.options.stringParams&&(this.push("{"+t.contexts.join(",")+"}"),this.push("{"+t.types.join(",")+"}")),this.push("{\n    "+t.values.join(",\n    ")+"\n  }")},pushString:function(t){this.pushStackLiteral(this.quotedString(t))},push:function(t){return this.inlineStack.push(t),t},pushLiteral:function(t){this.pushStackLiteral(t)},pushProgram:function(t){this.pushStackLiteral(null!=t?this.programExpression(t):null)},invokeHelper:function(t,e,s){this.context.aliases.helperMissing="helpers.helperMissing",this.useRegister("helper");var i=this.lastHelper=this.setupHelper(t,e,!0),n=this.nameLookup("depth"+this.lastContext,e,"context"),r="helper = "+i.name+" || "+n;i.paramsInit&&(r+=","+i.paramsInit),this.push("("+r+",helper ? helper.call("+i.callParams+") : helperMissing.call("+i.helperMissingParams+"))"),s||this.flushInline()},invokeKnownHelper:function(t,e){var s=this.setupHelper(t,e);this.push(s.name+".call("+s.callParams+")")},invokeAmbiguous:function(t,e){this.context.aliases.functionType='"function"',this.useRegister("helper"),this.emptyHash();var s=this.setupHelper(0,t,e),i=this.lastHelper=this.nameLookup("helpers",t,"helper"),n=this.nameLookup("depth"+this.lastContext,t,"context"),r=this.nextStack();s.paramsInit&&this.pushSource(s.paramsInit),this.pushSource("if (helper = "+i+") { "+r+" = helper.call("+s.callParams+"); }"),this.pushSource("else { helper = "+n+"; "+r+" = typeof helper === functionType ? helper.call("+s.callParams+") : helper; }")},invokePartial:function(t){var e=[this.nameLookup("partials",t,"partial"),"'"+t+"'",this.popStack(),"helpers","partials"];this.options.data&&e.push("data"),this.context.aliases.self="this",this.push("self.invokePartial("+e.join(", ")+")")},assignToHash:function(t){var e,s,i=this.popStack();this.options.stringParams&&(s=this.popStack(),e=this.popStack());var n=this.hash;e&&n.contexts.push("'"+t+"': "+e),s&&n.types.push("'"+t+"': "+s),n.values.push("'"+t+"': ("+i+")")},compiler:i,compileChildren:function(t,e){for(var s,i,n=t.children,r=0,o=n.length;o>r;r++){s=n[r],i=new this.compiler;var a=this.matchExistingProgram(s);null==a?(this.context.programs.push(""),a=this.context.programs.length,s.index=a,s.name="program"+a,this.context.programs[a]=i.compile(s,e,this.context),this.context.environments[a]=s):(s.index=a,s.name="program"+a)}},matchExistingProgram:function(t){for(var e=0,s=this.context.environments.length;s>e;e++){var i=this.context.environments[e];if(i&&i.equals(t))return e}},programExpression:function(t){if(this.context.aliases.self="this",null==t)return"self.noop";for(var e,s=this.environment.children[t],i=s.depths.list,n=[s.index,s.name,"data"],r=0,o=i.length;o>r;r++)e=i[r],n.push(1===e?"depth0":"depth"+(e-1));return(0===i.length?"self.program(":"self.programWithDepth(")+n.join(", ")+")"},register:function(t,e){this.useRegister(t),this.pushSource(t+" = "+e+";")},useRegister:function(t){this.registers[t]||(this.registers[t]=!0,this.registers.list.push(t))},pushStackLiteral:function(t){return this.push(new s(t))},pushSource:function(t){this.pendingContent&&(this.source.push(this.appendToBuffer(this.quotedString(this.pendingContent))),this.pendingContent=void 0),t&&this.source.push(t)},pushStack:function(t){this.flushInline();var e=this.incrStack();return t&&this.pushSource(e+" = "+t+";"),this.compileStack.push(e),e},replaceStack:function(t){var e,i,n,r="",o=this.isInline();if(o){var a=this.popStack(!0);if(a instanceof s)e=a.value,n=!0;else{i=!this.stackSlot;var h=i?this.incrStack():this.topStackName();r="("+this.push(h)+" = "+a+"),",e=this.topStack()}}else e=this.topStack();var p=t.call(this,e);return o?(n||this.popStack(),i&&this.stackSlot--,this.push("("+r+p+")")):(/^stack/.test(e)||(e=this.nextStack()),this.pushSource(e+" = ("+r+p+");")),e},nextStack:function(){return this.pushStack()},incrStack:function(){return this.stackSlot++,this.stackSlot>this.stackVars.length&&this.stackVars.push("stack"+this.stackSlot),this.topStackName()},topStackName:function(){return"stack"+this.stackSlot},flushInline:function(){var t=this.inlineStack;if(t.length){this.inlineStack=[];for(var e=0,i=t.length;i>e;e++){var n=t[e];n instanceof s?this.compileStack.push(n):this.pushStack(n)}}},isInline:function(){return this.inlineStack.length},popStack:function(t){var e=this.isInline(),i=(e?this.inlineStack:this.compileStack).pop();if(!t&&i instanceof s)return i.value;if(!e){if(!this.stackSlot)throw new h("Invalid stack pop");this.stackSlot--}return i},topStack:function(t){var e=this.isInline()?this.inlineStack:this.compileStack,i=e[e.length-1];return!t&&i instanceof s?i.value:i},quotedString:function(t){return'"'+t.replace(/\\/g,"\\\\").replace(/"/g,'\\"').replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029")+'"'},setupHelper:function(t,e,s){var i=[],n=this.setupParams(t,i,s),r=this.nameLookup("helpers",e,"helper");return{params:i,paramsInit:n,name:r,callParams:["depth0"].concat(i).join(", "),helperMissingParams:s&&["depth0",this.quotedString(e)].concat(i).join(", ")}},setupOptions:function(t,e){var s,i,n,r=[],o=[],a=[];r.push("hash:"+this.popStack()),this.options.stringParams&&(r.push("hashTypes:"+this.popStack()),r.push("hashContexts:"+this.popStack())),i=this.popStack(),n=this.popStack(),(n||i)&&(n||(this.context.aliases.self="this",n="self.noop"),i||(this.context.aliases.self="this",i="self.noop"),r.push("inverse:"+i),r.push("fn:"+n));for(var h=0;t>h;h++)s=this.popStack(),e.push(s),this.options.stringParams&&(a.push(this.popStack()),o.push(this.popStack()));return this.options.stringParams&&(r.push("contexts:["+o.join(",")+"]"),r.push("types:["+a.join(",")+"]")),this.options.data&&r.push("data:data"),r},setupParams:function(t,e,s){var i="{"+this.setupOptions(t,e).join(",")+"}";return s?(this.useRegister("options"),e.push("options"),"options="+i):(e.push(i),"")}};for(var p="break else new var case finally return void catch for switch while continue function this with default if throw delete in try do instanceof typeof abstract enum int short boolean export interface static byte extends long super char final native synchronized class float package throws const goto private transient debugger implements protected volatile double import public let yield".split(" "),l=i.RESERVED_WORDS={},c=0,u=p.length;u>c;c++)l[p[c]]=!0;return i.isValidJavaScriptVariableName=function(t){return!i.RESERVED_WORDS[t]&&/^[a-zA-Z_$][0-9a-zA-Z_$]*$/.test(t)?!0:!1},n=i}(i,s),c=function(t,e,s,i,n){"use strict";var r,o=t,a=e,h=s.parser,p=s.parse,l=i.Compiler,c=i.compile,u=i.precompile,f=n,d=o.create,g=function(){var t=d();return t.compile=function(e,s){return c(e,s,t)},t.precompile=function(e,s){return u(e,s,t)},t.AST=a,t.Compiler=l,t.JavaScriptCompiler=f,t.Parser=h,t.parse=p,t};return o=g(),o.create=g,r=o}(r,o,h,p,l);return c}();