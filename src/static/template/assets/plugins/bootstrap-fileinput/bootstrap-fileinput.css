/*!
 * J<PERSON>ny extension p7 to Bootstrap v3.0.0
 *
 * Copyright 2013 Twitter, Inc
 * Licensed under the Apache License v2.0
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Designed and built with all the love in the world by @mdo and @fat.
 */
 
.btn-file {
  position: relative;
  overflow: hidden;
  vertical-align: middle;
}

.btn-file > input {
  position: absolute;
  top: 0;
  right: 0;
  margin: 0;
  font-size: 23px;
  cursor: pointer;
  opacity: 0;
  filter: alpha(opacity=0);
  transform: translate(-300px, 0) scale(4);
  direction: ltr;
}

.fileinput {
  display: inline-block;
  margin-bottom: 9px;
}

.fileinput .uneditable-input {
  display: inline-block;
  margin-bottom: 0;
  vertical-align: middle;
  cursor: text;
}

.fileinput .thumbnail {
  display: inline-block;
  margin-bottom: 5px;
  overflow: hidden;
  text-align: center;
  vertical-align: middle;
}

.fileinput .thumbnail > img {
  max-height: 100%;
}

.fileinput .btn {
  vertical-align: middle;
}

.fileinput-exists .fileinput-new,
.fileinput-new .fileinput-exists {
  display: none;
}

.fileinput-inline .fileinput-controls {
  display: inline;
}

.fileinput .uneditable-input {
  white-space: normal;
}

.fileinput-new .input-group .btn-file {
  border-radius: 0 4px 4px 0;
}

.fileinput-new .input-group .btn-file.btn-xs,
.fileinput-new .input-group .btn-file.btn-sm {
  border-radius: 0 3px 3px 0;
}

.fileinput-new .input-group .btn-file.btn-lg {
  border-radius: 0 6px 6px 0;
}

.form-group.has-warning .fileinput .uneditable-input {
  color: #c09853;
  border-color: #faebcc;
}

.form-group.has-warning .fileinput .fileinput-preview {
  color: #c09853;
}

.form-group.has-warning .fileinput .thumbnail {
  border-color: #faebcc;
}

.form-group.has-error .fileinput .uneditable-input {
  color: #b94a48;
  border-color: #ebccd1;
}

.form-group.has-error .fileinput .fileinput-preview {
  color: #b94a48;
}

.form-group.has-error .fileinput .thumbnail {
  border-color: #ebccd1;
}

.form-group.has-success .fileinput .uneditable-input {
  color: #468847;
  border-color: #d6e9c6;
}

.form-group.has-success .fileinput .fileinput-preview {
  color: #468847;
}

.form-group.has-success .fileinput .thumbnail {
  border-color: #d6e9c6;
}

.input-group-addon:not(:first-child) {
  border-left: 0;
}