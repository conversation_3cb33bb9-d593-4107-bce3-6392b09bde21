{"emptyTable": "Não foi encontrado nenhum registo", "loadingRecords": "A carregar...", "processing": "A processar...", "lengthMenu": "Mostrar _MENU_ registos", "zeroRecords": "Não foram encontrados resultados", "search": "Procurar:", "paginate": {"first": "<PERSON><PERSON>", "previous": "Anterior", "next": "Se<PERSON><PERSON>", "last": "Último"}, "aria": {"sortAscending": ": Ordenar colunas de forma ascendente", "sortDescending": ": Ordenar colunas de forma descendente"}, "autoFill": {"cancel": "cancelar", "fill": "preencher", "fillHorizontal": "preencher c<PERSON><PERSON>las na <PERSON>", "fillVertical": "preencher células na vertical"}, "buttons": {"collection": "Coleção", "colvis": "Visibilidade de colunas", "colvisRestore": "Restaurar visibilidade", "copy": "Copiar", "copySuccess": {"1": "Uma linha copiada para a área de transferência", "_": "%ds linhas copiadas para a área de transferência"}, "copyTitle": "Copiar para a área de transferência", "csv": "CSV", "excel": "Excel", "pageLength": {"-1": "<PERSON><PERSON> todas as lin<PERSON>", "_": "Mostrar %d linhas"}, "pdf": "PDF", "print": "Imprimir", "copyKeys": "Pressionar CTRL ou u2318 + C para copiar a informação para a área de transferência. Para cancelar, clique nesta mensagem ou pressione ESC.", "createState": "<PERSON><PERSON><PERSON>", "removeAllStates": "Remover Todos", "removeState": "Remover", "renameState": "Renomear", "savedStates": "Gravados", "stateRestore": "Estado %d", "updateState": "<PERSON><PERSON><PERSON><PERSON>"}, "decimal": ",", "infoFiltered": "(filtrado num total de _MAX_ registos)", "infoThousands": ".", "searchBuilder": {"add": "Adicionar condi<PERSON>", "button": {"0": "<PERSON><PERSON><PERSON><PERSON> de pesquisa", "_": "Construtor de pesquisa (%d)"}, "clearAll": "<PERSON><PERSON> tudo", "condition": "Condição", "conditions": {"date": {"after": "<PERSON><PERSON><PERSON>", "before": "<PERSON><PERSON>", "between": "<PERSON><PERSON>", "empty": "<PERSON><PERSON><PERSON>", "equals": "Igual", "not": "Di<PERSON><PERSON>", "notBetween": "Não está entre", "notEmpty": "Não está vazio"}, "number": {"between": "<PERSON><PERSON>", "empty": "<PERSON><PERSON><PERSON>", "equals": "Igual", "gt": "<PERSON><PERSON> que", "gte": "<PERSON><PERSON> ou igual a", "lt": "<PERSON><PERSON> que", "lte": "<PERSON>or ou igual a", "not": "Di<PERSON><PERSON>", "notBetween": "Não está entre", "notEmpty": "Não está vazio"}, "string": {"contains": "<PERSON><PERSON><PERSON>", "empty": "<PERSON><PERSON><PERSON>", "endsWith": "Termina em", "equals": "Igual", "not": "Di<PERSON><PERSON>", "notEmpty": "Não está vazio", "startsWith": "Começa em", "notContains": "Não contém", "notStartsWith": "Não começa com", "notEndsWith": "Não termina com"}, "array": {"equals": "Igual", "empty": "<PERSON><PERSON><PERSON>", "contains": "<PERSON><PERSON><PERSON>", "not": "Di<PERSON><PERSON>", "notEmpty": "Não está vazio", "without": "Se<PERSON>"}}, "data": "<PERSON><PERSON>", "deleteTitle": "Excluir condição de filtragem", "logicAnd": "E", "logicOr": "Ou", "title": {"0": "<PERSON><PERSON><PERSON><PERSON> de pesquisa", "_": "Construtor de pesquisa (%d)"}, "value": "Valor", "leftTitle": "Excluir critério", "rightTitle": "Incluir critério"}, "searchPanes": {"clearMessage": "<PERSON><PERSON> tudo", "collapse": {"0": "Pain<PERSON>is de pesquisa", "_": "Painéis de pesquisa (%d)"}, "count": "{total}", "countFiltered": "{shown} ({total})", "emptyPanes": "Sem painéis de pesquisa", "loadMessage": "A carregar painéis de pesquisa", "title": "Filtros ativos", "showMessage": "<PERSON><PERSON> todos", "collapseMessage": "<PERSON><PERSON><PERSON><PERSON>"}, "select": {"cells": {"1": "1 célula seleccionada", "_": "%d células seleccionadas"}, "columns": {"1": "1 coluna seleccionada", "_": "%d colunas seleccionadas"}, "rows": {"1": "%d linha seleccionada", "_": "%d linhas sele<PERSON>"}}, "thousands": ".", "editor": {"close": "<PERSON><PERSON><PERSON>", "create": {"button": "Novo", "title": "Criar novo registro", "submit": "<PERSON><PERSON><PERSON>"}, "edit": {"button": "<PERSON><PERSON>", "title": "Editar registro", "submit": "<PERSON><PERSON><PERSON><PERSON>"}, "remove": {"button": "Remover", "title": "Remover", "submit": "Remover", "confirm": {"_": "Tem certeza que quer apagar %d entradas?", "1": "Tem certeza que quer apagar esta entrada?"}}, "multi": {"title": "Multiplos valores", "restore": "Desfazer alterações", "info": "Os itens selecionados contêm valores diferentes para esta entrada. Para editar e definir todos os itens nesta entrada com o mesmo valor, clique ou toque aqui, caso contr<PERSON><PERSON>, el<PERSON> man<PERSON>ão os seus valores individuais.", "noMulti": "Este campo pode ser editado individualmente mas não pode ser editado em grupo"}, "error": {"system": "Ocorreu um erro no sistema"}}, "info": "Mostrando os registos _START_ a _END_ num total de _TOTAL_", "infoEmpty": "Mostrando 0 os registos num total de 0", "datetime": {"previous": "anterior", "next": "próximo", "hours": "horas", "minutes": "minutos", "seconds": "segundos", "unknown": "desconhecido", "amPm": ["am", "pm"], "weekdays": ["Seg", "<PERSON><PERSON>", "<PERSON>ua", "<PERSON>ui", "Sex", "<PERSON><PERSON><PERSON>", "Dom"], "months": ["Janeiro", "<PERSON><PERSON>", "Março", "Abril", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Agosto", "Setembro", "Out<PERSON>ro", "Novembro", "Dezembro"]}, "stateRestore": {"creationModal": {"button": "<PERSON><PERSON><PERSON>", "columns": {"search": "Pesquisa por Colunas", "visible": "Visibilidade das Colunas"}, "name": "Nome:", "order": "Ordenar", "paging": "Paginação", "scroller": "Posição da barra de Scroll", "search": "Pesquisa", "searchBuilder": "Pesquisa Avançada", "select": "Selecionar", "title": "Criar <PERSON>", "toggleLabel": "Incluir:"}, "duplicateError": "Já existe um estado com o mesmo nome", "emptyError": "Não pode estar a vazio", "emptyStates": "Não existem estados gravados", "removeConfirm": "Deseja mesmo remover o estado %s?", "removeError": "Erro ao remover o estado.", "removeJoiner": " e ", "removeSubmit": "<PERSON><PERSON><PERSON>", "removeTitle": "<PERSON><PERSON><PERSON>", "renameButton": "Renomear", "renameLabel": "Novo nome para %s:", "renameTitle": "Renomear <PERSON>"}}