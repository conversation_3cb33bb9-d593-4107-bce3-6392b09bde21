/*-------------------------------------------------------------------
Reset stylesheet to reduce browser inconsistencies
http://meyerweb.com/eric/tools/css/reset/ 
v2.0 | 20110126
License: none (public domain)
-------------------------------------------------------------------*/
html, body, div, span, applet, object, iframe,
h1, h2, h3, h4, h5, h6, p, blockquote, pre,
a, abbr, acronym, address, big, cite, code,
del, dfn, em, img, ins, kbd, q, s, samp,
small, strike, strong, sub, sup, tt, var,
b, u, i, center,
dl, dt, dd, ol, ul, li,
fieldset, form, label, legend,
table, caption, tbody, tfoot, thead, tr, th, td,
article, aside, canvas, details, embed, 
figure, figcaption, footer, header, hgroup, 
menu, nav, output, ruby, section, summary,
time, mark, audio, video {
	margin: 0;
	padding: 0;
	border: 0;
	font-size: 100%;
	font: inherit;
	vertical-align: baseline;
}
/* HTML5 display-role reset for older browsers */
article, aside, details, figcaption, figure, 
footer, header, hgroup, menu, nav, section {
	display: block;
}
ol, ul {
	list-style: none;
}



blockquote, q {
	quotes: none;
}
blockquote:before, blockquote:after,
q:before, q:after {
	content: '';
	content: none;
}
table {
	border-collapse: collapse;
	border-spacing: 0;
}

/*-----------------------------------------------------------------*/

/*! fancyBox v2.0.4 fancyapps.com | fancyapps.com/fancybox/#license */
.fancybox-tmp iframe, .fancybox-tmp object {
	vertical-align: top;
	padding: 0;
	margin: 0;
}

.fancybox-wrap {
	position: absolute;
	top: 0;
	left: 0;
	z-index: 1002;
}

.fancybox-outer {
	padding: 0;
	margin: 0;
	background: #f9f9f9;
	color: #444;
	text-shadow: none;
	-webkit-border-radius: 4px;
	-moz-border-radius: 4px;
	border-radius: 4px;
}

.fancybox-opened {
	z-index: 1003;	
}

.fancybox-opened .fancybox-outer {
	-webkit-box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
	   -moz-box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
	        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
}

.fancybox-inner {
	width: 100%;
	height: 100%;
	padding: 0;
	margin: 0;
	position: relative;
	outline: none;
	overflow: hidden;
}

.fancybox-error {
	color: #444;
    font: 14px/20px "Helvetica Neue",Helvetica,Arial,sans-serif;
    margin: 0;
    padding: 10px;
}

.fancybox-image, .fancybox-iframe {
	display: block;
	width: 100%;
	height: 100%;
	border: 0;
	padding: 0;
	margin: 0;
	vertical-align: top;
}

.fancybox-image {
	max-width: 100%;
	max-height: 100%;
}

#fancybox-loading {
	position: fixed;
	top: 50%;
	left: 50%;
	margin-top: -21px;
	margin-left: -21px;
	width: 42px;
	height: 42px;
	background: url('/static_1/lib/fancybox/source/fancybox_loading.gif');
	opacity: 0.8;
	cursor: pointer;
	z-index: 1010;
}

.fancybox-close, .fancybox-prev span, .fancybox-next span {
	background-image: url('/static_1/lib/fancybox/source/fancybox_sprite.png');
}

.fancybox-close {
	position: absolute;
	top: -18px;
	right: -18px;
	width: 36px;
	height: 36px;
	cursor: pointer;
	z-index: 1004;
}

.fancybox-prev, .fancybox-next {
	position: absolute;
	top: 0;
	width: 40%;
	height: 100%;
	cursor: pointer;
	background: transparent url('/static_1/lib/fancybox/source/blank.gif'); /* helps IE */
	z-index: 1003;
}

.fancybox-prev {
	left: 0;	
}

.fancybox-next {
	right: 0;
}

.fancybox-prev span, .fancybox-next span {
	position: absolute;
	top: 50%;
	left: -9999px;
	width: 36px;
	height: 36px;
	margin-top: -18px;
	cursor: pointer;
	z-index: 1003;
}

.fancybox-prev span {
	background-position: 0 -36px;
}

.fancybox-next span {
	background-position: 0 -72px;
}

.fancybox-prev:hover, .fancybox-next:hover {
	visibility: visible;
}

.fancybox-prev:hover span {
	left: 20px;
}

.fancybox-next:hover span {
	left: auto;
	right: 20px;
}

.fancybox-tmp {
	position: absolute;
	top: -9999px;
	left: -9999px;
	padding: 0;
	overflow: visible;
	visibility: hidden;
}

/* Overlay helper */

#fancybox-overlay {
	position: absolute;
	top: 0;
	left: 0;
	overflow: hidden;
	display: none;
	z-index: 1001;
	background: #000;
}

/* Title helper */

.fancybox-title {
	visibility: hidden;	
	font: normal 13px/20px "Helvetica Neue",Helvetica,Arial,sans-serif;
	position: relative;
	text-shadow: none;
	z-index: 1005;
}

.fancybox-opened .fancybox-title {
	visibility: visible;
}

.fancybox-title-float-wrap {
	position: absolute;
	bottom: 0;
	right: 50%;
	margin-bottom: -35px;
	z-index: 1003;
	text-align: center;
}

.fancybox-title-float-wrap .child {
	display: inline-block;
	margin-right: -100%;
	padding: 2px 20px;
	background: transparent; /* Fallback for web browsers that doesn't support RGBa */
	background: rgba(0, 0, 0, 0.8);
	-webkit-border-radius: 15px;
	   -moz-border-radius: 15px;
			border-radius: 15px;
	text-shadow: 0 1px 2px #222;
	color: #FFF;
	font-weight: bold;
	line-height: 24px;
	white-space: nowrap;
}

.fancybox-title-outside-wrap {
	position: relative;
	margin-top: 10px;
	color: #fff;
}

.fancybox-title-inside-wrap {
	margin-top: 10px;
}

.fancybox-title-over-wrap {
	position: absolute;
	bottom: 0;
	left: 0;	
	color: #fff;
	padding: 10px;
	background: #000;
	background: rgba(0, 0, 0, .8);
}


#fancybox-buttons {
	position: fixed;
	left: 0;
	width: 100%;
	z-index: 1005;
}

#fancybox-buttons.top {
	top: 10px;
}

#fancybox-buttons.bottom {
	bottom: 10px;
}

#fancybox-buttons ul {
	display: block;
	width: 170px;
	height: 30px;
	margin: 0 auto;
	padding: 0;
	list-style: none;
	background: #111;
	-webkit-box-shadow: 0 1px 3px #000,0 0 0 1px rgba(0,0,0,.7),inset 0 0 0 1px rgba(255,255,255,.05);
	-moz-box-shadow: 0 1px 3px #000,0 0 0 1px rgba(0,0,0,.7),inset 0 0 0 1px rgba(255,255,255,.05);
	background: #111 -webkit-gradient(linear,0% 0%,0% 100%,from(rgba(255,255,255,.2)),color-stop(.5,rgba(255,255,255,.15)),color-stop(.5,rgba(255,255,255,.1)),to(rgba(255,255,255,.15)));
	background: #111 -moz-linear-gradient(top,rgba(255,255,255,.2) 0%,rgba(255,255,255,.15) 50%,rgba(255,255,255,.1) 50%,rgba(255,255,255,.15) 100%);
	border-radius: 3px;
}

#fancybox-buttons ul li {
	float: left;
	margin: 0;
	padding: 0;
}

#fancybox-buttons a {
	display: block;
	width: 30px;
	height: 30px;
	text-indent: -9999px;
	background-image: url('fancybox_buttons.png');
	background-repeat: no-repeat;
	outline: none;
}

#fancybox-buttons a.btnPrev {
	width: 32px;
	background-position: 6px 0;
}

#fancybox-buttons a.btnNext {
	background-position: -33px 0;
	border-right: 1px solid #3e3e3e;
}

#fancybox-buttons a.btnPlay {
	background-position: 0 -30px;
}

#fancybox-buttons a.btnPlayOn {
	background-position: -30px -30px;
}

#fancybox-buttons a.btnToggle {
	background-position: 3px -60px;
	border-left: 1px solid #111;
	border-right: 1px solid #3e3e3e;
	width: 35px
}

#fancybox-buttons a.btnToggleOn {
	background-position: -27px -60px;
}

#fancybox-buttons a.btnClose {
	border-left: 1px solid #111;
	width: 38px;
	background-position: -57px 0px;
}

#fancybox-buttons a.btnDisabled {
	opacity : 0.5;
	cursor: default;
}

#fancybox-thumbs {
	position: fixed;
	left: 0px;
	width: 100%;
	overflow: hidden;
	z-index: 1005;
}

#fancybox-thumbs.bottom {
	bottom: 2px;
}

#fancybox-thumbs.top {
	top: 2px;
}

#fancybox-thumbs ul {
	position: relative;
	list-style: none;
	margin: 0;
	padding: 0;
}

#fancybox-thumbs ul li {
	float: left;
	padding: 1px;
	opacity: 0.5;
}

#fancybox-thumbs ul li.active {
	opacity: 0.75;
	padding: 0;
	border: 1px solid #fff;
}

#fancybox-thumbs ul li:hover {
	opacity: 1;
}

#fancybox-thumbs ul li a {
	display: block;
	position: relative;
	overflow: hidden;
	border: 1px solid #222;
	background: #111;
	outline: none;
}

#fancybox-thumbs ul li img {
	display: block;
	position: relative;
	border: 0;
	padding: 0;
}

#pikame {
	display:none;
}
.pika-textnav {
	display: none;
}
.pika-counter {
	display:none;
}
.pika-stage img {
	width: 683px;
	height: 358px;

}

/*social icons*/
/*================================================================================*/
/*========DO NOT EDIT BELOW HERE UNLESS YOU KNOW WHAT YOU ARE DOING===============*/
/*================================================================================*/

/*========================================*/
/*========COMMON STYLING==================*/
/*========================================*/
.social ul, .social_small ul {list-style:none;margin:0 auto;padding:0;}
.social ul.inlined, .social_small ul.inlined {float:left;}
.social ul li{width:32px;height:32px;margin: 5px; text-indent:-9999px;}
.social ul li a, .social_small ul li a { 
	display:block;
	width:100%;
	height:100%;
	opacity: 1;
	-moz-opacity: 1;
	filter:alpha(opacity=100);
}
.social ul li a:hover, .social_small ul li a:hover{
/* 	opacity: 1;-moz-opacity: 1;filter:alpha(opacity=1); */
	}

.social_small ul li{width:16px;height:16px;margin: 5px; text-indent:-9999px;}

/*========================================*/
/*========SOCIAL LARGE SIZE===============*/
/*========================================*/
a.twitter, a.facebook, a.flickr, a.friendfeed, a.delicious, a.digg, a.lastfm, a.youtube, a.feed, a.linked-in{
	background:url(/static_1/images/social_icons.png);
}

.social ul a.twitter{background-position: 0px 0px;}
.social ul a.facebook{background-position: 0px -42px;}
.social ul a.flickr{background-position: 0px -84px;}
.social ul a.friendfeed{background-position: 0px -126px;}
.social ul a.delicious{background-position: 0px -168px;}
.social ul a.digg{background-position: 0px -210px;}
.social ul a.lastfm{background-position: 0px -252px;}
.social ul a.linked-in{background-position: 0px -294px;}
.social ul a.youtube{background-position: 0px -336px;}
.social ul a.feed{background-position: 0px -378px;}

/*========================================*/
/*========SOCIAL SMALL SIZE===============*/
/*========================================*/
.social_small ul a.twitter, .social_small ul a.facebook, .social_small ul a.flickr, .social_small ul a.friendfeed, .social_small ul a.delicious, .social_small ul a.digg, .social_small ul a.lastfm, .social_small ul a.youtube, .social_small ul a.feed, .social_small ul a.linked-in{
	background:url(/static_1/images/social_icons_small.png);
}

.social_small ul a.twitter{background-position: 0px 0px;}
.social_small ul a.facebook{background-position: 0px -21px;}
.social_small ul a.flickr{background-position: 0px -42px;}
.social_small ul a.friendfeed{background-position: 0px -78px;}
.social_small ul a.delicious{background-position: 0px -104px;}
.social_small ul a.digg{background-position: 0px -130px;}
.social_small ul a.lastfm{background-position: 0px -156px;}
.social_small ul a.linked-in{background-position: 0px -182px;}
.social_small ul a.youtube{background-position: 0px -168px;}
.social_small ul a.feed{background-position: 0px -234px;}