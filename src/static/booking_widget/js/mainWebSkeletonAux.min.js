(function(c){var a=document.createElement("div").style,h=a.MozBorderRadius!==undefined,j=a.WebkitBorderRadius!==undefined,e=a.borderRadius!==undefined||a.BorderRadius!==undefined,d=document.documentMode||0,l=c.browser.msie&&((c.browser.version<8&&!d)||d<8),i=c.browser.msie&&(function(){var n=document.createElement("div");try{n.style.setExpression("width","0+0");n.style.removeExpression("width")}catch(m){return false}return true})();c.support=c.support||{};c.support.borderRadius=h||j||e;function g(m,n){return parseInt(c.css(m,n))||0}function k(m){m=parseInt(m).toString(16);return(m.length<2)?"0"+m:m}function b(o){while(o){var m=c.css(o,"backgroundColor"),n;if(m&&m!="transparent"&&m!="rgba(0, 0, 0, 0)"){if(m.indexOf("rgb")>=0){n=m.match(/\d+/g);return"#"+k(n[0])+k(n[1])+k(n[2])}return m}if(o.nodeName.toLowerCase()=="html"){break}o=o.parentNode}return"#ffffff"}function f(o,m,n){switch(o){case"round":return Math.round(n*(1-Math.cos(Math.asin(m/n))));case"cool":return Math.round(n*(1+Math.cos(Math.asin(m/n))));case"sharp":return n-m;case"bite":return Math.round(n*(Math.cos(Math.asin((n-m-1)/n))));case"slide":return Math.round(n*(Math.atan2(m,n/m)));case"jut":return Math.round(n*(Math.atan2(n,(n-m-1))));case"curl":return Math.round(n*(Math.atan(m)));case"tear":return Math.round(n*(Math.cos(m)));case"wicked":return Math.round(n*(Math.tan(m)));case"long":return Math.round(n*(Math.sqrt(m)));case"sculpt":return Math.round(n*(Math.log((n-m-1),n)));case"dogfold":case"dog":return(m&1)?(m+1):n;case"dog2":return(m&2)?(m+1):n;case"dog3":return(m&3)?(m+1):n;case"fray":return(m%2)*n;case"notch":return n;case"bevelfold":case"bevel":return m+1;case"steep":return m/2+1;case"invsteep":return(n-m)/2+1}}c.fn.corner=function(m){if(this.length==0){if(!c.isReady&&this.selector){var n=this.selector,o=this.context;c(function(){c(n,o).corner(m)})}return this}return this.each(function(v){var u=c(this),D=[u.attr(c.fn.corner.defaults.metaAttr)||"",m||""].join(" ").toLowerCase(),K=/keep/.test(D),C=((D.match(/cc:(#[0-9a-f]+)/)||[])[1]),p=((D.match(/sc:(#[0-9a-f]+)/)||[])[1]),G=parseInt((D.match(/(\d+)px/)||[])[1])||10,E=/round|bevelfold|bevel|notch|bite|cool|sharp|slide|jut|curl|tear|fray|wicked|sculpt|long|dog3|dog2|dogfold|dog|invsteep|steep/,r=((D.match(E)||["round"])[0]),s=/dogfold|bevelfold/.test(D),q={T:0,B:1},z={TL:/top|tl|left/.test(D),TR:/top|tr|right/.test(D),BL:/bottom|bl|left/.test(D),BR:/bottom|br|right/.test(D)},H,N,F,I,y,O,B,L,J,x,M,P,A,t;if(!z.TL&&!z.TR&&!z.BL&&!z.BR){z={TL:1,TR:1,BL:1,BR:1}}if(c.fn.corner.defaults.useNative&&r=="round"&&(e||h||j)&&!C&&!p){if(z.TL){u.css(e?"border-top-left-radius":h?"-moz-border-radius-topleft":"-webkit-border-top-left-radius",G+"px")}if(z.TR){u.css(e?"border-top-right-radius":h?"-moz-border-radius-topright":"-webkit-border-top-right-radius",G+"px")}if(z.BL){u.css(e?"border-bottom-left-radius":h?"-moz-border-radius-bottomleft":"-webkit-border-bottom-left-radius",G+"px")}if(z.BR){u.css(e?"border-bottom-right-radius":h?"-moz-border-radius-bottomright":"-webkit-border-bottom-right-radius",G+"px")}return}H=document.createElement("div");c(H).css({overflow:"hidden",height:"1px",minHeight:"1px",fontSize:"1px",backgroundColor:p||"transparent",borderStyle:"solid"});N={T:parseInt(c.css(this,"paddingTop"))||0,R:parseInt(c.css(this,"paddingRight"))||0,B:parseInt(c.css(this,"paddingBottom"))||0,L:parseInt(c.css(this,"paddingLeft"))||0};if(typeof this.style.zoom!=undefined){this.style.zoom=1}if(!K){this.style.border="none"}H.style.borderColor=C||b(this.parentNode);F=c(this).outerHeight();for(I in q){y=q[I];if((y&&(z.BL||z.BR))||(!y&&(z.TL||z.TR))){H.style.borderStyle="none "+(z[I+"R"]?"solid":"none")+" none "+(z[I+"L"]?"solid":"none");O=document.createElement("div");c(O).addClass("jquery-corner");B=O.style;y?this.appendChild(O):this.insertBefore(O,this.firstChild);if(y&&F!="auto"){if(c.css(this,"position")=="static"){this.style.position="relative"}B.position="absolute";B.bottom=B.left=B.padding=B.margin="0";if(i){B.setExpression("width","this.parentNode.offsetWidth")}else{B.width="100%"}}else{if(!y&&c.browser.msie){if(c.css(this,"position")=="static"){this.style.position="relative"}B.position="absolute";B.top=B.left=B.right=B.padding=B.margin="0";if(i){L=g(this,"borderLeftWidth")+g(this,"borderRightWidth");B.setExpression("width","this.parentNode.offsetWidth - "+L+'+ "px"')}else{B.width="100%"}}else{B.position="relative";B.margin=!y?"-"+N.T+"px -"+N.R+"px "+(N.T-G)+"px -"+N.L+"px":(N.B-G)+"px -"+N.R+"px -"+N.B+"px -"+N.L+"px"}}for(J=0;J<G;J++){x=Math.max(0,f(r,J,G));M=H.cloneNode(false);M.style.borderWidth="0 "+(z[I+"R"]?x:0)+"px 0 "+(z[I+"L"]?x:0)+"px";y?O.appendChild(M):O.insertBefore(M,O.firstChild)}if(s&&c.support.boxModel){if(y&&l){continue}for(P in z){if(!z[P]){continue}if(y&&(P=="TL"||P=="TR")){continue}if(!y&&(P=="BL"||P=="BR")){continue}A={position:"absolute",border:"none",margin:0,padding:0,overflow:"hidden",backgroundColor:H.style.borderColor};t=c("<div/>").css(A).css({width:G+"px",height:"1px"});switch(P){case"TL":t.css({bottom:0,left:0});break;case"TR":t.css({bottom:0,right:0});break;case"BL":t.css({top:0,left:0});break;case"BR":t.css({top:0,right:0});break}O.appendChild(t[0]);var Q=c("<div/>").css(A).css({top:0,bottom:0,width:"1px",height:G+"px"});switch(P){case"TL":Q.css({left:G});break;case"TR":Q.css({right:G});break;case"BL":Q.css({left:G});break;case"BR":Q.css({right:G});break}O.appendChild(Q[0])}}}}})};c.fn.uncorner=function(){if(e||h||j){this.css(e?"border-radius":h?"-moz-border-radius":"-webkit-border-radius",0)}c("div.jquery-corner",this).remove();return this};c.fn.corner.defaults={useNative:true,metaAttr:"data-corner"}})(jQuery);
(function(d,i,j){var c=j(d),h=j(i),b=j.fancybox=function(){b.open.apply(this,arguments)},g=!1,f=null;j.extend(b,{version:"2.0.4",defaults:{padding:15,margin:20,width:800,height:600,minWidth:200,minHeight:200,maxWidth:9999,maxHeight:9999,autoSize:!0,fitToView:!0,aspectRatio:!1,topRatio:0.5,fixed:!j.browser.msie||6<j.browser.version||!i.documentElement.hasOwnProperty("ontouchstart"),scrolling:"auto",wrapCSS:"fancybox-default",arrows:!0,closeBtn:!0,closeClick:!1,nextClick:!1,mouseWheel:!0,autoPlay:!1,playSpeed:3000,modal:!1,loop:!0,ajax:{},keys:{next:[13,32,34,39,40],prev:[8,33,37,38],close:[27]},tpl:{wrap:'<div class="fancybox-wrap"><div class="fancybox-outer"><div class="fancybox-inner"></div></div></div>',image:'<img class="fancybox-image" src="{href}" alt="" />',iframe:'<iframe class="fancybox-iframe" name="fancybox-frame{rnd}" frameborder="0" hspace="0" '+(j.browser.msie?'allowtransparency="true""':"")+' scrolling="{scrolling}" src="{href}"></iframe>',swf:'<object classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000" width="100%" height="100%"><param name="wmode" value="transparent" /><param name="allowfullscreen" value="true" /><param name="allowscriptaccess" value="always" /><param name="movie" value="{href}" /><embed src="{href}" type="application/x-shockwave-flash" allowfullscreen="true" allowscriptaccess="always" width="100%" height="100%" wmode="transparent"></embed></object>',error:'<p class="fancybox-error">The requested content cannot be loaded.<br/>Please try again later.</p>',closeBtn:'<div title="Close" class="fancybox-item fancybox-close"></div>',next:'<a title="Next" class="fancybox-item fancybox-next"><span></span></a>',prev:'<a title="Previous" class="fancybox-item fancybox-prev"><span></span></a>'},openEffect:"fade",openSpeed:250,openEasing:"swing",openOpacity:!0,openMethod:"zoomIn",closeEffect:"fade",closeSpeed:250,closeEasing:"swing",closeOpacity:!0,closeMethod:"zoomOut",nextEffect:"elastic",nextSpeed:300,nextEasing:"swing",nextMethod:"changeIn",prevEffect:"elastic",prevSpeed:300,prevEasing:"swing",prevMethod:"changeOut",helpers:{overlay:{speedIn:0,speedOut:300,opacity:0.8,css:{cursor:"pointer"},closeClick:!0},title:{type:"float"}}},group:{},opts:{},coming:null,current:null,isOpen:!1,isOpened:!1,wrap:null,outer:null,inner:null,player:{timer:null,isActive:!1},ajaxLoad:null,imgPreload:null,transitions:{},helpers:{},open:function(a,e){j.isArray(a)||(a=[a]);if(a.length){b.close(!0),b.opts=j.extend(!0,{},b.defaults,e),b.group=a,b._start(b.opts.index||0)}},cancel:function(){if(!(b.coming&&!1===b.trigger("onCancel"))&&(b.coming=null,b.hideLoading(),b.ajaxLoad&&b.ajaxLoad.abort(),b.ajaxLoad=null,b.imgPreload)){b.imgPreload.onload=b.imgPreload.onabort=b.imgPreload.onerror=null}},close:function(a){b.cancel();if(b.current&&!1!==b.trigger("beforeClose")){b.unbindEvents(),!b.isOpen||a&&!0===a[0]?(j(".fancybox-wrap").stop().trigger("onReset").remove(),b._afterZoomOut()):(b.isOpen=b.isOpened=!1,j(".fancybox-item").remove(),b.wrap.stop(!0).removeClass("fancybox-opened"),b.inner.css("overflow","hidden"),b.transitions[b.current.closeMethod]())}},play:function(a){var l=function(){clearTimeout(b.player.timer)},k=function(){l();if(b.current&&b.player.isActive){b.player.timer=setTimeout(b.next,b.current.playSpeed)}},e=function(){l();j("body").unbind(".player");b.player.isActive=!1;b.trigger("onPlayEnd")};if(b.player.isActive||a&&!1===a[0]){e()}else{if(b.current&&(b.current.loop||b.current.index<b.group.length-1)){b.player.isActive=!0,j("body").bind({"afterShow.player onUpdate.player":k,"onCancel.player beforeClose.player":e,"beforeLoad.player":l}),k(),b.trigger("onPlayStart")}}},next:function(){b.current&&b.jumpto(b.current.index+1)},prev:function(){b.current&&b.jumpto(b.current.index-1)},jumpto:function(a){b.current&&(a=parseInt(a,10),1<b.group.length&&b.current.loop&&(a>=b.group.length?a=0:0>a&&(a=b.group.length-1)),"undefined"!==typeof b.group[a]&&(b.cancel(),b._start(a)))},reposition:function(a){b.isOpen&&b.wrap.css(b._getPosition(a))},update:function(){b.isOpen&&(g||(f=setInterval(function(){if(g&&(g=!1,clearTimeout(f),b.current)){if(b.current.autoSize){b.inner.height("auto"),b.current.height=b.inner.height()}b._setDimension();b.current.canGrow&&b.inner.height("auto");b.reposition();b.trigger("onUpdate")}},100)),g=!0)},toggle:function(){if(b.isOpen){b.current.fitToView=!b.current.fitToView,b.update()}},hideLoading:function(){j("#fancybox-loading").remove()},showLoading:function(){b.hideLoading();j('<div id="fancybox-loading"></div>').click(b.cancel).appendTo("body")},getViewport:function(){return{x:c.scrollLeft(),y:c.scrollTop(),w:c.width(),h:c.height()}},unbindEvents:function(){b.wrap&&b.wrap.unbind(".fb");h.unbind(".fb");c.unbind(".fb")},bindEvents:function(){var a=b.current,e=a.keys;a&&(c.bind("resize.fb, orientationchange.fb",b.update),e&&h.bind("keydown.fb",function(k){var l;if(!k.ctrlKey&&!k.altKey&&!k.shiftKey&&!k.metaKey&&0>j.inArray(k.target.tagName.toLowerCase(),["input","textarea","select","button"])){l=k.keyCode,-1<j.inArray(l,e.close)?(b.close(),k.preventDefault()):-1<j.inArray(l,e.next)?(b.next(),k.preventDefault()):-1<j.inArray(l,e.prev)&&(b.prev(),k.preventDefault())}}),j.fn.mousewheel&&a.mouseWheel&&1<b.group.length&&b.wrap.bind("mousewheel.fb",function(k,m){var l=j(k.target).get(0);if(0===l.clientHeight||l.scrollHeight===l.clientHeight){k.preventDefault(),b[0<m?"prev":"next"]()}}))},trigger:function(a){var k,e=b[-1<j.inArray(a,["onCancel","beforeLoad","afterLoad"])?"coming":"current"];if(e){j.isFunction(e[a])&&(k=e[a].apply(e,Array.prototype.slice.call(arguments,1)));if(!1===k){return !1}e.helpers&&j.each(e.helpers,function(m,l){if(l&&"undefined"!==typeof b.helpers[m]&&j.isFunction(b.helpers[m][a])){b.helpers[m][a](l,e)}});j.event.trigger(a+".fb")}},isImage:function(e){return e&&e.match(/\.(jpg|gif|png|bmp|jpeg)(.*)?$/i)},isSWF:function(e){return e&&e.match(/\.(swf)(.*)?$/i)},_start:function(a){var o={},n=b.group[a]||null,l,m,e;if("object"===typeof n&&(n.nodeType||n instanceof j)){l=!0,j.metadata&&(o=j(n).metadata())}o=j.extend(!0,{},b.opts,{index:a,element:n},j.isPlainObject(n)?n:o);j.each(["href","title","content","type"],function(k,p){o[p]=b.opts[p]||l&&j(n).attr(p)||o[p]||null});if("number"===typeof o.margin){o.margin=[o.margin,o.margin,o.margin,o.margin]}o.modal&&j.extend(!0,o,{closeBtn:!1,closeClick:!1,nextClick:!1,arrows:!1,mouseWheel:!1,keys:null,helpers:{overlay:{css:{cursor:"auto"},closeClick:!1}}});b.coming=o;if(!1===b.trigger("beforeLoad")){b.coming=null}else{m=o.type;a=o.href;if(!m){l&&(e=j(n).data("fancybox-type"),!e&&n.className&&(m=(e=n.className.match(/fancybox\.(\w+)/))?e[1]:null)),!m&&a&&(b.isImage(a)?m="image":b.isSWF(a)?m="swf":a.match(/^#/)&&(m="inline")),m||(m=l?"inline":"html"),o.type=m}"inline"===m||"html"===m?(o.content=o.content||("inline"===m&&a?j(a):n),o.content.length||(m=null)):(o.href=a||n,o.href||(m=null));o.group=b.group;"image"===m?b._loadImage():"ajax"===m?b._loadAjax():m?b._afterLoad():b._error("type")}},_error:function(a){j.extend(b.coming,{type:"html",autoSize:!0,minHeight:"0",hasError:a,content:b.coming.tpl.error});b._afterLoad()},_loadImage:function(){b.imgPreload=new Image;b.imgPreload.onload=function(){this.onload=this.onerror=null;b.coming.width=this.width;b.coming.height=this.height;b._afterLoad()};b.imgPreload.onerror=function(){this.onload=this.onerror=null;b._error("image")};b.imgPreload.src=b.coming.href;b.imgPreload.complete||b.showLoading()},_loadAjax:function(){b.showLoading();b.ajaxLoad=j.ajax(j.extend({},b.coming.ajax,{url:b.coming.href,error:function(a,e){"abort"!==e?b._error("ajax",a):b.hideLoading()},success:function(a,e){if("success"===e){b.coming.content=a,b._afterLoad()}}}))},_preload:function(){var a=b.group,k=b.current.index,e=function(l){if(l&&b.isImage(l)){(new Image).src=l}};1<a.length&&(e(j(a[k+1]||a[0]).attr("href")),e(j(a[k-1]||a[a.length-1]).attr("href")))},_afterLoad:function(){b.hideLoading();!b.coming||!1===b.trigger("afterLoad",b.current)?b.coming=!1:(b.isOpened?(j(".fancybox-item").remove(),b.wrap.stop(!0).removeClass("fancybox-opened"),b.inner.css("overflow","hidden"),b.transitions[b.current.prevMethod]()):(j(".fancybox-wrap").stop().trigger("onReset").remove(),b.trigger("afterClose")),b.unbindEvents(),b.isOpen=!1,b.current=b.coming,b.coming=!1,b.wrap=j(b.current.tpl.wrap).addClass("fancybox-tmp "+b.current.wrapCSS).appendTo("body"),b.outer=j(".fancybox-outer",b.wrap).css("padding",b.current.padding+"px"),b.inner=j(".fancybox-inner",b.wrap),b._setContent(),b.trigger("beforeShow"),b._setDimension(),b.wrap.hide().removeClass("fancybox-tmp"),b.bindEvents(),b._preload(),b.transitions[b.isOpened?b.current.nextMethod:b.current.openMethod]())},_setContent:function(){var a,l,k=b.current,e=k.type;switch(e){case"inline":case"ajax":case"html":a=k.content;"inline"===e&&a instanceof j&&(a=a.show().detach(),a.parent().hasClass("fancybox-inner")&&a.parents(".fancybox-wrap").trigger("onReset").remove(),j(b.wrap).bind("onReset",function(){a.appendTo("body").hide()}));if(k.autoSize){l=j('<div class="fancybox-tmp"></div>').appendTo(j("body")).append(a),k.width=l.outerWidth(),k.height=l.outerHeight(!0),a=l.contents().detach(),l.remove()}break;case"image":a=k.tpl.image.replace("{href}",k.href);k.aspectRatio=!0;break;case"swf":a=k.tpl.swf.replace(/\{width\}/g,k.width).replace(/\{height\}/g,k.height).replace(/\{href\}/g,k.href);break;case"iframe":a=k.tpl.iframe.replace("{href}",k.href).replace("{scrolling}",k.scrolling).replace("{rnd}",(new Date).getTime())}if(-1<j.inArray(e,["image","swf","iframe"])){k.autoSize=!1,k.scrolling=!1}b.inner.append(a)},_setDimension:function(){var B=b.wrap,A=b.outer,z=b.inner,x=b.current,y=b.getViewport(),t=x.margin,v=2*x.padding,w=x.width+v,u=x.height+v,s=x.width/x.height,e=x.maxWidth,r=x.maxHeight,q=x.minWidth,a=x.minHeight;y.w-=t[1]+t[3];y.h-=t[0]+t[2];-1<w.toString().indexOf("%")&&(w=y.w*parseFloat(w)/100);-1<u.toString().indexOf("%")&&(u=y.h*parseFloat(u)/100);x.fitToView&&(e=Math.min(y.w,e),r=Math.min(y.h,r));q=Math.min(w,q);a=Math.min(w,a);e=Math.max(q,e);r=Math.max(a,r);x.aspectRatio?(w>e&&(w=e,u=(w-v)/s+v),u>r&&(u=r,w=(u-v)*s+v),w<q&&(w=q,u=(w-v)/s+v),u<a&&(u=a,w=(u-v)*s+v)):(w=Math.max(q,Math.min(w,e)),u=Math.max(a,Math.min(u,r)));w=Math.round(w);u=Math.round(u);j(B.add(A).add(z)).width("auto").height("auto");z.width(w-v).height(u-v);B.width(w);t=B.height();if(w>e||t>r){for(;(w>e||t>r)&&w>q&&t>a;){u-=10,x.aspectRatio?(w=Math.round((u-v)*s+v),w<q&&(w=q,u=(w-v)/s+v)):w-=10,z.width(w-v).height(u-v),B.width(w),t=B.height()}}x.dim={width:w,height:t};x.canGrow=x.autoSize&&u>a&&u<r;x.canShrink=!1;x.canExpand=!1;if(w-v<x.width||u-v<x.height){x.canExpand=!0}else{if((w>y.w||t>y.h)&&w>q&&u>a){x.canShrink=!0}}B=t-v;b.innerSpace=B-z.height();b.outerSpace=B-A.height()},_getPosition:function(a){var q=b.current,p=b.getViewport(),o=q.margin,n=b.wrap.width()+o[1]+o[3],l=b.wrap.height()+o[0]+o[2],m={position:"absolute",top:o[0]+p.y,left:o[3]+p.x};if(q.fixed&&(!a||!1===a[0])&&l<=p.h&&n<=p.w){m={position:"fixed",top:o[0],left:o[3]}}m.top=Math.ceil(Math.max(m.top,m.top+(p.h-l)*q.topRatio))+"px";m.left=Math.ceil(Math.max(m.left,m.left+0.5*(p.w-n)))+"px";return m},_afterZoomIn:function(){var a=b.current;b.isOpen=b.isOpened=!0;b.wrap.addClass("fancybox-opened").css("overflow","visible");b.update();b.inner.css("overflow","auto"===a.scrolling?"auto":"yes"===a.scrolling?"scroll":"hidden");if(a.closeClick||a.nextClick){b.inner.css("cursor","pointer").bind("click.fb",a.nextClick?b.next:b.close)}a.closeBtn&&j(a.tpl.closeBtn).appendTo(b.wrap).bind("click.fb",b.close);a.arrows&&1<b.group.length&&((a.loop||0<a.index)&&j(a.tpl.prev).appendTo(b.wrap).bind("click.fb",b.prev),(a.loop||a.index<b.group.length-1)&&j(a.tpl.next).appendTo(b.wrap).bind("click.fb",b.next));b.trigger("afterShow");if(b.opts.autoPlay&&!b.player.isActive){b.opts.autoPlay=!1,b.play()}},_afterZoomOut:function(){b.trigger("afterClose");b.wrap.trigger("onReset").remove();j.extend(b,{group:{},opts:{},current:null,isOpened:!1,isOpen:!1,wrap:null,outer:null,inner:null})}});b.transitions={getOrigPosition:function(){var a=b.current.element,m={},l=50,e=50,k;a&&a.nodeName&&j(a).is(":visible")?(k=j(a).find("img:first"),k.length?(m=k.offset(),l=k.outerWidth(),e=k.outerHeight()):m=j(a).offset()):(a=b.getViewport(),m.top=a.y+0.5*(a.h-e),m.left=a.x+0.5*(a.w-l));return m={top:Math.ceil(m.top)+"px",left:Math.ceil(m.left)+"px",width:Math.ceil(l)+"px",height:Math.ceil(e)+"px"}},step:function(a,n){var m,l,k;if("width"===n.prop||"height"===n.prop){l=k=Math.ceil(a-2*b.current.padding),"height"===n.prop&&(m=(a-n.start)/(n.end-n.start),n.start>n.end&&(m=1-m),l-=b.innerSpace*m,k-=b.outerSpace*m),b.inner[n.prop](l),b.outer[n.prop](k)}},zoomIn:function(){var a=b.wrap,l=b.current,k,e;k=l.dim;if("elastic"===l.openEffect){e=j.extend({},k,b._getPosition(!0));delete e.position;k=this.getOrigPosition();if(l.openOpacity){k.opacity=0,e.opacity=1}a.css(k).show().animate(e,{duration:l.openSpeed,easing:l.openEasing,step:this.step,complete:b._afterZoomIn})}else{a.css(j.extend({},k,b._getPosition())),"fade"===l.openEffect?a.fadeIn(l.openSpeed,b._afterZoomIn):(a.show(),b._afterZoomIn())}},zoomOut:function(){var a=b.wrap,k=b.current,e;if("elastic"===k.closeEffect){"fixed"===a.css("position")&&a.css(b._getPosition(!0));e=this.getOrigPosition();if(k.closeOpacity){e.opacity=0}a.animate(e,{duration:k.closeSpeed,easing:k.closeEasing,step:this.step,complete:b._afterZoomOut})}else{a.fadeOut("fade"===k.closeEffect?k.closeSpeed:0,b._afterZoomOut)}},changeIn:function(){var a=b.wrap,k=b.current,e;"elastic"===k.nextEffect?(e=b._getPosition(!0),e.opacity=0,e.top=parseInt(e.top,10)-200+"px",a.css(e).show().animate({opacity:1,top:"+=200px"},{duration:k.nextSpeed,complete:b._afterZoomIn})):(a.css(b._getPosition()),"fade"===k.nextEffect?a.hide().fadeIn(k.nextSpeed,b._afterZoomIn):(a.show(),b._afterZoomIn()))},changeOut:function(){var a=b.wrap,k=b.current,e=function(){j(this).trigger("onReset").remove()};a.removeClass("fancybox-opened");"elastic"===k.prevEffect?a.animate({opacity:0,top:"+=200px"},{duration:k.prevSpeed,complete:e}):a.fadeOut("fade"===k.prevEffect?k.prevSpeed:0,e)}};b.helpers.overlay={overlay:null,update:function(){var e,k;this.overlay.width(0).height(0);j.browser.msie?(e=Math.max(i.documentElement.scrollWidth,i.body.scrollWidth),k=Math.max(i.documentElement.offsetWidth,i.body.offsetWidth),e=e<k?c.width():e):e=h.width();this.overlay.width(e).height(h.height())},beforeShow:function(a){if(!this.overlay){this.overlay=j('<div id="fancybox-overlay"></div>').css(a.css||{background:"black"}).appendTo("body"),this.update(),a.closeClick&&this.overlay.bind("click.fb",b.close),c.bind("resize.fb",j.proxy(this.update,this)),this.overlay.fadeTo(a.speedIn||"fast",a.opacity||1)}},onUpdate:function(){this.update()},afterClose:function(e){this.overlay&&this.overlay.fadeOut(e.speedOut||"fast",function(){j(this).remove()});this.overlay=null}};b.helpers.title={beforeShow:function(a){var e;if(e=b.current.title){e=j('<div class="fancybox-title fancybox-title-'+a.type+'-wrap">'+e+"</div>").appendTo("body"),"float"===a.type&&(e.width(e.width()),e.wrapInner('<span class="child"></span>'),b.current.margin[2]+=Math.abs(parseInt(e.css("margin-bottom"),10))),e.appendTo("over"===a.type?b.inner:"outside"===a.type?b.wrap:b.outer)}}};j.fn.fancybox=function(a){function l(m){var p=[],n,o=this.rel;if(!m.ctrlKey&&!m.altKey&&!m.shiftKey&&!m.metaKey){m.preventDefault(),m=j(this).data("fancybox-group"),"undefined"!==typeof m?n=m?"data-fancybox-group":!1:o&&""!==o&&"nofollow"!==o&&(m=o,n="rel"),n&&(p=e.length?j(e).filter("["+n+'="'+m+'"]'):j("["+n+'="'+m+'"]')),p.length?(k.index=p.index(this),b.open(p.get(),k)):b.open(this,k)}}var k=a||{},e=this.selector||"";e?h.undelegate(e,"click.fb-start").delegate(e,"click.fb-start",l):j(this).unbind("click.fb-start").bind("click.fb-start",l);return this}})(window,document,jQuery);
(function(b){var a=b.fancybox;a.helpers.buttons={tpl:'<div id="fancybox-buttons"><ul><li><a class="btnPrev" title="Previous" href="javascript:;"></a></li><li><a class="btnPlay" title="Start slideshow" href="javascript:;"></a></li><li><a class="btnNext" title="Next" href="javascript:;"></a></li><li><a class="btnToggle" title="Toggle size" href="javascript:;"></a></li><li><a class="btnClose" title="Close" href="javascript:jQuery.fancybox.close();"></a></li></ul></div>',list:null,buttons:{},update:function(){var c=this.buttons.toggle.removeClass("btnDisabled btnToggleOn");if(a.current.canShrink){c.addClass("btnToggleOn")}else{if(!a.current.canExpand){c.addClass("btnDisabled")}}},beforeLoad:function(c){if(a.group.length<2){a.coming.helpers.buttons=false;a.coming.closeBtn=true;return}a.coming.margin[c.position==="bottom"?2:0]+=30},onPlayStart:function(){if(this.list){this.buttons.play.attr("title","Pause slideshow").addClass("btnPlayOn")}},onPlayEnd:function(){if(this.list){this.buttons.play.attr("title","Start slideshow").removeClass("btnPlayOn")}},afterShow:function(d){var c;if(!this.list){this.list=b(d.tpl||this.tpl).addClass(d.position||"top").appendTo("body");this.buttons={prev:this.list.find(".btnPrev").click(a.prev),next:this.list.find(".btnNext").click(a.next),play:this.list.find(".btnPlay").click(a.play),toggle:this.list.find(".btnToggle").click(a.toggle)}}c=this.buttons;if(a.current.index>0||a.current.loop){c.prev.removeClass("btnDisabled")}else{c.prev.addClass("btnDisabled")}if(a.current.loop||a.current.index<a.group.length-1){c.next.removeClass("btnDisabled");c.play.removeClass("btnDisabled")}else{c.next.addClass("btnDisabled");c.play.addClass("btnDisabled")}this.update()},onUpdate:function(){this.update()},beforeClose:function(){if(this.list){this.list.remove()}this.list=null;this.buttons={}}}}(jQuery));
(function(b){var a=b.fancybox;a.helpers.thumbs={wrap:null,list:null,width:0,source:function(d){var c=b(d).find("img");return c.length?c.attr("src"):d.href},init:function(f){var e=this,g,c=f.width||50,h=f.height||50,d=f.source||this.source;g="";for(var i=0;i<a.group.length;i++){g+='<li><a style="width:'+c+"px;height:"+h+'px;" href="javascript:jQuery.fancybox.jumpto('+i+');"></a></li>'}this.wrap=b('<div id="fancybox-thumbs"></div>').addClass(f.position||"bottom").appendTo("body");this.list=b("<ul>"+g+"</ul>").appendTo(this.wrap);b.each(a.group,function(j){b("<img />").load(function(){var o=this.width,k=this.height,n,l,m;if(!e.list||!o||!k){return}n=o/c;l=k/h;m=e.list.children().eq(j).find("a");if(n>=1&&l>=1){if(n>l){o=Math.floor(o/l);k=h}else{o=c;k=Math.floor(k/n)}}b(this).css({width:o,height:k,top:Math.floor(h/2-k/2),left:Math.floor(c/2-o/2)});m.width(c).height(h);b(this).hide().appendTo(m).fadeIn(300)}).attr("src",d(this))});this.width=this.list.children().eq(0).outerWidth();this.list.width(this.width*(a.group.length+1)).css("left",Math.floor(b(window).width()*0.5-(a.current.index*this.width+this.width*0.5)))},update:function(c){if(this.list){this.list.stop(true).animate({left:Math.floor(b(window).width()*0.5-(a.current.index*this.width+this.width*0.5))},150)}},beforeLoad:function(c){if(a.group.length<2){a.coming.helpers.thumbs=false;return}a.coming.margin[c.position==="top"?0:2]=c.height+30},afterShow:function(c){if(this.list){this.update(c)}else{this.init(c)}this.list.children().removeClass("active").eq(a.current.index).addClass("active")},onUpdate:function(){this.update()},beforeClose:function(){if(this.wrap){this.wrap.remove()}this.wrap=null;this.list=null;this.width=0}}}(jQuery));
(function(c){function a(f){var d=f||window.event,g=[].slice.call(arguments,1),l=0,j=0,k=0;f=c.event.fix(d);f.type="mousewheel";if(f.wheelDelta){l=f.wheelDelta/120}if(f.detail){l=-f.detail/3}k=l;if(d.axis!==undefined&&d.axis===d.HORIZONTAL_AXIS){k=0;j=-1*l}if(d.wheelDeltaY!==undefined){k=d.wheelDeltaY/120}if(d.wheelDeltaX!==undefined){j=-1*d.wheelDeltaX/120}g.unshift(f,l,j,k);return c.event.handle.apply(this,g)}var b=["DOMMouseScroll","mousewheel"];c.event.special.mousewheel={setup:function(){if(this.addEventListener){for(var d=b.length;d;){this.addEventListener(b[--d],a,false)}}else{this.onmousewheel=a}},teardown:function(){if(this.removeEventListener){for(var d=b.length;d;){this.removeEventListener(b[--d],a,false)}}else{this.onmousewheel=null}}};c.fn.extend({mousewheel:function(d){return d?this.bind("mousewheel",d):this.trigger("mousewheel")},unmousewheel:function(d){return this.unbind("mousewheel",d)}})})(jQuery);(function(b){var d={vertical:!1,rtl:!1,start:1,offset:1,size:null,scroll:3,visible:null,animation:"normal",easing:"swing",auto:0,wrap:null,initCallback:null,setupCallback:null,reloadCallback:null,itemLoadCallback:null,itemFirstInCallback:null,itemFirstOutCallback:null,itemLastInCallback:null,itemLastOutCallback:null,itemVisibleInCallback:null,itemVisibleOutCallback:null,animationStepCallback:null,buttonNextHTML:"<div></div>",buttonPrevHTML:"<div></div>",buttonNextEvent:"click",buttonPrevEvent:"click",buttonNextCallback:null,buttonPrevCallback:null,itemFallbackDimension:null},a=!1;b(window).bind("load.jcarousel",function(){a=!0});b.jcarousel=function(r,p){this.options=b.extend({},d,p||{});this.autoStopped=this.locked=!1;this.buttonPrevState=this.buttonNextState=this.buttonPrev=this.buttonNext=this.list=this.clip=this.container=null;if(!p||p.rtl===void 0){this.options.rtl=(b(r).attr("dir")||b("html").attr("dir")||"").toLowerCase()=="rtl"}this.wh=!this.options.vertical?"width":"height";this.lt=!this.options.vertical?this.options.rtl?"right":"left":"top";for(var q="",o=r.className.split(" "),m=0;m<o.length;m++){if(o[m].indexOf("jcarousel-skin")!=-1){b(r).removeClass(o[m]);q=o[m];break}}r.nodeName.toUpperCase()=="UL"||r.nodeName.toUpperCase()=="OL"?(this.list=b(r),this.clip=this.list.parents(".jcarousel-clip"),this.container=this.list.parents(".jcarousel-container")):(this.container=b(r),this.list=this.container.find("ul,ol").eq(0),this.clip=this.container.find(".jcarousel-clip"));if(this.clip.size()===0){this.clip=this.list.wrap("<div></div>").parent()}if(this.container.size()===0){this.container=this.clip.wrap("<div></div>").parent()}q!==""&&this.container.parent()[0].className.indexOf("jcarousel-skin")==-1&&this.container.wrap('<div class=" '+q+'"></div>');this.buttonPrev=b(".jcarousel-prev",this.container);if(this.buttonPrev.size()===0&&this.options.buttonPrevHTML!==null){this.buttonPrev=b(this.options.buttonPrevHTML).appendTo(this.container)}this.buttonPrev.addClass(this.className("jcarousel-prev"));this.buttonNext=b(".jcarousel-next",this.container);if(this.buttonNext.size()===0&&this.options.buttonNextHTML!==null){this.buttonNext=b(this.options.buttonNextHTML).appendTo(this.container)}this.buttonNext.addClass(this.className("jcarousel-next"));this.clip.addClass(this.className("jcarousel-clip")).css({position:"relative"});this.list.addClass(this.className("jcarousel-list")).css({overflow:"hidden",position:"relative",top:0,margin:0,padding:0}).css(this.options.rtl?"right":"left",0);this.container.addClass(this.className("jcarousel-container")).css({position:"relative"});!this.options.vertical&&this.options.rtl&&this.container.addClass("jcarousel-direction-rtl").attr("dir","rtl");var g=this.options.visible!==null?Math.ceil(this.clipping()/this.options.visible):null,q=this.list.children("li"),n=this;if(q.size()>0){var l=0,k=this.options.offset;q.each(function(){n.format(this,k++);l+=n.dimension(this,g)});this.list.css(this.wh,l+100+"px");if(!p||p.size===void 0){this.options.size=q.size()}}this.container.css("display","block");this.buttonNext.css("display","block");this.buttonPrev.css("display","block");this.funcNext=function(){n.next()};this.funcPrev=function(){n.prev()};this.funcResize=function(){n.resizeTimer&&clearTimeout(n.resizeTimer);n.resizeTimer=setTimeout(function(){n.reload()},100)};this.options.initCallback!==null&&this.options.initCallback(this,"init");!a&&b.browser.safari?(this.buttons(!1,!1),b(window).bind("load.jcarousel",function(){n.setup()})):this.setup()};var c=b.jcarousel;c.fn=c.prototype={jcarousel:"0.2.8"};c.fn.extend=c.extend=b.extend;c.fn.extend({setup:function(){this.prevLast=this.prevFirst=this.last=this.first=null;this.animating=!1;this.tail=this.resizeTimer=this.timer=null;this.inTail=!1;if(!this.locked){this.list.css(this.lt,this.pos(this.options.offset)+"px");var e=this.pos(this.options.start,!0);this.prevFirst=this.prevLast=null;this.animate(e,!1);b(window).unbind("resize.jcarousel",this.funcResize).bind("resize.jcarousel",this.funcResize);this.options.setupCallback!==null&&this.options.setupCallback(this)}},reset:function(){this.list.empty();this.list.css(this.lt,"0px");this.list.css(this.wh,"10px");this.options.initCallback!==null&&this.options.initCallback(this,"reset");this.setup()},reload:function(){this.tail!==null&&this.inTail&&this.list.css(this.lt,c.intval(this.list.css(this.lt))+this.tail);this.tail=null;this.inTail=!1;this.options.reloadCallback!==null&&this.options.reloadCallback(this);if(this.options.visible!==null){var f=this,h=Math.ceil(this.clipping()/this.options.visible),e=0,g=0;this.list.children("li").each(function(i){e+=f.dimension(this,h);i+1<f.first&&(g=e)});this.list.css(this.wh,e+"px");this.list.css(this.lt,-g+"px")}this.scroll(this.first,!1)},lock:function(){this.locked=!0;this.buttons()},unlock:function(){this.locked=!1;this.buttons()},size:function(e){if(e!==void 0){this.options.size=e,this.locked||this.buttons()}return this.options.size},has:function(f,h){if(h===void 0||!h){h=f}if(this.options.size!==null&&h>this.options.size){h=this.options.size}for(var e=f;e<=h;e++){var g=this.get(e);if(!g.length||g.hasClass("jcarousel-item-placeholder")){return !1}}return !0},get:function(e){return b(">.jcarousel-item-"+e,this.list)},add:function(g,m){var f=this.get(g),l=0,k=b(m);if(f.length===0){for(var h,i=c.intval(g),f=this.create(g);;){if(h=this.get(--i),i<=0||h.length){i<=0?this.list.prepend(f):h.after(f);break}}}else{l=this.dimension(f)}k.get(0).nodeName.toUpperCase()=="LI"?(f.replaceWith(k),f=k):f.empty().append(m);this.format(f.removeClass(this.className("jcarousel-item-placeholder")),g);k=this.options.visible!==null?Math.ceil(this.clipping()/this.options.visible):null;l=this.dimension(f,k)-l;g>0&&g<this.first&&this.list.css(this.lt,c.intval(this.list.css(this.lt))-l+"px");this.list.css(this.wh,c.intval(this.list.css(this.wh))+l+"px");return f},remove:function(f){var g=this.get(f);if(g.length&&!(f>=this.first&&f<=this.last)){var e=this.dimension(g);f<this.first&&this.list.css(this.lt,c.intval(this.list.css(this.lt))+e+"px");g.remove();this.list.css(this.wh,c.intval(this.list.css(this.wh))-e+"px")}},next:function(){this.tail!==null&&!this.inTail?this.scrollTail(!1):this.scroll((this.options.wrap=="both"||this.options.wrap=="last")&&this.options.size!==null&&this.last==this.options.size?1:this.first+this.options.scroll)},prev:function(){this.tail!==null&&this.inTail?this.scrollTail(!0):this.scroll((this.options.wrap=="both"||this.options.wrap=="first")&&this.options.size!==null&&this.first==1?this.options.size:this.first-this.options.scroll)},scrollTail:function(e){if(!this.locked&&!this.animating&&this.tail){this.pauseAuto();var f=c.intval(this.list.css(this.lt)),f=!e?f-this.tail:f+this.tail;this.inTail=!e;this.prevFirst=this.first;this.prevLast=this.last;this.animate(f)}},scroll:function(e,f){!this.locked&&!this.animating&&(this.pauseAuto(),this.animate(this.pos(e),f))},pos:function(B,z){var A=c.intval(this.list.css(this.lt));if(this.locked||this.animating){return A}this.options.wrap!="circular"&&(B=B<1?1:this.options.size&&B>this.options.size?this.options.size:B);for(var y=this.first>B,w=this.options.wrap!="circular"&&this.first<=1?1:this.first,t=y?this.get(w):this.get(this.last),x=y?w:w-1,v=null,u=0,s=!1,r=0;y?--x>=B:++x<B;){v=this.get(x);s=!v.length;if(v.length===0&&(v=this.create(x).addClass(this.className("jcarousel-item-placeholder")),t[y?"before":"after"](v),this.first!==null&&this.options.wrap=="circular"&&this.options.size!==null&&(x<=0||x>this.options.size))){t=this.get(this.index(x)),t.length&&(v=this.add(x,t.clone(!0)))}t=v;r=this.dimension(v);s&&(u+=r);if(this.first!==null&&(this.options.wrap=="circular"||x>=1&&(this.options.size===null||x<=this.options.size))){A=y?A+r:A-r}}for(var w=this.clipping(),q=[],f=0,p=0,t=this.get(B-1),x=B;++f;){v=this.get(x);s=!v.length;if(v.length===0){v=this.create(x).addClass(this.className("jcarousel-item-placeholder"));if(t.length===0){this.list.prepend(v)}else{t[y?"before":"after"](v)}if(this.first!==null&&this.options.wrap=="circular"&&this.options.size!==null&&(x<=0||x>this.options.size)){t=this.get(this.index(x)),t.length&&(v=this.add(x,t.clone(!0)))}}t=v;r=this.dimension(v);if(r===0){throw Error("jCarousel: No width/height set for items. This will cause an infinite loop. Aborting...")}this.options.wrap!="circular"&&this.options.size!==null&&x>this.options.size?q.push(v):s&&(u+=r);p+=r;if(p>=w){break}x++}for(v=0;v<q.length;v++){q[v].remove()}u>0&&(this.list.css(this.wh,this.dimension(this.list)+u+"px"),y&&(A-=u,this.list.css(this.lt,c.intval(this.list.css(this.lt))-u+"px")));u=B+f-1;if(this.options.wrap!="circular"&&this.options.size&&u>this.options.size){u=this.options.size}if(x>u){f=0;x=u;for(p=0;++f;){v=this.get(x--);if(!v.length){break}p+=this.dimension(v);if(p>=w){break}}}x=u-f+1;this.options.wrap!="circular"&&x<1&&(x=1);if(this.inTail&&y){A+=this.tail,this.inTail=!1}this.tail=null;if(this.options.wrap!="circular"&&u==this.options.size&&u-f+1>=1&&(y=c.intval(this.get(u).css(!this.options.vertical?"marginRight":"marginBottom")),p-y>w)){this.tail=p-w-y}if(z&&B===this.options.size&&this.tail){A-=this.tail,this.inTail=!0}for(;B-->x;){A+=this.dimension(this.get(B))}this.prevFirst=this.first;this.prevLast=this.last;this.first=x;this.last=u;return A},animate:function(g,j){if(!this.locked&&!this.animating){this.animating=!0;var e=this,i=function(){e.animating=!1;g===0&&e.list.css(e.lt,0);!e.autoStopped&&(e.options.wrap=="circular"||e.options.wrap=="both"||e.options.wrap=="last"||e.options.size===null||e.last<e.options.size||e.last==e.options.size&&e.tail!==null&&!e.inTail)&&e.startAuto();e.buttons();e.notify("onAfterAnimation");if(e.options.wrap=="circular"&&e.options.size!==null){for(var f=e.prevFirst;f<=e.prevLast;f++){f!==null&&!(f>=e.first&&f<=e.last)&&(f<1||f>e.options.size)&&e.remove(f)}}};this.notify("onBeforeAnimation");if(!this.options.animation||j===!1){this.list.css(this.lt,g+"px"),i()}else{var h=!this.options.vertical?this.options.rtl?{right:g}:{left:g}:{top:g},i={duration:this.options.animation,easing:this.options.easing,complete:i};if(b.isFunction(this.options.animationStepCallback)){i.step=this.options.animationStepCallback}this.list.animate(h,i)}}},startAuto:function(e){if(e!==void 0){this.options.auto=e}if(this.options.auto===0){return this.stopAuto()}if(this.timer===null){this.autoStopped=!1;var f=this;this.timer=window.setTimeout(function(){f.next()},this.options.auto*1000)}},stopAuto:function(){this.pauseAuto();this.autoStopped=!0},pauseAuto:function(){if(this.timer!==null){window.clearTimeout(this.timer),this.timer=null}},buttons:function(f,g){if(f==null&&(f=!this.locked&&this.options.size!==0&&(this.options.wrap&&this.options.wrap!="first"||this.options.size===null||this.last<this.options.size),!this.locked&&(!this.options.wrap||this.options.wrap=="first")&&this.options.size!==null&&this.last>=this.options.size)){f=this.tail!==null&&!this.inTail}if(g==null&&(g=!this.locked&&this.options.size!==0&&(this.options.wrap&&this.options.wrap!="last"||this.first>1),!this.locked&&(!this.options.wrap||this.options.wrap=="last")&&this.options.size!==null&&this.first==1)){g=this.tail!==null&&this.inTail}var e=this;this.buttonNext.size()>0?(this.buttonNext.unbind(this.options.buttonNextEvent+".jcarousel",this.funcNext),f&&this.buttonNext.bind(this.options.buttonNextEvent+".jcarousel",this.funcNext),this.buttonNext[f?"removeClass":"addClass"](this.className("jcarousel-next-disabled")).attr("disabled",f?!1:!0),this.options.buttonNextCallback!==null&&this.buttonNext.data("jcarouselstate")!=f&&this.buttonNext.each(function(){e.options.buttonNextCallback(e,this,f)}).data("jcarouselstate",f)):this.options.buttonNextCallback!==null&&this.buttonNextState!=f&&this.options.buttonNextCallback(e,null,f);this.buttonPrev.size()>0?(this.buttonPrev.unbind(this.options.buttonPrevEvent+".jcarousel",this.funcPrev),g&&this.buttonPrev.bind(this.options.buttonPrevEvent+".jcarousel",this.funcPrev),this.buttonPrev[g?"removeClass":"addClass"](this.className("jcarousel-prev-disabled")).attr("disabled",g?!1:!0),this.options.buttonPrevCallback!==null&&this.buttonPrev.data("jcarouselstate")!=g&&this.buttonPrev.each(function(){e.options.buttonPrevCallback(e,this,g)}).data("jcarouselstate",g)):this.options.buttonPrevCallback!==null&&this.buttonPrevState!=g&&this.options.buttonPrevCallback(e,null,g);this.buttonNextState=f;this.buttonPrevState=g},notify:function(e){var f=this.prevFirst===null?"init":this.prevFirst<this.first?"next":"prev";this.callback("itemLoadCallback",e,f);this.prevFirst!==this.first&&(this.callback("itemFirstInCallback",e,f,this.first),this.callback("itemFirstOutCallback",e,f,this.prevFirst));this.prevLast!==this.last&&(this.callback("itemLastInCallback",e,f,this.last),this.callback("itemLastOutCallback",e,f,this.prevLast));this.callback("itemVisibleInCallback",e,f,this.first,this.last,this.prevFirst,this.prevLast);this.callback("itemVisibleOutCallback",e,f,this.prevFirst,this.prevLast,this.first,this.last)},callback:function(t,r,s,q,o,l,p){if(!(this.options[t]==null||typeof this.options[t]!="object"&&r!="onAfterAnimation")){var n=typeof this.options[t]=="object"?this.options[t][r]:this.options[t];if(b.isFunction(n)){var m=this;if(q===void 0){n(m,s,r)}else{if(o===void 0){this.get(q).each(function(){n(m,this,q,s,r)})}else{for(var t=function(e){m.get(e).each(function(){n(m,this,e,s,r)})},g=q;g<=o;g++){g!==null&&!(g>=l&&g<=p)&&t(g)}}}}}},create:function(e){return this.format("<li></li>",e)},format:function(f,h){for(var f=b(f),e=f.get(0).className.split(" "),g=0;g<e.length;g++){e[g].indexOf("jcarousel-")!=-1&&f.removeClass(e[g])}f.addClass(this.className("jcarousel-item")).addClass(this.className("jcarousel-item-"+h)).css({"float":this.options.rtl?"right":"left","list-style":"none"}).attr("jcarouselindex",h);return f},className:function(e){return e+" "+e+(!this.options.vertical?"-horizontal":"-vertical")},dimension:function(f,h){var e=b(f);if(h==null){return !this.options.vertical?e.outerWidth(!0)||c.intval(this.options.itemFallbackDimension):e.outerHeight(!0)||c.intval(this.options.itemFallbackDimension)}else{var g=!this.options.vertical?h-c.intval(e.css("marginLeft"))-c.intval(e.css("marginRight")):h-c.intval(e.css("marginTop"))-c.intval(e.css("marginBottom"));b(e).css(this.wh,g+"px");return this.dimension(e)}},clipping:function(){return !this.options.vertical?this.clip[0].offsetWidth-c.intval(this.clip.css("borderLeftWidth"))-c.intval(this.clip.css("borderRightWidth")):this.clip[0].offsetHeight-c.intval(this.clip.css("borderTopWidth"))-c.intval(this.clip.css("borderBottomWidth"))},index:function(e,f){if(f==null){f=this.options.size}return Math.round(((e-1)/f-Math.floor((e-1)/f))*f)+1}});c.extend({defaults:function(e){return b.extend(d,e||{})},intval:function(e){e=parseInt(e,10);return isNaN(e)?0:e},windowLoaded:function(){a=!0}});b.fn.jcarousel=function(f){if(typeof f=="string"){var g=b(this).data("jcarousel"),e=Array.prototype.slice.call(arguments,1);return g[f].apply(g,e)}else{return this.each(function(){var h=b(this).data("jcarousel");h?(f&&b.extend(h.options,f),h.reload()):b(this).data("jcarousel",new c(this,f))})}}})(jQuery);(function(b){var c={autoPlay:true,speed:5000,text:{play:"",stop:"",previous:"Previous",next:"Next",loading:"Loading"},transition:[1],showCaption:true,IESafe:false,showTooltips:false,carousel:false,carouselVertical:false,animationFinished:null,buildFinished:null,bindFinished:null,startOn:0,thumbOpacity:0.4,hoverPause:false,animationSpeed:600,fadeThumbsIn:false,carouselOptions:{}};b.fn.PikaChoose=function(d){return this.each(function(){b(this).data("pikachoose",new a(this,d))})};b.PikaChoose=function(h,i){this.options=b.extend({},c,i||{});this.list=null;this.image=null;this.anchor=null;this.caption=null;this.imgNav=null;this.imgPlay=null;this.imgPrev=null;this.imgNext=null;this.textNext=null;this.textPrev=null;this.previous=null;this.next=null;this.aniWrap=null;this.aniDiv=null;this.aniImg=null;this.thumbs=null;this.transition=null;this.active=null;this.tooltip=null;this.animating=false;this.stillOut=null;this.counter=null;this.timeOut=null;if(typeof(this.options.data)!="undefined"){h=b("<ul></ul>").appendTo(h);b.each(this.options.data,function(){if(typeof(this.link)!="undefined"){var e=b("<li><a href='"+this.link+"'><img></a></li>").appendTo(h);if(typeof(this.title)!="undefined"){e.find("a").attr("title",this.title)}}else{var e=b("<li><img></li>").appendTo(h)}if(typeof(this.caption)!="undefined"){e.append("<span>"+this.caption+"</span>")}if(typeof(this.thumbnail)!="undefined"){e.find("img").attr("ref",this.image);e.find("img").attr("src",this.thumbnail)}else{e.find("img").attr("src",this.image)}})}if(h instanceof jQuery||h.nodeName.toUpperCase()=="UL"||h.nodeName.toUpperCase()=="OL"){this.list=b(h);this.build();this.bindEvents()}else{return}var j=0;var d=0;for(var g=0;g<25;g++){var f='<div col="'+j+'" row="'+d+'"></div>';this.aniDiv.append(f);j++;if(j==5){d++;j=0}}};var a=b.PikaChoose;a.fn=a.prototype={pikachoose:"4.4.3"};b.fn.pikachoose=b.fn.PikaChoose;a.fn.extend=a.extend=b.extend;a.fn.extend({build:function(){this.step=0;this.wrap=b("<div class='pika-stage'></div>").insertBefore(this.list);this.image=b("<img>").appendTo(this.wrap);this.imgNav=b("<div class='pika-imgnav'></div>").insertAfter(this.image);this.imgPlay=b("<a></a>").appendTo(this.imgNav);this.counter=b("<span class='pika-counter'></span>").appendTo(this.imgNav);if(this.options.autoPlay){this.imgPlay.addClass("pause")}else{this.imgPlay.addClass("play")}this.imgPrev=b("<a class='previous'></a>").insertAfter(this.imgPlay);this.imgNext=b("<a class='next'></a>").insertAfter(this.imgPrev);this.caption=b("<div class='caption'></div>").insertAfter(this.imgNav).hide();this.tooltip=b("<div class='pika-tooltip'></div>").insertAfter(this.list).hide();this.aniWrap=b("<div class='pika-aniwrap'></div>").insertAfter(this.caption);this.aniImg=b("<img>").appendTo(this.aniWrap).hide();this.aniDiv=b("<div class='pika-ani'></div>").appendTo(this.aniWrap);this.textNav=b("<div class='pika-textnav'></div>").insertAfter(this.aniWrap);this.textPrev=b("<a class='previous'>"+this.options.text.previous+"</a>").appendTo(this.textNav);this.textNext=b("<a class='next'>"+this.options.text.next+"</a>").appendTo(this.textNav);this.list.addClass("pika-thumbs");this.thumbs=this.list.find("img");this.loader=b("<div class='pika-loader'></div>").appendTo(this.wrap).hide().html(this.options.text.loading);this.active=this.thumbs.eq(this.options.startOn);this.finishAnimating({source:this.active.attr("ref")||this.active.attr("src"),caption:this.active.parents("li:first").find("span:first").html(),clickThrough:this.active.parent().attr("href")||"",clickThroughTitle:this.active.parent().attr("title")||""});this.aniDiv.css({position:"relative"});var e=this;this.updateThumbs();if(this.options.fadeThumbsIn){this.list.fadeIn()}if(this.options.carousel){var d={vertical:this.options.carouselVertical,initCallback:function(g){jQuery(g.list).find("img").click(function(k,h){if(typeof(h)!=="undefined"&&h.how=="auto"){if(e.options.autoPlay==false){return false}}var i=parseInt(jQuery(this).parents(".jcarousel-item").attr("jcarouselindex"));var j=(jQuery(this).parents("ul").find("li:last").attr("jcarouselindex")==i-1)?true:false;if(!j){i=(i-2<=0)?0:i-2}i++;g.scroll(i)})}};var f=b.extend({},d,this.options.carouselOptions||{});this.list.jcarousel(f)}if(typeof(this.options.buildFinished)=="function"){this.options.buildFinished(this)}},createThumb:function(g){var d=g;var e=this;this.thumbs=this.list.find("img");if(typeof b.data(g[0],"source")!=="undefined"){return}g.parents("li:first").wrapInner("<div class='clip' />");d.hide();b.data(g[0],"clickThrough",d.parent("a").attr("href")||"");b.data(g[0],"clickThroughTitle",d.parent("a").attr("title")||"");if(d.parent("a").length>0){d.unwrap()}b.data(g[0],"caption",d.next("span").html()||"");d.next("span").remove();b.data(g[0],"source",d.attr("ref")||d.attr("src"));b.data(g[0],"order",d.closest("ul").find("li").index(d.parents("li")));var f=b.data(g[0]);b("<img />").bind("load",{data:f},function(){if(typeof(e.options.buildThumbStart)=="function"){e.options.buildThumbStart(e)}var k=b(this);var i=this.width;var n=this.height;if(i===0){i=k.attr("width")}if(n===0){n=k.attr("height")}var p=parseInt(d.parents(".clip").css("height").slice(0,-2));var m=parseInt(d.parents(".clip").css("width").slice(0,-2));if(m==0){m=d.parents("li:first").outerWidth()}if(p==0){p=d.parents("li:first").outerHeight()}var o=m/i;var j=p/n;var l;if(o<j){d.css({height:"100%"})}else{d.css({width:"100%"})}d.hover(function(h){clearTimeout(e.stillOut);b(this).stop(true,true).fadeTo(250,1);if(!e.options.showTooltips){return}e.tooltip.show().stop(true,true).html(f.caption).animate({top:b(this).parent().position().top,left:b(this).parent().position().left,opacity:1},"fast")},function(h){if(!b(this).hasClass("active")){b(this).stop(true,true).fadeTo(250,e.options.thumbOpacity);e.stillOut=setTimeout(e.hideTooltip,700)}});if(f.order==e.options.startOn){d.fadeTo(250,1);d.addClass("active");d.parents("li").eq(0).addClass("active")}else{d.fadeTo(250,e.options.thumbOpacity)}if(typeof(e.options.buildThumbFinish)=="function"){e.options.buildThumbFinish(e)}}).attr("src",d.attr("src"))},bindEvents:function(){this.thumbs.bind("click",{self:this},this.imgClick);this.imgNext.bind("click",{self:this},this.nextClick);this.textNext.bind("click",{self:this},this.nextClick);this.imgPrev.bind("click",{self:this},this.prevClick);this.textPrev.bind("click",{self:this},this.prevClick);this.imgPlay.unbind("click").bind("click",{self:this},this.playClick);this.wrap.unbind("mouseenter").bind("mouseenter",{self:this},function(d){d.data.self.imgNav.stop(true,true).fadeTo("slow",1);if(d.data.self.options.hoverPause==true){clearTimeout(d.data.self.timeOut)}});this.wrap.unbind("mouseleave").bind("mouseleave",{self:this},function(d){d.data.self.imgNav.stop(true,true).fadeTo("slow",0);if(d.data.self.options.autoPlay==true&&d.data.self.options.hoverPause){d.data.self.timeOut=setTimeout((function(e){return function(){e.nextClick()}})(d.data.self),d.data.self.options.speed)}});this.tooltip.unbind("mouseenter").bind("mouseenter",{self:this},function(d){clearTimeout(d.data.self.stillOut)});this.tooltip.unbind("mouseleave").bind("mouseleave",{self:this},function(d){d.data.self.stillOut=setTimeout(d.data.self.hideTooltip,700)});if(typeof(this.options.bindsFinished)=="function"){this.options.bindsFinished(this)}},hideTooltip:function(d){b(".pika-tooltip").animate({opacity:0.01})},imgClick:function(h,d){var f=h.data.self;var g=b.data(this);if(f.animating){return}if(typeof(d)=="undefined"||d.how!="auto"){if(f.options.autoPlay){f.imgPlay.trigger("click")}}else{if(f.options.autoPlay==false){return false}}if(b(this).attr("src")!==b.data(this).source){f.loader.fadeIn("fast")}f.caption.fadeOut("slow");f.animating=true;f.active.fadeTo(300,f.options.thumbOpacity).removeClass("active");f.active.parents(".active").eq(0).removeClass("active");f.active=b(this);f.active.addClass("active").fadeTo(200,1);f.active.parents("li").eq(0).addClass("active");b("<img />").bind("load",{self:f,data:g},function(){f.loader.fadeOut("fast");f.aniDiv.css({height:f.image.height(),width:f.image.width()}).show();f.aniDiv.children("div").css({width:"20%",height:"20%","float":"left"});var e=0;if(f.options.transition[0]==-1){e=Math.floor(Math.random()*7)+1}else{e=f.options.transition[f.step];f.step++;if(f.step>=f.options.transition.length){f.step=0}}if(f.options.IESafe&&b.browser.msie){e=1}f.doAnimation(e,g)}).attr("src",b.data(this).source)},doAnimation:function(i,g){this.aniWrap.css({position:"absolute",top:this.image.position().top,left:this.image.position().left});var e=this;e.image.stop(true,false);e.caption.stop().fadeOut();var f=e.aniDiv.children("div").eq(0).width();var h=e.aniDiv.children("div").eq(0).height();var d=new Image();b(d).attr("src",g.source);if(d.height!=e.image.height()||d.width!=e.image.width()){if(i!=0&&i!=1&&i!=7){}}e.aniDiv.css({height:e.image.height(),width:e.image.width()});e.aniDiv.children().each(function(){var l=b(this);var j=Math.floor(l.parent().width()/5)*l.attr("col");var k=Math.floor(l.parent().height()/5)*l.attr("row");l.css({background:"url("+g.source+") -"+j+"px -"+k+"px","background-size":l.parent().width()+"px "+l.parent().height()+"px",width:"0px",height:"0px",position:"absolute",top:k+"px",left:j+"px","float":"none"})});e.aniDiv.hide();e.aniImg.hide();switch(i){case 0:e.image.stop(true,true).fadeOut(e.options.animationSpeed,function(){e.image.attr("src",g.source).fadeIn(e.options.animationSpeed,function(){e.finishAnimating(g)})});break;case 1:e.aniDiv.hide();e.aniImg.height(e.image.height()).hide().attr("src",g.source);e.wrap.css({height:e.image.height()});b.when(e.image.fadeOut(e.options.animationSpeed),e.aniImg.eq(0).fadeIn(e.options.animationSpeed)).done(function(){e.finishAnimating(g)});break;case 2:e.aniDiv.show().children().hide().each(function(k){var j=k*30;b(this).css({opacity:0.1}).show().delay(j).animate({opacity:1,width:f,height:h},200,"linear",function(){if(e.aniDiv.find("div").index(this)==24){e.finishAnimating(g)}})});break;case 3:e.aniDiv.show().children("div:lt(5)").hide().each(function(k){var j=b(this).attr("col")*100;b(this).css({opacity:0.1,width:f}).show().delay(j).animate({opacity:1,height:e.image.height()},e.options.animationSpeed,"linear",function(){if(e.aniDiv.find(" div").index(this)==4){e.finishAnimating(g)}})});break;case 4:e.aniDiv.show().children().hide().each(function(k){if(k>4){return}var j=b(this).attr("col")*30;var m=e.gapper(b(this),h,f);var l=e.options.animationSpeed*0.7;b(this).css({opacity:0.1,height:"100%"}).show().delay(j).animate({opacity:1,width:m.width},l,"linear",function(){if(e.aniDiv.find(" div").index(this)==4){e.finishAnimating(g)}})});break;case 5:e.aniDiv.show().children().show().each(function(k){var j=k*Math.floor(Math.random()*5)*7;var l=e.gapper(b(this),h,f);if(b(".animation div").index(this)==24){j=700}b(this).css({height:l.height,width:l.width,opacity:0.01}).delay(j).animate({opacity:1},e.options.animationSpeed,function(){if(e.aniDiv.find(" div").index(this)==24){e.finishAnimating(g)}})});break;case 6:e.aniDiv.height(e.image.height()).hide().css({background:"url("+g.source+") top left no-repeat"});e.aniDiv.children("div").hide();e.aniDiv.css({width:0}).show().animate({width:e.image.width()},e.options.animationSpeed,function(){e.finishAnimating(g);e.aniDiv.css({background:"transparent"})});break;case 7:e.wrap.css({overflow:"hidden"});e.aniImg.height(e.image.height()).hide().attr("src",g.source);e.aniDiv.hide();e.image.css({position:"relative"}).animate({left:"-"+e.wrap.outerWidth()+"px"});e.aniImg.show();e.aniWrap.css({left:e.wrap.outerWidth()}).show().animate({left:"0px"},e.options.animationSpeed,function(){e.finishAnimating(g)});break}},finishAnimating:function(f){this.animating=false;this.image.attr("src",f.source);this.image.css({left:"0"});this.image.show();var d=this;b("<img />").bind("load",function(){d.aniImg.fadeOut("fast");d.aniDiv.fadeOut("fast")}).attr("src",f.source);var g=this.list.find("img").index(this.active);g++;var e=this.list.find("img").length;this.counter.html(g+"/"+e);if(f.clickThrough!=""){if(this.anchor==null){this.anchor=this.image.wrap("<a>").parent()}this.anchor.attr("href",f.clickThrough);this.anchor.attr("title",f.clickThroughTitle)}else{if(this.image.parent("a").length>0){this.image.unwrap()}this.anchor=null}if(this.options.showCaption&&f.caption!=""&&f.caption!=null){this.caption.html(f.caption).fadeTo("slow",1)}if(this.options.autoPlay==true){var d=this;this.timeOut=setTimeout((function(h){return function(){h.nextClick()}})(this),this.options.speed,this.timeOut)}if(typeof(this.options.animationFinished)=="function"){this.options.animationFinished(this)}},gapper:function(e,f,d){var g;if(e.attr("row")==4){g=(this.aniDiv.height()-(f*5))+f;f=g}if(e.attr("col")==4){g=(this.aniDiv.width()-(d*5))+d;d=g}return{height:f,width:d}},nextClick:function(i){var g="natural";try{var d=i.data.self;if(typeof(i.data.self.options.next)=="function"){i.data.self.options.next(this)}}catch(h){var d=this;g="auto"}var f=d.active.parents("li:first").next().find("img");if(f.length==0){f=d.list.find("img").eq(0)}f.trigger("click",{how:g})},prevClick:function(g){if(typeof(g.data.self.options.previous)=="function"){g.data.self.options.previous(this)}var d=g.data.self;var f=d.active.parents("li:first").prev().find("img");if(f.length==0){f=d.list.find("img:last")}f.trigger("click")},playClick:function(f){var d=f.data.self;d.options.autoPlay=!d.options.autoPlay;d.imgPlay.toggleClass("play").toggleClass("pause");if(d.options.autoPlay){d.nextClick()}},Next:function(){var d={data:{self:this}};this.nextClick(d)},Play:function(){if(this.options.autoPlay){return}var d={data:{self:this}};this.playClick(d)},Pause:function(){if(!this.options.autoPlay){return}var d={data:{self:this}};this.playClick(d)},updateThumbs:function(){var d=this;this.thumbs=this.list.find("img");this.thumbs.each(function(){d.createThumb(b(this),d)})}})})(jQuery);