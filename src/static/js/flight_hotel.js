const flightHotelSelector = function(){
    return {
        config: {
            //Static element
            selectorWrapper: $(),
            airportSelector: $(),
            flightHotelToggleSwitch: $(),

            //Dynamic data
            airportOptions: [],
            airportOptionsJSON: "",
        },
        init: function () {
            this.initStaticElements();
            this.initSelect2();
            this.prepareListeners();
        },
        initStaticElements: function () {
            this.config.selectorWrapper = $('.flight_hotel_selector_wrapper');
            this.config.flightHotelToggleSwitch = $('.flight_hotel_switch_wrapper input[type="checkbox"]');
            this.config.airportSelector = $('#airport_selector');
        },
        initSelect2: function () {
            this.config.airportOptions = JSON.parse(this.config.airportOptionsJSON);
            this.config.airportOptions.unshift({
                id: "",
                text: "",
                disabled: true,
                selected: true
            });

            const normalizeString = (str) => {
                return str.toUpperCase().normalize("NFD").replace(/\p{Diacritic}/gu, "");
            };

            $.fn.select2.amd.require(["select2/data/array", "select2/utils"], function (ArrayDataAdapter, Utils) {
                function CustomDataAdapter($element, options) {
                    this.options = options;
                    CustomDataAdapter.__super__.constructor.call(this, $element, options);
                }
                Utils.Extend(CustomDataAdapter, ArrayDataAdapter);

                CustomDataAdapter.prototype.filterOptions = function (options, params) {
                    let matcher = this.options.get('matcher'),
                        filteredResults = [];

                    for (let group of options) {
                        let matched_group = matcher(params, group);
                        if (matched_group !== null) {
                            filteredResults.push(matched_group);
                        }
                    }

                    return filteredResults;
                };

                /**
                 * Custom query function to load select options from custom source and to handle pagination.
                 * After filtering the options, the results are paginated based on the page size.
                 * The pagination is based on the number of children options, not the groups.
                 * Then, the results are passed to the callback function.
                 *
                 * @param {object} params - The search term and pagination parameters
                 * @param {string} params.term - The search term used to filter the options
                 * @param {number} params.page - The current page number
                 * @param {function} callback - Callback function to be called with the results
                 */
                CustomDataAdapter.prototype.query = function (params, callback) {
                    if (!("page" in params)) {
                        params.page = 1;
                    }

                    const pageSize = 100;
                    const filteredResults = this.filterOptions(flightHotelSelector.config.airportOptions, params);
                    const startIndex = (params.page - 1) * pageSize;
                    const endIndex = params.page * pageSize;

                    const paginatedResults = [];
                    let optionCounter = 0;
                    let optionsToSkip = startIndex;
                    let optionsToTake = pageSize;

                    // Loop through all groups
                    for (let i = 0; i < filteredResults.length; i++) {
                        const group = filteredResults[i];
                        let groupChildren = group.children || [];

                        // Calculate how many children to take from this group
                        // Page boundaries are handled by the slice method
                        const childrenToInclude = groupChildren.slice(optionsToSkip, optionsToSkip + optionsToTake);
                        const childrenCount = childrenToInclude.length;

                        if (childrenCount > 0) {
                            paginatedResults.push({...group, children: childrenToInclude});
                            optionsToTake -= childrenCount;
                        }

                        optionCounter += childrenCount;
                        optionsToSkip = Math.max(0, optionsToSkip - childrenCount);

                        if (optionsToTake <= 0) {
                            break;
                        }
                    }

                    // Calculate total options across all groups to determine if there are more pages
                    let totalOptions = 0;
                    for (let group of filteredResults) {
                        totalOptions += (group.children || []).length;
                    }

                    // Set the results and pagination info
                    let data = {};
                    data.results = paginatedResults;
                    data.pagination = {};
                    data.pagination.more = endIndex < totalOptions;
                    callback(data);
                };

                /**
                 * Custom matcher to filter the options based on the search term.
                 * The matcher is called for each group in the dropdown.
                 * The search is case-insensitive and ignores diacritics.
                 * If the group text matches the search term, the entire group is shown.
                 * Otherwise, the children of the group are filtered based on the search term.
                 * @param params {object} - The search term
                 * @param groupData {object} - The group to be filtered. Each group contains a children array with the options
                 * @returns {object|null} - The group to be displayed (modified if needed) or null if the group should not be displayed
                 */
                const customMatcher = (params, groupData) => {
                    // If there are no search terms, return all the data
                    if (!params.term?.trim()) {
                        return groupData;
                    }

                    // Normalize the search term to compare with the data
                    const searchTerm = normalizeString(params.term);

                    if (normalizeString(groupData.text).indexOf(searchTerm) > -1) {
                        // Show the entire group if the group text matches the search term
                        return groupData;
                    } else if (groupData.children) {
                        // Filter the children based on the search term
                        const filteredChildren = groupData.children.filter((child) => {
                            return normalizeString(child.text).indexOf(searchTerm) > -1;
                        });
                        if (filteredChildren.length) {
                            const modifiedData = {...groupData};
                            modifiedData.children = filteredChildren;
                            return modifiedData;
                        }
                    }

                    // Return `null` if the group should not be displayed
                    return null;
                };

                flightHotelSelector.config.airportSelector.select2({
                    width: '100%',
                    data: flightHotelSelector.config.airportOptions,
                    ajax: {},  // Required for pagination
                    dataAdapter: CustomDataAdapter,  // Needed to load select options from custom source and to implement pagination
                    //resultsAdapter: CustomResultsAdapter,  // This adapter could potentially fix the issue with repeated group titles on between pages, but the pagination stops working when it is provided
                    matcher: customMatcher  // Needed for personalized search
                });
            });

        },
        prepareListeners: function () {
            this.config.flightHotelToggleSwitch.on('change', function () {
                const isChecked = $(this).is(':checked');
                if (isChecked) {
                    flightHotelSelector.config.selectorWrapper.fadeIn();
                } else {
                    flightHotelSelector.config.selectorWrapper.fadeOut();
                    flightHotelSelector.config.airportSelector.val('').trigger('change');
                }
            });
        }
    };
}();