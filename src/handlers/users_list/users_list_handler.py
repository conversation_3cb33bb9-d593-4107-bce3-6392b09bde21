# -*- coding: utf-8 -*-
import logging
from copy import deepcopy

from flask import make_response
from flask.views import MethodView

from paraty.constants.advanced_configs import AGE_SELECTION_CONFIG_PROPERTY, AGE_SELECTION_WEB_ONLY_CONFIG_PROPERTY, \
    LANGUAGE_SELECTION, CALLCENTER_COUNTRY_LIST, RANGO_EDADES_BABYS, RANGO_EDADES_KIDS, ONLY_ADULTS, \
    CALLCENTER_PROMOCODE_SELECTOR, VALID_ROOMS_OPTIONS, CALLCENTER_NUM_ROOMS_SELECTOR, HIDE_COMMENTS_CALLSEEKER, \
    CALLCENTER_IGNORE_MIN_STAY, CALLCENTER_IGNORE_DISPO, NOT_SHOW_CHECKBOX_MOBILE_OFFERS, CALLSEEKER_PRERESERVATION, \
    MEMBERS_CLUB, MEMBERCLUB_NAMESPACE, HIDE_PAYMENT_FOR_CALLSEKER, ALLOW_PETS, INCLUDE_BABIES, \
    SHOW_BABIES_AGES_SELECTOR, <PERSON>OMINIO_BOOKING, CUSTOM_PATH_BOOKING, BOOK_YESTERDAY, BREADCRUMBS_CALLSEEKER, \
    BOOKING_SPA, DOMINIO_ASOCIADO, NO_AVAILABILITY_REDIRECT, CALLCENTER_COUNTRY_INDEX
from paraty.constants.section_types import INDIVIDUAL_HOTEL, INDIVIDUAL_ROOM
from paraty.constants.web_configs import PREBOOKING, ZENDESK, CALLCENTER_HOTEL_GROUPS
from paraty.tools.templates_methods import build_template_jinja
from paraty.utils import hotel_manager_utils
from paraty.utils.constants import COUNTRIES
from paraty.utils.session import session_utils, session_manager

import json
from flask import request, redirect, make_response

from paraty.utils.hotel_manager_utils import get_config_property_value, getWebConfiguration, getPromocodes, \
    get_all_hotels_call_seeker
from paraty.utils.languages.language_utils import get_web_dictionary
from paraty.utils.session.session_utils import USER_MASTERED_USERS
from paraty_commons_3.bigquery.bigquery_communicator import execute_query
from paraty_commons_3.common_data.common_data_provider import get_all_websections, _set_language_web_page_properties, \
    get_hotel_advance_config_value
from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_users, get_account_manager
from paraty_commons_3.language_utils import SPANISH

PARAM_ID = "id"

LANGUAGES_TRANSLATE = {"SPANISH": u'Español', "FRENCH": u'Francés', "ENGLISH": u'English', "DUTCH": u'Holandés',
                       "POLISH": u'Polaco', "FINNISH": u'Finlandés', "SWEDISH": u'Sueco', "PORTUGUESE": u'Português',
                       "GERMAN": u'Alemán', 'ITALIAN': u'Italiano', 'RUSSIAN': u'Ruso', 'SUOMI': u'Suomi',
                       'CATALAN': u'Catalán'}


class UserListHandler(MethodView):

    def get(self, hotel_id):

        hotel = hotel_manager_utils.get_hotel_by_id(hotel_id)
        user_session = session_utils.get_session_user()
        translations = get_web_dictionary()

        internal_url = hotel_manager_utils.build_url(hotel['applicationId'], hotel['url'])

        hotel_url = get_config_property_value(hotel, DOMINIO_BOOKING)

        if not hotel_url or get_config_property_value(hotel, CUSTOM_PATH_BOOKING):
            hotel_url = internal_url



        context = {
            'hotel_id':hotel_id,
            'booking_url': hotel_url + "/booking1",
            'agent_id': user_session['name'],
            'hotel_url': hotel_url,
            'internal_url': internal_url,
            'namespace': hotel['applicationId'],
            'booking_spa': get_config_property_value(hotel, BOOKING_SPA),
            'breadcrumbs_enabled': get_config_property_value(hotel, BREADCRUMBS_CALLSEEKER),
            'book_yesterday': get_config_property_value(hotel, BOOK_YESTERDAY),
            'related_hotels': get_config_property_value(hotel, NO_AVAILABILITY_REDIRECT),
            'hotel_groups': self.get_hotel_groups(hotel, SPANISH)
        }

        if context.get("booking_spa"):
            context['show_mobile_offers'] = True
            context['hide_comments'] = True

            hotel_url = get_config_property_value(hotel, DOMINIO_BOOKING)

            if not hotel_url:
                hotel_url = get_config_property_value(hotel, DOMINIO_ASOCIADO)

            context['booking_url'] = f"{hotel_url}/es/"

        context.update(translations)
        content = build_template_jinja('templates/users_list', 'users_list.html', context)

        response = make_response(content, 200)
        response.headers['Content-Type'] = 'text/html'

        return response


    def get_hotels(self, hotel, language):
        hotel_code = hotel['applicationId']
        all_sections = deepcopy(get_all_websections(hotel_code))
        hotels = [x for x in all_sections if x.get('sectionType') == INDIVIDUAL_HOTEL and x.get('enabled')]

        if not hotels:
            hotels = [x for x in all_sections if x.get('sectionType') == INDIVIDUAL_ROOM and x.get('enabled')]

        for hotel_section in hotels:
            _set_language_web_page_properties(hotel_code, hotel_section, language, [])

        hotels = [x for x in hotels if x.get('namespace')]

        return hotels

    def get_hotel_groups(self, hotel, language):
        hotels = self.get_hotels(hotel, language)
        groups = {}

        for hotel_section in hotels:
            hotel_namespace = hotel_section.get('namespace')
            if hotel_namespace:
                groups.setdefault('all', []).append(hotel_namespace)

                if hotel_section.get('destiny'):
                    groups.setdefault('destinations', {}).setdefault(hotel_section.get('destiny'), []).append(
                        hotel_namespace)

                if hotel_section.get('country'):
                    groups.setdefault('countries', {}).setdefault(hotel_section.get('country'), []).append(
                        hotel_namespace)

        groups_config = getWebConfiguration(hotel, CALLCENTER_HOTEL_GROUPS)
        if groups_config:
            for group_name, group_hotels in groups_config.items():
                groups.setdefault('custom', {})[group_name] = group_hotels.split(';')

        # Sort by name
        for key in ['destinations', 'countries', 'custom']:
            if groups.get(key):
                groups[key] = dict(sorted(groups[key].items()))

        return groups

    def get_users_list(self, session, hotel_id, start_date, end_date):

        hotel_code_entities = datastore_communicator.get_using_entity_and_params('HotelInfo',
                                                                                 search_params=[
                                                                                     ('hotel_key', '=', hotel_id)])
        hotel_code = hotel_code_entities[0].get("applicationId")

        admins = get_hotel_advance_config_value(hotel_code=hotel_code, main_key="Admin_Call_Center")
        if admins:
            admins = admins.split(";")
        else:
            print("The Admin Call_Center configuration does not exist or is incorrectly configured")
            admins = []


        if session.get("name") in admins:
            reservations = list(
                datastore_communicator.get_using_entity_and_params('Reservation', [('agent', '>', None),
                                                                                   ("timestamp", '>', start_date),
                                                                                   ("timestamp", '<', end_date)],
                                                                   hotel_code=hotel_code))
        else:
            reservations = list(
                datastore_communicator.get_using_entity_and_params('Reservation', [('agent', '>', session.get("name")),
                                                                                   ("timestamp", '>', start_date),
                                                                                   ("timestamp", '<', end_date)],
                                                                   hotel_code=hotel_code))


        data = [{
            "agent": reservation.get('agent'),
            "datetime": reservation.get('timestamp').split(" ")[0]
        } for reservation in reservations]

        count_data = {}
        for datum in data:
            if datum.get("agent") in count_data:
                if count_data[datum.get("agent")].get(datum.get("datetime")):
                    count_data[datum.get("agent")][datum.get("datetime")] += 1
                else:
                    count_data[datum.get("agent")].update({datum.get("datetime"): 1})
            else:
                count_data[datum.get("agent")] = {datum.get("datetime"): 1}

        list_data = []
        for key, value in count_data.items():
            for key_value, value_number in value.items():
                if("agente" in key):
                    list_data.append({"agent": key, "datetime": key_value, "count": value_number})



        return list_data, hotel_code

    def post(self, start_date, end_date):
        hotel_id = request.referrer.split("/")[-1]
        session = session_utils.get_session_user()
        users_list, hotel_code = self.get_users_list(session=session, hotel_id=hotel_id, start_date=start_date, end_date=end_date)
        return {"data": json.dumps(users_list), "hotel_code": hotel_code}


def get_user_info(start_date, end_date, user):
    hotel_id = request.referrer.split("/")[-1]
    hotel_code_entities = datastore_communicator.get_using_entity_and_params('HotelInfo',
                                                                             search_params=[
                                                                                 ('hotel_key', '=', hotel_id)])
    hotel_code = hotel_code_entities[0].get("applicationId")

    reservations = list(
        datastore_communicator.get_using_entity_and_params('Reservation', [('agent', '=', user),
                                                                           ("timestamp", '>', start_date),
                                                                           ("timestamp", '<', end_date)],
                                                           hotel_code=hotel_code))
    data= []
    for reservation in reservations:
        data.append(reservation.get('timestamp').split(" ")[0])

    graphic_data = {}
    for datum in data:
        if datum in graphic_data:
            graphic_data[datum] += 1
        else:
            graphic_data[datum] = 1


    return graphic_data

def get_general_list_info(start_date, end_date):
    query = f'''
    SELECT
      agent, partitionTimestamp, count(identifier) as reservas, sum(price+priceSupplements) as revenue
    FROM
      `analysis-seeker.bi_dataset.MVIEW_DATOS_RESERVAS_6`
    WHERE
      TIMESTAMP_TRUNC(partitionTimestamp, DAY) >= TIMESTAMP("{start_date}") and TIMESTAMP_TRUNC(partitionTimestamp, DAY) <= TIMESTAMP("{end_date}") and 
      (agent like 'agente%' and agent not like '%-nau%' and agent not like '%landmar%' and agent not like '%oasis%' and agent not like '%qhotels%' and agent not like '%_ona%')
      and source_fixed like '%Callcenter%' and cancelled = False
    group by agent, partitionTimestamp
'''
    data = execute_query(query=query)

    list_data = [{
        "agent": datum.get('agent'),
        "datetime": datum.get("partitionTimestamp").strftime('%Y/%m/%d'),
        "reservas": datum.get('reservas')
    } for datum in data]

    return {"data": json.dumps(list_data)}

def get_general_graph_info(start_date, end_date):
    query = f'''
        SELECT
          agent, partitionTimestamp, count(identifier) as reservas, sum(price+priceSupplements) as revenue
        FROM
          `analysis-seeker.bi_dataset.MVIEW_DATOS_RESERVAS_6`
        WHERE
          TIMESTAMP_TRUNC(partitionTimestamp, DAY) >= TIMESTAMP("{start_date}") and TIMESTAMP_TRUNC(partitionTimestamp, DAY) <= TIMESTAMP("{end_date}") and 
          (agent like 'agente%' and agent not like '%-nau%' and agent not like '%landmar%' and agent not like '%oasis%' and agent not like '%qhotels%' and agent not like '%_ona%')
          and source_fixed like '%Callcenter%' and cancelled = False
        group by agent, partitionTimestamp
    '''
    data = execute_query(query=query)

    list_data = [{
        "agent": datum.get('agent'),
        "datetime": datum.get("partitionTimestamp").strftime('%Y/%m/%d'),
        "reservas": datum.get('reservas')
    } for datum in data]

    return {"data": json.dumps(list_data)}


def get_mastered_users(current_user):
    current_user_name = current_user.get('name')
    all_users = datastore_communicator.get_using_entity_and_params('ParatyUser', hotel_code='admin-hotel')
    if not all_users:
        logging.error('Something went wrong trying to get all users')
        return

    available_users = [current_user_name]
    for user in all_users:
        if not user.get('configurationMap'):
            continue
        user_configs = dict(map(lambda x: x.split(" @@ "), user['configurationMap']))
        if user_configs.get('call_seeker_master') and current_user_name in user_configs['call_seeker_master'].split(';'):
            available_users.append(user.get('name'))

    session_manager.set(USER_MASTERED_USERS, available_users)

    return available_users

