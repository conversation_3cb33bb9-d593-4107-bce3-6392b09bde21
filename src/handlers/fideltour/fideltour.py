# -*- coding: utf-8 -*-
import logging

import requests
from flask import request
from flask.views import MethodView


class Fideltour_call(MethodView):

    def get(self):
        self.post()
    def post(self):
        hotel_code = request.args.get("hotel_code")
        email = request.args.get("email")
        logging.info(f"call pms-seeker fideltour hotel_code:{hotel_code} email: {email}")
        response = requests.post(f"https://pms-seeker.ew.r.appspot.com/fideltour_v2/get_user_info?hotel_code={hotel_code}")
        return response.json()