import logging

from flask import request
from flask.views import MethodView
from google.cloud import ndb

from paraty_commons_3.datastore.datastore_utils import get_project_and_namespace
from paraty_commons_3.decorators.cache.distributed_strong_cache import DistributedCacheEntry
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_hotels_by_application
from paraty_commons_3.decorators.cache import cache_controller


class CacheCleaner(MethodView):
    def get(self):
        hotel_code = request.values.get("hotel_code")
        application_id, namespace = get_project_and_namespace(hotel_code)
        all_hotels = get_all_hotels_by_application(application_id)
        if all_hotels:
            for hotel in all_hotels:
                cache_controller.invalidate_cache(hotel_code)
                logging.info("Cache cleaned for hotel %s", hotel['applicationId'])

        return "OK"