# -*- coding: utf-8 -*-
import logging

from flask import request
from flask.views import MethodView
from google.appengine.ext.deferred import deferred

from paraty import Config
from paraty.tools import callUtils

import datetime

from model.call_model import Call

__author__ = 'jlvillada'

from paraty.tools.templates_methods import build_template_jinja
from paraty.tools.xml_utils import XmlFinder


class ReportCallHandler(MethodView):

    def post(self):

        # if 'gzip' in self.request.headers.get('User-Agent', '').lower():
        #     logging.info('User-Agent contains gzip. Request content will be decoded')
        #     self.request_body = self.request.body.decode("zlib")
        # else:
        #     self.request_body = self.request.body

        try:

            self.xml_finder = XmlFinder(request.data)

            calls = self.xml_finder.find_elements('call')

            logging.info('Calls processing: %s ' % str(len(calls)))

            for call_aux in calls:

                call_bd = Call()

                call_bd.src = call_aux.find('src').text
                call_bd.start = datetime.datetime.strptime(call_aux.find('start').text, '%Y-%m-%d %H:%M:%S')
                call_bd.duration = int(call_aux.find('duration').text)
                call_bd.billsec = int(call_aux.find('billsec').text)
                call_bd.status = str(call_aux.find('status').text)
                call_bd.client = str(call_aux.find('client').text)
                call_bd.dst = ''
                call_bd.agent = ''
                call_bd.agent_name = ''
                call_bd.fails = '[]'
                if call_aux.find('dst').text:
                    call_bd.dst = str(call_aux.find('dst').text)
                    call_bd.agent = str(call_aux.find('agent').text)
                    call_bd.agent_name = str(call_aux.find('agent_name').text)
                    call_bd.name = str(call_aux.find('name').text)

                result = []
                for fail_aux in call_aux.findChildren('fail'):
                    # logging.info("FAIL %s " % str(fail_aux))
                    # logging.info("DST %s " % str(fail_aux.find('dst').text))
                    # logging.info("STATUS %s " % str(fail_aux.find('status').text))
                    fail = {'dst': str(fail_aux.find('fail_dst').text), 'status': str(fail_aux.find('fail_status').text), 'agent': str(fail_aux.find('fail_agent').text)}
                    result.append(fail)
                    call_bd.fails = str(result)

                call_bd.put()

            logging.info('Saved in datastore sucessfull')

        except Exception as e:
            logging.error("Error doing push call: %s" % e)


    def get(self, operation):

        # try:

        if request.values.get('date', ''):
            date = request.values.get('date', '')

        else:
            today = datetime.date.today()
            date_aux = today - datetime.timedelta(days=1)
            date = date_aux.strftime("%Y-%m-%d")

        logging.info('Search day %s...' % date)

        if Config.DEV:
            _do_operation(operation, date)
        else:
            deferred.defer(_do_operation, operation, date)


def _do_operation(operation, date):

        GET_REPORTS_AVAILABLES = {
            'daily': {'data': callUtils.get_calls_report_daily,
                      'template': 'report_calls_daily.html',
                      'address': '<EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>',
                      # 'address': '<EMAIL>',
                      'title': '[paratybackend] [CallSeeker] Resumen diario de llamadas por agente'}
        }

        report = GET_REPORTS_AVAILABLES.get(operation)
        result = report['data'](date)

        if result:

            template_path = report['template']

            contentHtml = build_template_jinja('templates/calls', template_path, result)

            logging.info('Mails sending... ')
            sendEmailAttachment(address=report['address'], title=report['title'], contentText='', contentHtml=contentHtml, sender='<EMAIL>')

        # except Exception, e:
        #     logging.warning("Error report call " + str(operation))
        #     logging.warning(e)

