import datetime
import json

from flask import request
from flask.views import MethodView

from model.observations_model import ParatyObservations
from paraty.utils.session.session_utils import get_session_user
from paraty.utils.users_utils import is_ring2travel_user


class ParatyObservationsHandler(MethodView):
    def get(self):
        if not self._validate_user():
            return "Unauthorized", 401

        hotel_code = request.values.get("hotel_code")
        hotel_observations = ParatyObservations.query(ParatyObservations.hotel_code == hotel_code).fetch()
        observations = sorted(hotel_observations, key=lambda x: x.modification_timestamp, reverse=True)
        serialized_data = [obs.to_dict() for obs in observations]

        return json.dumps(serialized_data), 200

    def put(self):
        if not self._validate_user():
            return "Unauthorized", 401

        actual_user = get_session_user()

        new_observation = ParatyObservations()
        new_observation.hotel_code = request.values.get("hotel_code")
        new_observation.observation = request.values.get("observation")
        new_observation.user = actual_user['name']
        new_observation.put()

        return '', 200

    def delete(self):
        if not self._validate_user():
            return "Unauthorized", 401

        observation_id = request.values.get("observation_id")
        observation = ParatyObservations.get_by_id(int(observation_id))
        observation.key.delete()

        return '', 200


    def post(self):
        if not self._validate_user():
            return "Unauthorized", 401

        observation_id = request.values.get("observation_id")
        observation = ParatyObservations.get_by_id(observation_id)
        observation.observation = request.values.get("observation")
        observation.modification_timestamp = datetime.datetime.now()
        observation.put()

        return '', 200


    def _validate_user(self):
        if is_ring2travel_user():
            return True