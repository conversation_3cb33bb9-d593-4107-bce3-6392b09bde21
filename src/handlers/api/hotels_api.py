# -*- coding: utf-8 -*-
import json
import logging

from flask import make_response, request

from handlers.api.base_api import BaseAPIHandler
from model import info_model
from model.info_model import load_xls_info, HotelInfo
from model.valid_session_model import ValidSession
from paraty.utils.session import session_utils, session_manager
from paraty.utils import hotel_manager_utils
from paraty.utils.logindecorator import login

class HotelApi(BaseAPIHandler):

	@login
	def get(self, operation, hotel_id):

		GET_OPERATIONS_AVAILABLES = {
			'info': self.__get_hotel_info,
			'addExcel': self.__add_hotel_excel
		}

		executor = GET_OPERATIONS_AVAILABLES.get(operation)
		if not executor:
			make_response('', '400')
			return

		return self.render_json(executor(hotel_id))


	def post(self, operation, hotel_id):
		POST_OPERATIONS_AVAILABLES = {
			'info': self.__update_hotel_info
		}

		executor = POST_OPERATIONS_AVAILABLES.get(operation)
		if not executor:
			make_response('', '400')
			return

		return self.render_json(executor(hotel_id))

	def __build_booking_widget_url(self, hotel_id):
		return '/booking-widget/%s' % hotel_id

	def __build_users_list_widget_url(self, hotel_id):
		return '/users-list/%s' % hotel_id

	def _get_domain_to_use(self, hotel):

		#Some hotels need to go to a different page
		if 'port-' in hotel['url']:
			return 'http://port-corpo-dot-porthotels.appspot.com'

		return hotel_manager_utils.getRealDomain(hotel)

	def _get_url_to_reservation(self, hotel):
		return hotel_manager_utils.getUrlReservation(hotel)

	def _get_url_to_offers(self, hotel):
		return hotel_manager_utils.getUrlOffers(hotel)

	def __get_hotel_info(self, hotel_id):
		hotel = hotel_manager_utils.get_hotel_by_id(hotel_id)
		user_session = session_utils.get_session_user()
		user_name = user_session["name"]
		current_sid = session_manager.get_session_id()
		valid_session = ValidSession()
		results = {
			'iframeSrc': self.__build_booking_widget_url(hotel_id),
			'webIframe': self._get_domain_to_use(hotel),
			'webIframeReservation': self._get_url_to_reservation(hotel),
			'webIframeOffers': self._get_url_to_offers(hotel),
			'hotelCode': hotel['applicationId']
		}

		configuration_map = user_session.get("configurationMap", {})
		check_valid_session = False
		if configuration_map.get("item"):
			for config in configuration_map["item"]:
				config_map = config.split(" @@ ")
				if config_map[0].lower() == "limit login number" and config_map[1].lower() == "true":
					check_valid_session = True


		if check_valid_session:
			if valid_session.get_by_id(user_name) and valid_session.get_by_id(user_name).session_id != current_sid:
					logging.info("Invalid session: the same session is active in other device!")
					results['sessionError'] = "Sesión inválida! Tiene abierta la sesión en otro dispositivo!"

		hotel_xls = info_model.get_info_by_hotel(hotel)
		if hotel_xls:
			results['xlsUrl'] = hotel_xls

		return results

	def __add_hotel_excel(self, hotel_id):
		hotel = hotel_manager_utils.get_hotel_by_id(hotel_id)
		hotel_info = HotelInfo.query(HotelInfo.hotel_key == hotel['key']).get()

		if not hotel_info:
			hotel_info = HotelInfo()
			hotel_info.applicationId = hotel['applicationId']
			hotel_info.hotel_key = hotel['key']
		hotel_info.drive_xls_url = request.values.get('xls')
		hotel_info.put()
		return self.__get_hotel_info(hotel_id)

	def __update_hotel_info(self, hotel_id):
		hotel = hotel_manager_utils.get_hotel_by_id(hotel_id)
		content = json.loads(request.data)
		hotel_info = info_model.get_info_by_hotel(hotel)
		hotel_info.content = content['content']
		hotel_info.put()
		return {}


class LoadInventory(BaseAPIHandler):

	def post(self):
		data = json.loads(request.data)
		load_xls_info(data)