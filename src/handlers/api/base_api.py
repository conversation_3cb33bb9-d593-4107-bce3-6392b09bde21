import base64
import json
import logging

from flask import make_response, request
from flask.views import MethodView
from werkzeug.utils import cached_property

log = logging.getLogger(__name__)


class BaseAPIHandler(MethodView):
	"""BaseHandler which will be inherited all other handlers
	it should implement the most common functionality
	required by all handlers
	"""

	#@webapp2.cached_property
	#def mako(self):
		#pass

	#def render_response(self, _template, **context):
		#rv = self.mako.render_template(_template, **context)
		#self.response.write(rv)

	def render_json(self, obj):
		rv = json.dumps(obj)
		response = make_response(rv, '200')
		response.headers['Content-Type'] = 'application/json'

		return response


	def get_basic_auth_data(self):
		basic_auth = request.headers.get('Authorization')
		if not basic_auth:
			logging.error("Request does not carry auth.")
			make_response('', '403')
			return

		user_info = base64.b64decode(basic_auth[6:].encode('utf-8'))
		username, password = user_info.decode('utf-8').split(':')
		return username, password
