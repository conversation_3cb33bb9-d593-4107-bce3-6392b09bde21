from flask import request, make_response
from flask.views import MethodView

from paraty.utils.session import session_manager
from paraty.tools.templates_methods import build_template_jinja
from paraty.utils.languages.language_utils import get_web_dictionary
from paraty_commons_3.language_utils import get_language_title


class UtilsHandler(MethodView):
    def get(self):
        return self.build_response()

    def post(self):
        return self.build_response()

    def build_response(self):
        action = request.values.get("action")

        if action == 'set_session_language':
            target_language = request.values.get("language")

            if get_language_title(target_language):
                session_manager.set('selected_language', target_language)
                return make_response('ok', '204')
            else:
                return make_response('ko', '500')

        elif action == 'get_template':
            target_template = request.values.get("template")

            context = {}
            result = ''

            translations = get_web_dictionary()
            if translations:
                context.update(translations)

            if session_manager.get('update_hotel_info'):
                context['update_hotel_info'] = True

            if target_template == 'hotel_info':
                result = build_template_jinja('templates/hotel_info', 'hotel_info_tab.html', context)

            if result:
                response = make_response(result, '200')
                response.headers['Content-Type'] = 'text/html'

                return response
            else:
                return make_response(result, '404')
