import logging

import requests

from paraty.constants.web_configs import FLIGHT_HOTEL
from paraty.utils.hotel_manager_utils import getWebConfiguration
from paraty.utils.languages.language_utils import get_session_language
from paraty_commons_3.language_utils import get_language_code


DEFAULT_HOTEL_URL = 'https://www.puentereal.com'
AIRPORTS_ENDPOINT = 'flight_hotel/airports'


def is_flight_hotel_active(hotel: str) -> bool:
    """
    Check if the flight hotel feature is active.

    :param hotel: The hotel for which to check the flight hotel feature.
    :return: True if the flight hotel feature is active, False otherwise.
    """

    flight_hotel_config = getWebConfiguration(hotel, FLIGHT_HOTEL)
    return bool(flight_hotel_config and flight_hotel_config.get('active'))

def get_airports_data() -> list:
    """
    Fetch the list of airports from hotel-webs (using Puentereal url).

    :return: A list of airports grouped for selectric.
    """
    language_code = get_language_code(get_session_language())
    url = f"{DEFAULT_HOTEL_URL}/{AIRPORTS_ENDPOINT}?language_code={language_code}"
    try:
        response = requests.get(url, timeout=20)
        return response.json()
    except:
        logging.error(f"Error fetching airports data from {url}")
        return []