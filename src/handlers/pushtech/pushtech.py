# -*- coding: utf-8 -*-
import logging

import requests
from flask import request
from flask.views import MethodView


class Pushtech_call(MethodView):

    def post(self):
        hotel_code = request.args.get("hotel_code")
        json_data = request.json
        logging.info(f"call paratytech-adapter pushtech hotel_code:{hotel_code} json_data={json_data}")
        response = requests.post(f"https://paratytech-adapter.appspot.com/pushtech/get_contact?hotel_code={hotel_code}",json=json_data)
        return response.json()