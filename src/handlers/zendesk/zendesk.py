import requests
from flask import request

from paraty import app
from paraty.utils.booking_utils import get_selected_options
from paraty.utils.hotel_manager_utils import get_hotel_name_from_email_sender
from paraty.utils.session import session_manager, session_utils


@app.route("/zendesk/tickets", methods=['POST'])
def send_budget():
	params = request.get_json()
	ticket_id = params.get("id_ticket")
	email = params.get("email")
	whatsapp = params.get("whatsapp")
	session_id = params.get("session_id")
	hotel_code = params.get("hotel_code")
	user_session = session_utils.get_session_user()

	booking_summary = get_selected_options(hotel_code, session_id)

	params_to_send = {
		'sessionKey': session_manager.get('session_id'),
		'agent_id': user_session['name'],
		'hotel_code': hotel_code,
		'hotel_name': get_hotel_name_from_email_sender(hotel_code),
		'whatsapp': whatsapp,
		**booking_summary
	}

	if ticket_id:
		params_to_send['ticket_id'] = ticket_id

	if email:
		params_to_send['email'] = email

	send_budget_url = 'https://zendesk-adapter.appspot.com/callcenter/send_budget'
	response = requests.post(send_budget_url, json=params_to_send)
	response.raise_for_status()

	return response.text, 200
