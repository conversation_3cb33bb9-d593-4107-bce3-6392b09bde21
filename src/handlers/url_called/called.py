# -*- coding: utf-8 -*-
import logging

import requests
from flask import request
from flask.views import MethodView
from urllib.parse import unquote
from paraty import Config

class Master_Called(MethodView):

    def get(self, url):
        data = self.extract_data(url)
        hotel_code =data.get("hotel_code", "unknown")
        if not hotel_code:
            hotel_code = request.json.get("hotel_code", "unknown")
        logging.info(f"call url {url} with hotel_code:{hotel_code} form call-seeker")
        response = requests.get(f"{url}", data=request.data, json=request.json)
        return response.text
    def post(self,url):
        data = self.extract_data(url)
        hotel_code =data.get("hotel_code")
        if not hotel_code:
            hotel_code = request.json.get("hotel_code", "unknown")
        logging.info(f"call url {url} with hotel_code:{hotel_code} form call-seeker")
        logging.info(f"call url {url} with params json: {request.json} with params data:{request.data}")
        response = requests.post(f"{url}", data=request.data, json=request.json)
        return response.text

    def extract_data(self, url):
        try:
            params_args = url.split("?")
            if len(params_args) > 1:
                data = params_args[1].split("=")
                result = {data[i]: data[i + 1] for i in range(0, len(data), 2)}
                return result
            return {}
        except Exception as e:
            logging.info(f"error to extract data from url {url}")
            logging.info(f"{e}")
            return {}
