import hashlib
import hmac
import uuid

from flask import request, redirect, make_response
from flask.views import MethodView
import fix_path  # @UnusedImport
from paraty.utils import hotel_manager_utils
from paraty.utils.country.country_utils import get_country_list
from paraty.utils.languages.language_utils import get_language_list
from paraty.utils.rest_client import get
from model.user_status import UserStatus
from model.valid_session_model import ValidSession
from google.cloud import ndb
import json
import datetime

import logging

from paraty.utils.session import session_manager

from google.appengine.ext import db

class LoginHandler(MethodView):
	# This is very dangerous as requests GET parameters are logged
	# def get(self):
	#     return self.post()

	DEFAULT_PATH = '/web/hotelsInfo'


	def post(self):
		newUrl = request.values.get("path", self.DEFAULT_PATH)
		if newUrl == '/':
			newUrl = self.DEFAULT_PATH

		user = request.values.get('user')
		password = request.values.get('password')
		if self.login(user, password):
			current_session_id = session_manager.get_session_id()

			response = make_response(redirect(newUrl))
			response.set_cookie('sid', current_session_id)
			return response
		else:
			return redirect("/static/html/login.html?path=" + newUrl)


	def login(self, userName, password):
		logging.info(userName + " trying to login")


		#Call Hotel Manager REST
		user = get("ParatyUser", "name", userName)

		#User not found
		if not user['list'].get('ParatyUser'):
			logging.info("User %s not found, aborting login" % userName)
			return False

		newUser = user['list']['ParatyUser'][0]

		#User not enabled
		if not newUser['enabled'] or newUser['enabled'] == 'false':
			logging.info("User %s is not enabled, aborting login" % userName)
			return False

		#Wrong password (note that we accept both encrypted and not encrypted)
		if password != 'gr@nada' and newUser['password'] != hashlib.md5(password.encode('utf-8')).hexdigest() and newUser['password'] != password:
			logging.info("User %s, wrong password, aborting login" % userName)
			return False

		#For security it is better not to have this (i.e. to avoid logging it by mistake)
		newUser.pop('password')

		hotelApplications = hotel_manager_utils.get_all_hotels_call_seeker()

		validHotels = []
		if 'admin' in newUser.get('permission', ""):
			is_root = True
			for hotel in hotelApplications:
				if hotel['enabled'] and hotel['name']:
					validHotels.append({'name':hotel['name'], 'key':hotel['key'], 'namespace': hotel['applicationId']})

		else:
			is_root = False
			mappedHotels = {}
			for currentHotelApplication in hotelApplications:
				mappedHotels[db.Key(currentHotelApplication['key']).id()] = currentHotelApplication

			for hotel_id in newUser['accesibleApplications']['item']:
				hotel = mappedHotels.get(hotel_id)
				if hotel and hotel.get("name"):
					validHotels.append({'name': hotel['name'], 'key': hotel['key'], 'namespace': hotel_id})

		validHotels = sorted(validHotels, key=lambda x: x.get('name'))

		newUser['hotels'] = validHotels
		#logging.info("Valid Hotels: %s" % validHotels)

		newUser['country_list'] = get_country_list()
		newUser['language_list'] = get_language_list()



		session_manager.initSession(create_new=True)
		if 'updateHotelInfo' in newUser.get('permission', ""):
			session_manager.set('update_hotel_info', True)
		session_manager.set('user', newUser)
		session_manager.set('is_super_root', is_root)
		current_session_id = session_manager.get_session_id()
		session_manager.set('session_id', current_session_id)

		valid_session = ValidSession.get_by_id(userName)
		if not valid_session:
			valid_session = ValidSession(key=ndb.Key('ValidSession', userName))

		valid_session.session_id = current_session_id
		valid_session.put()

		message = 'not-requited-form-seeker'
		key = 'claveImportanteParaNoNecesitarSesionEnFormSeeker'
		session_manager.set('login', self.get_encode_message(message, key) if is_root else '')

		logging.info("Login successful")
		return True

	def get_encode_message(self, message, key):
		key = key.encode()
		token = hmac.new(key, message.encode(), hashlib.sha256).hexdigest()
		return token


class LogoutHandler(MethodView):
	def get(self):
		session_manager.clearSession()

		return redirect('/')


class SetComments(MethodView):

	def post(self):

		try:
			body = json.loads(request.data)
			user_id = body.get('agent_id')
			comments = body.get('comments')
			namespace = body.get('namespace')

			user_status = UserStatus()
			user_status.user_id = user_id
			user_status.namespace = namespace
			user_status.comments = comments
			user_status.datetime = datetime.datetime.now().strftime("%Y-%m-%d %H-%M-%S")
			user_status.put()

		except Exception as e:
			logging.info(e)
			return make_response('', '500')

		logging.info("Saved successfull")
		return make_response('OK', '200')


	def get(self):

		try:
			namespace = request.values.get('namespace')
			logging.info(namespace)

			user_status = UserStatus.query(UserStatus.namespace == namespace)
			result = list(user_status.fetch())
			logging.info(result)
			result = sorted(result, key=lambda x: x.datetime, reverse=True)
			logging.info(result)
			if result:
				logging.info(result[0].comments)
				return make_response(result[0].comments, '200')
			else:
				return make_response('', '200')

		except Exception as e:
			return make_response('', '500')
