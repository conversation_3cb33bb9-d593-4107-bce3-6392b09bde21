from handlers.vacation_club.vacation_club_models import VacationClubConfig
from paraty.constants.integrations_constants import VACATION_CLUB
from paraty_commons_3.common_data.data_management.integrations_utils import get_integration_configuration


def is_vacation_club_enabled(hotel: str) -> bool:
    """
    Check if the vacation club feature is enabled by configuration.

    :return: True if the feature is enabled and has configured all mandatory properties, False otherwise.
    """

    config = get_vacation_club_config(hotel)
    return bool(config and config.active)


def get_vacation_club_config(hotel: str) -> VacationClubConfig:
    """
    Get the vacation club configuration.

    :return: The vacation club configuration object.
    """
    config_dict = get_integration_configuration(VACATION_CLUB, hotel).get('configurations', {})
    return
