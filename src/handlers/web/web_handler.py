from flask import request, make_response
from flask.views import MethodView

from paraty.utils.logindecorator import login
from paraty.web_builder import page_factory


__author__ = 'fmatheis'

PARAM_ID = "id"

class MainWebHandler(MethodView):

	def get_identity_param(self):
		return request.values.get(PARAM_ID)

	@login
	def get(self, pageId="hotelsInfo"):
		content = page_factory.buildPage(pageId)
		return make_response(content, '200')
