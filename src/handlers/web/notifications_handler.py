import json

from flask import make_response

from handlers.api.base_api import BaseAPIHandler
from paraty.notifications import notifications_service
from paraty.utils.session import session_utils

__author__ = 'fmatheis'


class NotificationsAPIHandler(BaseAPIHandler):

	def get(self):
		user = session_utils.get_session_user()
		notifications = notifications_service.get_notifications_by_user(user)
		return self.render_json(notifications)